// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Create the intentnow database
db = db.getSiblingDB('intentnow');

// Create a user for the intentnow database
db.createUser({
  user: 'intentnow',
  pwd: 'intentnow123',
  roles: [
    {
      role: 'readWrite',
      db: 'intentnow'
    }
  ]
});

// Create initial collections with basic structure
db.createCollection('stores');
db.createCollection('users');
db.createCollection('widgets');

print('MongoDB initialization completed successfully');