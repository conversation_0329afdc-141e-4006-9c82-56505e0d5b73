{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "attach",
      "name": "Attach to server",
      "port": 9229,
      "restart": true,
      "sourceMapPathOverrides": {
        "webpack://@apps/*": "${workspaceFolder}/apps/*"
      }
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "Web (http-3000) in Chrome",
      "url": "http://localhost:3000/merchant",
      "webRoot": "${workspaceFolder}/apps/intentnow-web",
      "port": 9876
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "Shopify-Web (https-3001) in Chrome",
      "url": "https://admin.shopify.com/store/quickstart-1d36e8f9/apps/intentnow-dev-0",
      "webRoot": "${workspaceFolder}/apps/intentnow-web",
      "port": 9876
    }
  ]
}
