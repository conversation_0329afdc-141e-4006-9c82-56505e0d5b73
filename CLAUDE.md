# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the IntentNow monorepo containing:
- **intentnow-server**: NestJS backend with Shopify integration, Firebase Auth, Firestore, and MongoDB
- **intentnow-web**: Next.js frontend with both standalone and Shopify embedded apps
- **shared-entities**: Shared TypeScript data models and DTOs
- **intentnow-tag**: React widget library compiled to CDN script for Shopify storefronts

## Development Commands

### Setup
```bash
# Install dependencies
pnpm install

# Build shared packages (required before running apps)
pnpm --filter @packages/shared-entities build
pnpm --filter @packages/intentnow-tag build

# Or auto-rebuild packages on changes
pnpm --filter "@packages/*" --parallel build --watch
```

### Running Applications
```bash
# Run both server and web (standalone mode)
pnpm dev

# Run only web app with dependencies
pnpm dev:web

# Run only server with dependencies  
pnpm dev:server

# Run with Shopify embedded mode (requires HTTPS proxy)
pnpm shopify:dev
```

### Testing & Quality
```bash
# Run all tests
pnpm test

# Server-specific tests
cd apps/intentnow-server && pnpm test
cd apps/intentnow-server && pnpm test:e2e

# Lint all projects
pnpm lint

# Type checking
pnpm typecheck

# Format code
pnpm format
```

### Build
```bash
# Build all projects
pnpm build

# Build specific projects
pnpm --filter @apps/intentnow-server build
pnpm --filter @apps/intentnow-web build
```

## Architecture

### Server (NestJS)
- **Modules**: Shopify, IntentNow, Auth, Admin, Store
- **Authentication**: Firebase Admin SDK + Shopify OAuth
- **Databases**: MongoDB (primary), Firestore (session storage)
- **Caching**: Redis with memory fallback
- **Observability**: OpenTelemetry, Pino logging
- **APIs**: REST endpoints for both standalone and Shopify embedded usage

### Web (Next.js)
- **Multi-tenant**: Supports standalone web app + Shopify Admin embedded app
- **Auth**: Firebase client-side + Clerk integration
- **UI**: Material-UI with custom admin theme
- **Features**: JSONForms, Puck editor, widget builders
- **Routes**: 
  - `/admin/*` - Internal admin console
  - `/intentnow/*` - Shopify embedded app
  - `/auth/*` - Authentication flows

### Shared Packages
- **shared-entities**: Must be built before running apps (`pnpm --filter @packages/shared-entities build`)
- **intentnow-tag**: Widget library that compiles to CDN script, requires build step

## Environment Setup

### Server (.env in apps/intentnow-server)
- Copy from `.env.template`
- Required: `AUTH_ROBOT_TOKENS`, `AUTH_USER_EMAILS` 
- Optional: `REDIS_URL` (falls back to memory)
- GCP authentication needed for Firestore: `gcloud auth application-default login`

### Web (.env.local in apps/intentnow-web)  
- Copy from `.env.local.template`
- Configure for either standalone or Shopify embedded mode

### Shopify Development
- Uses test apps: intentnow-dev-0, intentnow-dev-1
- HTTPS proxy required: runs on localhost:3001 (web), localhost:4001 (server)
- Separate Shopify app repo handles extensions: intentnow-shopify-app

## Development Notes

### Package Dependencies
Always build shared packages before running apps. Watch mode recommended during development:
```bash
pnpm --filter "@packages/*" --parallel build --watch
```

### Debugging
VSCode debug configurations available:
- "attach to server" - Debug NestJS backend
- "Web" - Debug standalone Next.js app  
- "Shopify-Web" - Debug Shopify embedded app

### Testing
- Server uses Jest with custom test environment
- Use `pnpm test:watch` for development
- E2E tests available with `pnpm test:e2e`

### Shopify Integration
- Backend handles Shopify webhooks, GraphQL, and REST APIs
- Frontend uses Shopify App Bridge for embedded experience
- Widget deployment to CDN: `pnpm --filter @packages/intentnow-tag bundle-upload`