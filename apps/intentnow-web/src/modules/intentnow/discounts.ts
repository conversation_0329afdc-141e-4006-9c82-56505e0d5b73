import { useShopifyApp } from '../shopify/app'
import { shopifyApiFetch } from '../shopify/fetch'
import { useAuthedApiFetch } from '../auth/fetch'
import { usePagedData } from '../utils/paged-data-helper'
import { GeneratedDiscountsPageDto } from '@packages/shared-entities'

//TODO: implement a generic paged loader
export function useGeneratedDiscounts(pageSize = 20) {
  const shopifyApp = useShopifyApp()

  async function pagedFetcher(
    pageSize: number,
    startAfter?: string,
    endBefore?: string
  ) {
    if (!shopifyApp) {
      throw new Error()
    }

    let apiUrl = `/intentnow/generated-discounts?pageSize=${pageSize}`
    if (startAfter) {
      apiUrl += `&startAfter=${startAfter}`
    } else if (endBefore) {
      apiUrl += `&endBefore=${endBefore}`
    }

    const { data, pageInfo } =
      await shopifyApiFetch<GeneratedDiscountsPageDto>(shopifyApp)(apiUrl)

    return {
      data,
      pageInfo,
    }
  }

  return usePagedData(
    shopifyApp?.shop ? `useGenerateDiscounts-${shopifyApp.shop}` : undefined,
    pageSize,
    pagedFetcher
  )
}

//Admin only
export function useGeneratedDiscountsForShop(
  appHandle: string | undefined,
  shop: string | undefined,
  pageSize = 20
) {
  const { authedApiPostFetch } = useAuthedApiFetch()

  async function pagedFetcher(
    pageSize: number,
    startAfter?: string,
    endBefore?: string
  ) {
    if (!shop) {
      throw new Error()
    }

    let apiUrl = `/api/admin/intentnow/generated-discounts?pageSize=${pageSize}`
    if (startAfter) {
      apiUrl += `&startAfter=${startAfter}`
    } else if (endBefore) {
      apiUrl += `&endBefore=${endBefore}`
    }

    const { data, pageInfo } = await authedApiPostFetch<
      GeneratedDiscountsPageDto,
      {
        shop: string
        appHandle?: string
      }
    >([
      apiUrl,
      {
        shop,
        appHandle,
      },
    ])

    return {
      data,
      pageInfo,
    }
  }

  return usePagedData(
    shop ? `useGeneratedDiscountsForShop-${shop}` : undefined,
    pageSize,
    pagedFetcher
  )
}
