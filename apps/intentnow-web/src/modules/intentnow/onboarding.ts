import { useAuthedApiFetch } from '../auth/fetch'

//Admin only
export function useStoreOnboarding(shop: string | undefined) {
  const { authedApiPostFetch } = useAuthedApiFetch()

  async function generateAppConfigs(newShopifyApp: {
    appHandle: string
    appName: string
    apiKey: string
    secretKey: string
    promoEmbedId?: string
  }) {
    return await authedApiPostFetch<
      {
        tomlFile: string
        appConfigs: {
          serverConfigs: any[]
          webConfigs: any[]
        }
      },
      {
        newShopifyApp: {
          appHandle: string
          appName: string
          apiKey: string
          secretKey: string
          promoEmbedId?: string
        }
      }
    >([
      `/api/admin/intentnow/generate-app-configs`,
      {
        newShopifyApp,
      },
    ])
  }

  async function initStore(
    name: string,
    website: string,
    fromShop: string | undefined,
    appHandle: string
  ) {
    if (!shop) {
      throw new Error('Shop required')
    }

    return await authedApiPostFetch<
      {
        shop: string
        storeIdCreated: string | undefined
        experimentCreated: string | undefined
        dynamicConfigCreated: string | undefined
      },
      {
        appHandle: string
        shop: string
        name: string
        website: string
        fromShop: string | undefined
      }
    >([
      `/api/admin/intentnow/init-store`,
      {
        appHandle,
        shop,
        name,
        website,
        fromShop,
      },
    ])
  }

  function generateAppHandle() {
    if (!shop) {
      return ''
    }

    const shopPrefix = shop.split('.')[0]
    if (!shopPrefix) {
      return ''
    }

    const appHandleBuf: string[] = []
    for (let i = 0; i < shopPrefix.length; i++) {
      const ch = shopPrefix.charAt(i).toLowerCase()
      if (('0' <= ch && ch <= '9') || ('a' <= ch && ch <= 'z')) {
        appHandleBuf.push(ch)
      }
    }
    const appHandle: string | undefined = `intentnow-${appHandleBuf.join('')}`
    return appHandle
  }

  return {
    generateAppHandle,
    generateAppConfigs,
    initStore,
  }
}
