import { OfferDiscountCodeConfigDto } from '@packages/shared-entities'

export const storeCampaignStatusLabels: Record<string, string> = {
  active: 'Active',
  disabled: 'Disabled',
  notStarted: 'Not Started',
  ended: 'Ended',
  unknown: 'Unknown',
}

export const storeOfferStatusLabels: Record<string, string> = {
  draft: 'Draft',
  complete: 'Complete',
}

export function generatePreviewDiscount(
  discountConfig: OfferDiscountCodeConfigDto
) {
  const prefix = discountConfig.codeGen.prefix ?? ''
  return {
    title: discountConfig.discount.title,
    code: `${prefix}${discountConfig.codeGen.alphabet.slice(
      0,
      discountConfig.codeGen.length - prefix.length
    )}`,
    startsAt: new Date(),
    endsAt: new Date(),
  }
}
