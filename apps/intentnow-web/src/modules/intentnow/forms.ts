import { ShopifyStoreWidget } from '@packages/shared-entities'

export const shopifyStoreJsonSchema = {
  //JsonSchema for ShopifyStore
  type: 'object',
  required: ['shop'],
  properties: {
    name: {
      type: 'string',
    },
    shop: {
      type: 'string',
    },
    website: {
      type: 'string',
    },
    appHandle: {
      type: 'string',
    },
    pendingInstall: {
      type: 'boolean',
    },
    promoConfig: {
      type: 'object',
      required: ['model'],
      properties: {
        model: {
          type: 'object',
          required: ['parameters'],
          properties: {
            overrides: {
              type: 'object',
              properties: {
                promo: {
                  type: 'boolean',
                },
              },
            },
          },
        },
      },
    },
    analyticsConfig: {
      type: 'object',
      properties: {
        chartIds: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
        internalDashboards: {
          type: 'array',
          items: {
            type: 'object',
            required: ['name', 'link'],
            properties: {
              name: {
                type: 'string',
              },
              link: {
                type: 'string',
              },
            },
          },
        },
      },
    },
  },
}

export const shopifyStoreUiSchema = {
  //UI schema for shopifyStoreJsonSchema
  type: 'VerticalLayout',
  elements: [
    {
      type: 'Group',
      label: 'Store',
      elements: [
        {
          type: 'Control',
          scope: '#/properties/shop',
          options: {
            readOnly: true,
          },
        },
        {
          type: 'Control',
          scope: '#/properties/appHandle',
        },
        {
          type: 'Control',
          scope: '#/properties/name',
        },
        {
          type: 'Control',
          scope: '#/properties/website',
        },
        {
          type: 'Control',
          scope: '#/properties/pendingInstall',
        },
      ],
    },
    {
      type: 'Group',
      label: 'Promo Config',
      elements: [
        {
          label:
            'Model Overrides (setting "Promo" to true will return discount for all users)',
          type: 'Control',
          scope:
            '#/properties/promoConfig/properties/model/properties/overrides',
          options: {
            readOnly: true,
          },
        },
      ],
    },
    {
      type: 'Group',
      label: 'Analytics Config',
      elements: [
        {
          label: 'Embedded Amplitude Chart IDs (Merchant Analytics)',
          type: 'Control',
          scope: '#/properties/analyticsConfig/properties/chartIds',
        },
        {
          label: 'Internal Analytics Dashboards',
          type: 'Control',
          scope: '#/properties/analyticsConfig/properties/internalDashboards',
        },
      ],
    },
  ],
}

export function getLaunchConfigJsonSchema(widgets: ShopifyStoreWidget[]) {
  const widgetOptions = widgets.map((widget) => ({
    const: widget._id,
    title: widget.name,
  }))

  const launchConfigJsonSchema = {
    type: 'object',
    required: ['name', 'configVariants'],
    properties: {
      name: {
        type: 'string',
      },
      configVariants: {
        type: 'array',
        minItems: 1,
        items: {
          type: 'object',
          required: ['modelConfig'],
          properties: {
            ...(widgetOptions.length > 0
              ? {
                  widgetId: {
                    type: 'string',
                    oneOf: widgetOptions,
                  },
                }
              : {}),
            modelConfig: {
              type: 'object',
              required: ['parameters'],
              properties: {
                parameters: {
                  type: 'object',
                  required: ['model', 'floor', 'ceiling', 'start', 'end'],
                  properties: {
                    model: {
                      type: 'string',
                    },
                    floor: {
                      type: 'number',
                      minimum: 0,
                      maximum: 1,
                    },
                    ceiling: {
                      type: 'number',
                      minimum: 0,
                      maximum: 1,
                    },
                    start: {
                      type: 'integer',
                      minimum: 0,
                    },
                    end: {
                      type: 'integer',
                      minimum: 0,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  }

  const launchConfigUiSchema = {
    type: 'VerticalLayout',
    elements: [
      {
        type: 'Control',
        scope: '#/properties/name',
      },
      {
        type: 'Control',
        scope: '#/properties/configVariants',
        options: {
          detail: {
            type: 'VerticalLayout',
            elements: [
              ...(widgetOptions.length > 0
                ? [
                    {
                      type: 'Control',
                      scope: '#/properties/widgetId',
                      label: 'Widget',
                    },
                  ]
                : []),
              {
                type: 'Control',
                scope: '#/properties/modelConfig',
                label: 'Model Config',
              },
            ],
          },
        },
      },
    ],
  }
  return { launchConfigJsonSchema, launchConfigUiSchema }
}
