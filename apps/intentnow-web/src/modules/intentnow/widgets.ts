import { DiscountConfig, ShopifyStoreWidget } from '@packages/shared-entities'

export function generateInjectWidgetScript(
  shop: string,
  appHandle: string,
  tagVersion: string
) {
  // Note: keep this script template in sync with logics in: https://github.com/intentnow/intentnow-shopify-app/blob/main/extensions/intentnow-promo-widget/blocks/intentnow-embed.liquid

  return `
  (function () {
    const renderRoot = document.createElement('div')
    renderRoot.id = 'intentnow-render-root'
    document.body.appendChild(renderRoot)

    //Logger flag
    const logging = true
  
    const script = document.createElement('script')
    script.src = 'https://api.intentnow.com/cdn/scripts/intentnow-tag-${tagVersion}.js'
  
    script.addEventListener('load', function() {
      console.debug('Intentnow Tag has loaded!');
      runIntentnowWidget()
    })
    document.head.appendChild(script);
    
    async function runIntentnowWidget() {
      logging && console.debug("[intentnow-embed] DOM loaded");

      if (!IntentnowTag) {
        logging && console.error("IntentnowTag is not defined");
        return;
      }

      logging && console.debug("IntentnowTag is defined");
      await initIntentNow()

      IntentnowTag.runIntentnowWidget(
        'intentnow-render-root',
        () => {
          const cartDiscounts = intentnowGetCartDiscounts();
          return !cartDiscounts?.length
        }
      )
    }

    function intentnowGetCartDiscounts() {
      const cartDiscounts = [];
      return cartDiscounts;
    }

    async function initIntentNow() {
      if (!IntentnowTag) {
        logging && console.error("IntentnowTag is not defined");
        return;
      }

      let batchTimeoutHighPriorityInMs = 1000
      
      const eventSettings = {
        shop: '${shop}',
        eventSource: 'intentnow-promo-widget',
        eventApiUrl: 'https://api2.intentnow.com/api/intentnow/events',
        eventApiToken: '',
        logging,
        customerId: undefined,
        customerEmail: undefined,
        batchTimeoutInMs: 10000,
        batchTimeoutHighPriorityInMs,
      }

      const intentnowSettings = {
        shop: '${shop}',
        appProxySubPath: '${appHandle}',
        logging,
        baseApiUrl: 'https://api.intentnow.com/api/intentnow/shopify-app-proxy-test/${appHandle}',
      }

      await IntentnowTag.initializeEvents(eventSettings)
      IntentnowTag.initializeIntentnow(intentnowSettings)
    }
  })()
  `
}

export function getPreviewWidgetUrl(
  shop: string | undefined,
  website: string | undefined,
  widgetData: ShopifyStoreWidget | undefined
) {
  if (!website && !shop) {
    return ''
  }

  return `${website || `https://${shop}`}?intentnow-preview=${widgetData?._id || 'true'}`
}

export function generatePreviewDiscount(discountConfig: DiscountConfig) {
  const prefix = discountConfig.codeGen.prefix ?? ''
  return {
    title: discountConfig.discountInput.title!,
    code: `${prefix}${discountConfig.codeGen.alphabet.slice(
      0,
      discountConfig.codeGen.length - prefix.length
    )}`,
    startsAt: new Date(),
    endsAt: new Date(),
  }
}
