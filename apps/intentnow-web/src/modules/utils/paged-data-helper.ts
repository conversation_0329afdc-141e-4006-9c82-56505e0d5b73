import { useEffect, useState } from 'react'
import useSWR from 'swr'

export interface PagedFetcherPageInfo {
  // count: number
  // pageSize: number
  totalCount?: number
  first?: string
  last?: string
  hasPrev: boolean
  hasNext: boolean
}

export type PagedFetcher<T> = (
  pageSize: number,
  startAfter?: string,
  endBefore?: string
) => Promise<{
  data: T[]
  pageInfo?: PagedFetcherPageInfo
}>

export interface DecoratedPageInfo {
  totalCount?: number
  hasPrev: boolean
  hasNext: boolean
  prevPage?: () => void
  nextPage?: () => void
  firstPage: () => void
  lastPage: () => void
}

export function usePagedData<T>(
  // The "dataId" is used by SWR to uniquely identify the cached data. Not using a proper dataId may cause cached data being used in wrong place
  dataId: string | undefined,
  pageSize: number,
  pagedFetcher: PagedFetcher<T>
) {
  const [startAfter, setStartAfter] = useState<string>()
  const [endBefore, setEndBefore] = useState<string>()

  //Reset page parameters whenever dataId or pageSize changes
  useEffect(() => {
    setStartAfter(undefined)
    setEndBefore(undefined)
  }, [dataId, pageSize])

  const { data, isLoading, error, mutate } = useSWR(
    dataId ? { dataId, pageSize, startAfter, endBefore } : undefined,
    async (params) => {
      const { data, pageInfo } = await pagedFetcher(
        params.pageSize,
        params.startAfter,
        params.endBefore
      )
      return {
        data,
        pageInfo,
      }
    }
  )

  const hasPrev = data?.pageInfo?.hasPrev ?? false
  const hasNext = data?.pageInfo?.hasNext ?? false
  const prevPage = hasPrev
    ? () => {
        if (data?.pageInfo) {
          setStartAfter(undefined)
          setEndBefore(data.pageInfo.first)
        }
      }
    : undefined
  const nextPage = hasNext
    ? () => {
        if (data?.pageInfo) {
          setStartAfter(data.pageInfo.last)
          setEndBefore(undefined)
        }
      }
    : undefined

  function firstPage() {
    setStartAfter(undefined)
    setEndBefore(undefined)
  }

  function lastPage() {
    setStartAfter(undefined)
    setEndBefore('__LAST__')
  }

  return {
    data: data?.data,
    isLoading,
    error,
    mutate,
    pageInfo: {
      totalCount: data?.pageInfo?.totalCount,
      hasPrev,
      hasNext,
      prevPage,
      nextPage,
      firstPage,
      lastPage,
    } satisfies DecoratedPageInfo,
  }
}
