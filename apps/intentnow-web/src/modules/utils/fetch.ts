import { appConfig } from '@/modules/config/app'

export const baseApiUrl = appConfig.baseApiUrl

export type ApiFetchError = Error & {
  response?: Response
}

export const apiFetch = async <DT = any>(
  apiUrl: string,
  init?: RequestInit,
  json = true
) => {
  const headers: any = {
    ...init?.headers,
    ...(json ? { 'Content-Type': 'application/json' } : {}),
  }

  let fetchUrl
  if (apiUrl.startsWith('http')) {
    //absolute URL
    fetchUrl = apiUrl
  } else {
    //relative URL
    fetchUrl = baseApiUrl + apiUrl
  }

  const response = await fetch(fetchUrl, { ...init, headers })

  let resBody = null
  if (json) {
    try {
      resBody = await response.json()
    } catch (e) {
      console.error('parse response error', e)
      throw e
    }
  } else {
    try {
      resBody = await response.text()
    } catch (e) {
      console.error('parse response error', e)
      throw e
    }
  }

  if (!response.ok) {
    const error = new Error(
      `API fetch error, status=${response.status}, body=${JSON.stringify(
        resBody
      )}`
    ) as ApiFetchError

    error.response = response
    throw error
  }
  return resBody as DT
}

export const apiPostFetch = async <DT = any, BT = any>(
  [apiUrl, body]: [string, BT],
  init?: RequestInit,
  json = true
) => {
  return await apiFetch<DT>(
    apiUrl ?? null,
    {
      ...init,
      method: 'POST',
      body: json ? JSON.stringify(body ?? {}) : (body as any),
    },
    json
  )
}
