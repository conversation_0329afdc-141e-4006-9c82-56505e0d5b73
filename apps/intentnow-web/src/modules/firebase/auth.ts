import { firebase } from '../firebase/firebase'
import {
  getAuth,
  GoogleAuthProvider,
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
} from 'firebase/auth'
import { useEffect } from 'react'
import { firebaseConfig } from '../firebase/firebase'
import { useRouter } from 'next/navigation'
import { useAuthState } from 'react-firebase-hooks/auth'

export const useFirebaseAuth = ({
  locale,
  redirectUrl,
}: {
  locale?: string
  redirectUrl?: string
} = {}) => {
  const auth = getAuth(firebase)
  const router = useRouter()
  if (locale) {
    auth.languageCode = locale
  }
  const [user, authLoading, authError] = useAuthState(auth)

  useEffect(() => {
    //This is for handling the redirect from Google Auth
    if (auth && redirectUrl && router) {
      getRedirectResult(auth).then((result) => {
        if (result?.user) {
          router.push(redirectUrl)
        }
      })
    }
  }, [auth, redirectUrl, router])

  const signInWithGoogle = async () => {
    const provider = new GoogleAuthProvider()
    if (firebaseConfig.authRedirect) {
      await signInWithRedirect(auth, provider)
      return
    } else {
      const result = await signInWithPopup(auth, provider)
      return result.user
    }
  }

  const signOut = async () => {
    await auth.signOut()
  }

  // The reason we are doing this check: "(user?.uid && (profileLoading || !profileValue))",
  // is because the profileLoading state coming from useDocument is not reliable.
  // There is race condition where the profileLoading returns false, but the profileValue
  // is not returned. If a userId has been passed in, the profileValue should always have
  // a value (even with empty data), if the profileLoading changes to false. If profileValue
  // has not value, it means that useDocument() is still loading the data.
  const loading = Boolean(authLoading)
  const error = authError
  const dataComplete = !loading && user

  //TODO: most of the functions here can't be used in Shopify session
  //      we need to either remove them from the return or throw an error.
  return {
    user: dataComplete ? user : null,
    loading,
    error,
    signInWithGoogle,
    signOut,
  }
}
