'use client'
import { Provider as AppBridgeProvider } from '@shopify/app-bridge-react'
import { useSearchParams } from 'next/navigation'
import { useEffect } from 'react'
import { useLocalStorage } from 'usehooks-ts'

interface ShopifyAppConfig {
  API_KEY: string
  APP_HANDLE: string
}

const shopifyAppConfigs = JSON.parse(
  process.env.NEXT_PUBLIC_SHOPIFY_APP_FE_CONFIGS ?? '[]'
) as ShopifyAppConfig[]

const shopifyAppConfigsMap: { [handle: string]: ShopifyAppConfig } = {}

shopifyAppConfigs.forEach((config) => {
  shopifyAppConfigsMap[config.APP_HANDLE] = config
})

// console.debug('shopifyAppConfigs', shopifyAppConfigs)
// console.debug('shopifyAppConfigsMap', shopifyAppConfigsMap)

export function ShopifyAppProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const [, setCachedShop] = useLocalStorage<string | undefined>(
    'intentnow_shopify-shop',
    undefined
  )

  const [cachedHost, setCachedHost] = useLocalStorage<string | undefined>(
    'intentnow_shopify-host',
    undefined
  )

  const [cachedAppHandle, setCachedAppHandle] = useLocalStorage<
    string | undefined
  >('intentnow_shopify-app-handle', undefined)

  const params = useSearchParams()
  const host = params.get('host') ?? ''
  const shop = params.get('shop') ?? undefined
  const appHandle =
    params.get('handle') ?? params.get('index') ?? cachedAppHandle

  useEffect(() => {
    if (shop) {
      setCachedShop(shop)
    }
  }, [shop, setCachedShop])

  useEffect(() => {
    if (host) {
      setCachedHost(host)
    }
  }, [host, setCachedHost])

  useEffect(() => {
    if (appHandle != undefined) {
      setCachedAppHandle(appHandle)
    }
  }, [appHandle, setCachedAppHandle])

  if (!appHandle || !shopifyAppConfigsMap[appHandle]) {
    return <></>
  }

  return (
    <AppBridgeProvider
      config={{
        apiKey: shopifyAppConfigsMap[appHandle].API_KEY,
        host: host ?? cachedHost,
        forceRedirect: false,
      }}
    >
      {children}
    </AppBridgeProvider>
  )
}
