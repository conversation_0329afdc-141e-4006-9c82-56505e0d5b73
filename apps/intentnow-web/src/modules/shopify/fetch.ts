import { Redirect } from '@shopify/app-bridge/actions'
import { ShopifyApp } from './app'
import { authenticatedFetch } from '@shopify/app-bridge/utilities'
import { appConfig } from '@/modules/config/app'

const baseUrl = appConfig.baseApiUrl

export enum ShopifyApiErrorReason {
  unauthorized = 'unauthorized',
  reauthRequested = 'reauthRequested',
  throttled = 'throttled',
  unknown = 'unknown',
}

export class ShopifyApiError extends Error {
  constructor(
    message: string,
    public readonly reason: ShopifyApiErrorReason | string,
    public readonly extensions?: Record<string, any>,
    public readonly body?: any
  ) {
    super(message)
    // This will set the prototype correctly when casting.
    Object.setPrototypeOf(this, ShopifyApiError.prototype)
    // This will set the name property of the error to the class name.
    this.name = this.constructor.name
  }
}

export function shopifyApiFetch<DT = any>(shopifyApp: ShopifyApp | undefined) {
  if (!shopifyApp) return () => Promise.resolve() as DT

  const fetchFunction = authenticatedFetch(shopifyApp.app)

  return async (relativeUrl: string, options?: RequestInit) => {
    const headers: any = {
      ...options?.headers,
      'Content-Type': 'application/json',
    }
    const apiUrl = `${baseUrl}/api/shopify/${
      shopifyApp.appHandle
    }${relativeUrl}${relativeUrl.indexOf('?') < 0 ? '?' : '&'}shop=${
      shopifyApp.shop
    }&host=${shopifyApp.host}&embedded=1`
    const response = await fetchFunction(apiUrl, { ...options, headers })
    if (
      response.headers.get('X-Shopify-API-Request-Failure-Reauthorize') === '1'
    ) {
      const authUrlHeader = response.headers.get(
        'X-Shopify-API-Request-Failure-Reauthorize-Url'
      )
      let redirectUrl =
        authUrlHeader || `${baseUrl}/api/shopify/${shopifyApp.appHandle}/login`
      if (!redirectUrl?.startsWith('http')) {
        redirectUrl = `${baseUrl}${redirectUrl}`
      }

      const redirect = Redirect.create(shopifyApp.app)
      redirect.dispatch(Redirect.Action.REMOTE, redirectUrl)

      //Sleep for 5 seconds to allow the redirection to happen before throwing the error
      const sleepMs = 5000
      await new Promise((resolve) => {
        setTimeout(resolve, sleepMs)
      })
      throw new ShopifyApiError(
        'Shopify API requested reauthorization',
        ShopifyApiErrorReason.reauthRequested
      )
    }

    let resBody = null
    try {
      resBody = await response.json()
    } catch (e) {
      console.error('parse response error', e)
    }

    if (!response.ok) {
      let reason: string = ShopifyApiErrorReason.unknown
      const extensions = resBody?.body?.extensions
      if (
        resBody?.body?.errors?.graphQLErrors[0] &&
        resBody?.body?.errors?.graphQLErrors[0].message === 'Throttled'
      ) {
        console.debug(`Shopify API fetch throttled`)
        reason = ShopifyApiErrorReason.throttled
      } else {
        console.error(
          `Shopify API fetch error, status=${
            response.status
          }, body=${JSON.stringify(resBody)}`
        )
        reason = `${JSON.stringify(resBody)}, ${response.status}`
      }
      throw new ShopifyApiError(
        `Shopify API fetch error: ${reason}`,
        reason,
        extensions,
        resBody
      )
    }
    return resBody as DT
  }
}

export function shopifyApiPostFetch<DT = any, BT = any>(
  shopifyApp: ShopifyApp | undefined
) {
  const fetch = shopifyApiFetch<DT>(shopifyApp)
  return async ([relativeUrl, body]: [string, BT], options?: RequestInit) => {
    return await fetch(relativeUrl ?? null, {
      ...options,
      method: 'POST',
      body: JSON.stringify(body ?? {}),
    })
  }
}

export function shopifyApiDeleteFetch<DT = any>(
  shopifyApp: ShopifyApp | undefined
) {
  const fetch = shopifyApiFetch<DT>(shopifyApp)
  return async (relativeUrl: string, options?: RequestInit) => {
    return await fetch(relativeUrl ?? null, {
      ...options,
      method: 'DELETE',
    })
  }
}
