import { AppBridgeState, ClientApplication } from '@shopify/app-bridge'
import { useAppBridge } from '@shopify/app-bridge-react'
import { useSearchParams } from 'next/navigation'
import { useMemo } from 'react'
import { useLocalStorage } from 'usehooks-ts'

export interface ShopifyApp {
  app: ClientApplication<AppBridgeState>
  shop: string
  host: string
  appHandle: string
  //locale: string
}

export function useShopifyApp(): ShopifyApp | undefined {
  const app = useAppBridge()
  const [cachedShop] = useLocalStorage<string | undefined>(
    'intentnow_shopify-shop',
    undefined
  )
  const shop = useSearchParams().get('shop') ?? cachedShop
  const [cachedHost] = useLocalStorage<string | undefined>(
    'intentnow_shopify-host',
    undefined
  )
  const host = useSearchParams().get('host') ?? cachedHost

  const [cachedAppHandle] = useLocalStorage<string | undefined>(
    'intentnow_shopify-app-handle',
    undefined
  )
  const params = useSearchParams()
  const appHandle =
    params.get('handle') ?? params.get('index') ?? cachedAppHandle

  const shopifyApp = useMemo(() => {
    return app && shop && host && appHandle != undefined
      ? { app, shop, host, appHandle }
      : undefined
  }, [app, shop, host, appHandle])

  return shopifyApp
}
