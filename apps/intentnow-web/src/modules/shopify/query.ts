import { useAuthedApiFetch } from '../auth/fetch'

//Admin only
export function useShopifyQueries() {
  const { authedApiPostFetch } = useAuthedApiFetch()

  async function adminShopifyGraphQlQuery(
    appHandle: string | undefined,
    shop: string,
    graphQl: {
      query: string
      variables?: any
    }
  ) {
    return await authedApiPostFetch<
      any,
      {
        appHandle?: string
        shop: string
        graphQl: {
          query: string
          variables?: any
        }
      }
    >([
      '/api/shopify/graphql',
      {
        appHandle,
        shop,
        graphQl,
      },
    ])
  }

  async function adminShopifyRestQuery(
    appHandle: string | undefined,
    shop: string,
    rest: {
      op: string
      path: string
      query?: any
      data?: any
    }
  ) {
    return await authedApiPostFetch<
      any,
      {
        appHandle?: string
        shop: string
        rest: {
          op: string
          path: string
          query?: any
          data?: any
        }
      }
    >([
      '/api/shopify/rest',
      {
        appHandle,
        shop,
        rest,
      },
    ])
  }
  return {
    adminShopifyGraphQlQuery,
    adminShopifyRestQuery,
  }
}
