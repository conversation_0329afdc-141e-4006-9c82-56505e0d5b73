import { UseOneProps, useTableProps } from '@refinedev/core'

export const refineConfig = {
  defaultUseOneQueryOptions: {
    enabled: true,
    staleTime: 1000 * 60,
    refetchOnWindowFocus: 'always',
    refetchOnReconnect: true,
    retry: 0,
  } satisfies UseOneProps<any, any, any>['queryOptions'],
  defaultUseListQueryOptions: {
    enabled: true,
    staleTime: 1000 * 60,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    retry: 0,
  } satisfies useTableProps<any, any, any>['queryOptions'],
}
