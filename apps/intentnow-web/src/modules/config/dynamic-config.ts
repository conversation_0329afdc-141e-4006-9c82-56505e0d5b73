import { useDynamicConfig } from '@statsig/react-bindings'

//const isProd = process.env.NEXT_PUBLIC_APP_ENV === 'production'

export function useAdminAppConfig() {
  const adminAppConfig = useDynamicConfig('shopify-admin-app-config')
  const debug = adminAppConfig.get<boolean>('debug', false)
  const discounts = adminAppConfig.get<boolean>('discounts', false)
  const analytics = adminAppConfig.get<boolean>('analytics', false)
  const merchantPortal = adminAppConfig.get<boolean>('merchantPortal', false)

  return { debug, discounts, analytics, merchantPortal }
}
