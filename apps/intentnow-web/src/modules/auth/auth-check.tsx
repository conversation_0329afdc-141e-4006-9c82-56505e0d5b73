'use client'
import { useAuth } from './auth'

export function UserIsAdmin({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()

  if (!user || !user.roles?.admin) {
    return <></>
  }

  return <>{children}</>
}

export function UserIsNotAdmin({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()

  if (!user || user.roles?.admin) {
    return <></>
  }

  return <>{children}</>
}

export function UserIsMerchant({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()

  if (!user || !user.roles?.merchant) {
    return <></>
  }

  return <>{children}</>
}

export function UserIsNotMerchant({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()

  if (!user || user.roles?.merchant) {
    return <></>
  }

  return <>{children}</>
}
