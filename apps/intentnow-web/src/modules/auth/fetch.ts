import { apiFetch, baseApiUrl } from '../utils/fetch'
import { useAuth } from './auth'

export function useAuthedApiFetch() {
  const { user, getToken } = useAuth()

  const authedApiFetch = async <DT = any>(
    apiUrl: string,
    init?: RequestInit,
    json = true
  ) => {
    const headers: any = {}

    if (user) {
      const token = await getToken()
      if (token) {
        headers['X-Auth-Provider'] = user.authType.toUpperCase()
        headers.Authorization = `Bearer ${token}`
      }
    }

    return apiFetch<DT>(
      apiUrl,
      {
        ...init,
        headers: {
          ...init?.headers,
          ...headers,
        },
      },
      json
    )
  }

  const authedApiPostFetch = async <DT = any, BT = any>(
    [apiUrl, body]: [string, BT],
    init?: RequestInit,
    json = true
  ) => {
    return await authedApiFetch<DT>(
      apiUrl ?? null,
      {
        ...init,
        method: 'POST',
        body: json ? JSON.stringify(body ?? {}) : (body as any),
      },
      json
    )
  }

  const authedApiPatchFetch = async <DT = any, BT = any>(
    [apiUrl, body]: [string, BT],
    init?: RequestInit,
    json = true
  ) => {
    return await authedApiFetch<DT>(
      apiUrl ?? null,
      {
        ...init,
        method: 'PATCH',
        body: json ? JSON.stringify(body ?? {}) : (body as any),
      },
      json
    )
  }

  const authedApiDeleteFetch = async <DT = any, BT = any>(
    [apiUrl, body]: [string, BT],
    init?: RequestInit,
    json = true
  ) => {
    return await authedApiFetch<DT>(
      apiUrl ?? null,
      {
        ...init,
        method: 'DELETE',
        body: json ? JSON.stringify(body ?? {}) : (body as any),
      },
      json
    )
  }

  return {
    authedApiFetch,
    authedApiPostFetch,
    authedApiPatchFetch,
    authedApiDeleteFetch,
    baseApiUrl,
  }
}
