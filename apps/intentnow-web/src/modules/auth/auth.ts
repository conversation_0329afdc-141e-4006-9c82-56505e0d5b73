import { useFirebaseAuth } from '../firebase/auth'
import { useUser as useClerkUser, useAuth as useClerkAuth } from '@clerk/nextjs'
import {
  AuthType,
  ClerkSessionPayload,
  UserContext,
} from '@packages/shared-entities'
import { usePathname } from 'next/navigation'
import { useMemo } from 'react'

//TODO: Clerk Auth and Fire<PERSON> Auth will co-exist for a while. We will retire the Firebase Auth completely in the future.
const pathnamePrefixToUserTypeMap = [
  {
    prefix: '/admin2',
    userType: AuthType.clerk,
  },
  {
    prefix: '/merchant',
    userType: AuthType.clerk,
  },
  {
    prefix: '/admin',
    userType: AuthType.firebase,
  },
]

export const useAuth = () => {
  const pathname = usePathname()
  const clerkUser = useClerkUser()
  const firebaseAuth = useFirebaseAuth()
  const clerkAuth = useClerkAuth()

  const { user, loading, signOut, getToken } = useMemo(() => {
    let user: UserContext | undefined
    let loading = false
    let signOut = () => {}
    let getToken: () => Promise<string | undefined> = async () => undefined

    const userConfig = pathnamePrefixToUserTypeMap.find((m) =>
      pathname.startsWith(m.prefix)
    )

    if (userConfig) {
      if (userConfig.userType === AuthType.clerk) {
        const sessionPayload: ClerkSessionPayload | undefined =
          clerkAuth.sessionClaims ?? undefined
        const roles: string[] = sessionPayload?.metadata?.roles ?? ['merchant']

        user = clerkUser.user
          ? {
              authType: AuthType.clerk,
              userId: clerkUser.user.id,
              userInfo: {
                email: clerkUser.user.primaryEmailAddress?.emailAddress,
                displayName: clerkUser.user.fullName ?? undefined,
                imageUrl: clerkUser.user.imageUrl,
              },
              roles: {
                admin: roles.includes('admin'),
                merchant: roles.includes('merchant'),
              },
            }
          : undefined
        signOut = clerkAuth.signOut
        getToken = async () => {
          const token = await clerkAuth.getToken()
          return token ?? undefined
        }
      } else if (userConfig.userType === AuthType.firebase) {
        user = firebaseAuth.user
          ? {
              authType: AuthType.firebase,
              userId: firebaseAuth.user.uid,
              userInfo: {
                email: firebaseAuth.user.email ?? undefined,
                displayName: firebaseAuth.user.displayName ?? undefined,
                imageUrl: firebaseAuth.user.photoURL ?? undefined,
              },
              roles: {
                admin: true,
                merchant: false,
              },
            }
          : undefined
        loading = firebaseAuth.loading
        signOut = firebaseAuth.signOut
        getToken = async () => {
          const token = firebaseAuth.user
            ? await firebaseAuth.user.getIdToken()
            : undefined
          return token
        }
      }
    }

    return {
      user,
      loading,
      signOut,
      getToken,
    }
  }, [pathname, clerkUser, clerkAuth, firebaseAuth])

  return {
    user,
    loading,
    signOut,
    getToken,
  }
}
