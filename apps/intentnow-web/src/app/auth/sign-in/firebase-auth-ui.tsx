'use client'
import { firebase } from '@/modules/firebase/firebase'
import { getAuth, GoogleAuthProvider, onAuthStateChanged } from 'firebase/auth'
import { useEffect, useRef, useState } from 'react'
import 'firebaseui/dist/firebaseui.css'

const auth = getAuth(firebase)

export const FirebaseAuthUI = ({
  signInSuccessUrl,
}: {
  signInSuccessUrl?: string
}) => {
  const firebaseAuthConfig = {
    signInFlow: 'popup',
    signInOptions: [GoogleAuthProvider.PROVIDER_ID],
    signInSuccessUrl,
  }

  return (
    <>
      <StyledFirebaseAuth uiConfig={firebaseAuthConfig} firebaseAuth={auth} />
    </>
  )
}

interface Props {
  // The Firebase UI Web UI Config object.
  // See: https://github.com/firebase/firebaseui-web#configuration
  uiConfig: any
  // Callback that will be passed the FirebaseUi instance before it is
  // started. This allows access to certain configuration options such as
  // disableAutoSignIn().
  uiCallback?(ui: any): void
  // The Firebase App auth instance to use.
  firebaseAuth: any // As firebaseui-web
  className?: string
}

const StyledFirebaseAuth = ({
  uiConfig,
  firebaseAuth,
  className,
  uiCallback,
}: Props) => {
  const [userSignedIn, setUserSignedIn] = useState(false)
  const elementRef = useRef(null)

  useEffect(() => {
    // Get or Create a firebaseUI instance.
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const firebaseui = require('firebaseui')

    const firebaseUiWidget =
      firebaseui.auth.AuthUI.getInstance() ||
      new firebaseui.auth.AuthUI(firebaseAuth)
    if (uiConfig.signInFlow === 'popup') firebaseUiWidget.reset()

    // We track the auth state to reset firebaseUi if the user signs out.
    const unregisterAuthObserver = onAuthStateChanged(firebaseAuth, (user) => {
      if (!user && userSignedIn) firebaseUiWidget.reset()
      setUserSignedIn(!!user)
    })

    // Trigger the callback if any was set.
    if (uiCallback) uiCallback(firebaseUiWidget)

    // Render the firebaseUi Widget.
    firebaseUiWidget.start(elementRef.current, uiConfig)

    return () => {
      unregisterAuthObserver()
      firebaseUiWidget.reset()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uiConfig])

  return <div className={className} ref={elementRef} />
}

export default StyledFirebaseAuth
