'use client'
import { useSearchParams } from 'next/navigation'
import { FirebaseAuthUI } from './firebase-auth-ui'
import { Box } from '@mui/material'

export default function FirebaseUIPocPage() {
  const redirect = useSearchParams().get('redirect') ?? '/'
  return (
    <Box
      sx={{ p: 2, border: '1px dashed grey' }}
      maxWidth={300}
      justifySelf="center"
    >
      <FirebaseAuthUI signInSuccessUrl={redirect}></FirebaseAuthUI>
    </Box>
  )
}
