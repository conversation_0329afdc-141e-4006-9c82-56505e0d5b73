'use client'

import { StoreWidget2Editor } from '@/components/widgets/store-widget2-editor'
import { useSearchParams } from 'next/navigation'

export default function AdminStoreWidget3Edit() {
  const searchParams = useSearchParams()
  const shop = searchParams.get('shop')
  const widgetId = searchParams.get('widget-id') ?? undefined
  return (
    <div>{shop && <StoreWidget2Editor shop={shop} widgetId={widgetId} />}</div>
  )
}
