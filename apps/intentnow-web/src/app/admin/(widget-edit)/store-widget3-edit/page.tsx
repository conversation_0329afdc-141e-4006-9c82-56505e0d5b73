'use client'

import { StoreWidget3Editor } from '@/components/widgets/store-widget3-editor'
import { useSearchParams } from 'next/navigation'

export default function AdminStoreWidget3Edit() {
  const searchParams = useSearchParams()
  const shop = searchParams.get('shop')
  const widgetId = searchParams.get('widget-id') ?? undefined
  return (
    <div>{shop && <StoreWidget3Editor shop={shop} widgetId={widgetId} />}</div>
  )
}
