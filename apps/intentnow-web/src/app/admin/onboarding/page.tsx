'use client'
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tabs,
  TextField,
} from '@mui/material'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import { AdminLayout } from '../admin-layout'
import { useEffect, useState } from 'react'
import { useStoreConfigs } from '@/modules/intentnow/settings'
import { useStoreOnboarding } from '@/modules/intentnow/onboarding'
import { JSONTree } from 'react-json-tree'

export default function AdminOnboardingPage() {
  const [tab, setTab] = useState<string>('appOnboarding')
  const [shop, setShop] = useState<string>('')
  const [name, setName] = useState<string>()
  const [website, setWebsite] = useState<string>()
  const [fromShop, setFromShop] = useState<string>()
  const [appHandle, setAppHandle] = useState<string>('')
  const [appName, setAppName] = useState<string>()
  const [apiKey, setApiKey] = useState<string>()
  const [secretKey, setSecretKey] = useState<string>()
  const [promoEmbedId, setPromoEmbedId] = useState<string>()
  const [loading, setLoading] = useState<boolean>()

  const [tomlFile, setTomlFile] = useState<string>()
  const [serverConfigs, setServerConfigs] = useState<any[]>()
  const [webConfigs, setWebConfigs] = useState<any[]>()

  const [initStoreResults, setInitStoreResults] = useState<
    {
      item: string
      done: boolean
    }[]
  >()

  const { storeConfigs } = useStoreConfigs()
  const { generateAppHandle, generateAppConfigs, initStore } =
    useStoreOnboarding(shop)

  useEffect(() => {
    setAppHandle(generateAppHandle())
  }, [generateAppHandle])

  return (
    <AdminLayout title={`IntentNow Admin - Onboarding`}>
      <Stack>
        <Box maxWidth={500}>
          <Stack>
            <TextField
              required={true}
              label="New Shop"
              name="shop"
              fullWidth
              margin="dense"
              value={shop}
              onChange={(e) => {
                setShop(e.target.value)
              }}
            />
            <TextField
              required={true}
              label="App Handle"
              name="appHandle"
              fullWidth
              margin="dense"
              value={appHandle}
              onChange={(e) => setAppHandle(e.target.value)}
              InputLabelProps={{ shrink: Boolean(appHandle) }}
            />
          </Stack>
        </Box>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tab}
            onChange={(e, value) => {
              setTab(value)
            }}
          >
            <Tab label="Shopify App Onboarding" value="appOnboarding" />
            <Tab label="Store Onboarding" value="storeOnboarding" />
            <Tab label="Others" value="otherOnboarding" />
          </Tabs>
          {tab === 'appOnboarding' && (
            <Box>
              <Stack>
                <Box maxWidth={500}>
                  <TextField
                    required={true}
                    label="App Name"
                    name="appName"
                    fullWidth
                    margin="dense"
                    value={appName}
                    onChange={(e) => setAppName(e.target.value)}
                  />
                  <TextField
                    required={true}
                    label="API Key (Client ID)"
                    name="apiKey"
                    fullWidth
                    margin="dense"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                  />
                  <TextField
                    required={true}
                    label="Secret Key"
                    name="secretKey"
                    fullWidth
                    margin="dense"
                    value={secretKey}
                    onChange={(e) => setSecretKey(e.target.value)}
                  />
                  <TextField
                    label="Promo Embed ID"
                    name="promoEmbedId"
                    fullWidth
                    margin="dense"
                    value={promoEmbedId}
                    onChange={(e) => setPromoEmbedId(e.target.value)}
                  />
                  <Button
                    variant="contained"
                    disabled={
                      !(shop && appHandle && appName && apiKey && secretKey) ||
                      loading
                    }
                    onClick={async () => {
                      if (appHandle && appName && apiKey && secretKey) {
                        setLoading(true)
                        const { tomlFile, appConfigs } =
                          await generateAppConfigs({
                            appHandle,
                            appName,
                            apiKey,
                            secretKey,
                            promoEmbedId,
                          })
                        setTomlFile(tomlFile)
                        const { serverConfigs, webConfigs } = appConfigs
                        setServerConfigs(serverConfigs)
                        setWebConfigs(webConfigs)
                        setLoading(false)
                      }
                    }}
                  >
                    Generate App Configs
                  </Button>
                </Box>
                {tomlFile && (
                  <Box margin={2}>
                    {`shopify.app.${appHandle}.toml`}
                    <Button
                      startIcon={<ContentCopyIcon />}
                      onClick={() => {
                        if (tomlFile) {
                          navigator.clipboard.writeText(tomlFile)
                        }
                      }}
                    />
                    <TextField
                      value={tomlFile}
                      multiline={true}
                      minRows={10}
                      fullWidth
                      InputProps={{
                        readOnly: true,
                      }}
                    />
                  </Box>
                )}
                {serverConfigs && (
                  <Box margin={2}>
                    SHOPIFY_APP_CONFIGS
                    <Button
                      startIcon={<ContentCopyIcon />}
                      onClick={() => {
                        if (serverConfigs) {
                          navigator.clipboard.writeText(
                            JSON.stringify(serverConfigs)
                          )
                        }
                      }}
                    />
                    <JSONTree
                      data={serverConfigs ?? []}
                      shouldExpandNodeInitially={() => true}
                    />
                  </Box>
                )}

                {webConfigs && (
                  <Box margin={2}>
                    SHOPIFY_APP_FE_CONFIGS
                    <Button
                      startIcon={<ContentCopyIcon />}
                      onClick={() => {
                        if (webConfigs) {
                          navigator.clipboard.writeText(
                            JSON.stringify(webConfigs)
                          )
                        }
                      }}
                    />
                    <JSONTree
                      data={webConfigs ?? []}
                      shouldExpandNodeInitially={() => true}
                    />
                  </Box>
                )}
              </Stack>
            </Box>
          )}
          {tab === 'storeOnboarding' && (
            <Box>
              <Stack>
                <Box maxWidth={500}>
                  <TextField
                    required={true}
                    label="Store Name"
                    name="name"
                    fullWidth
                    margin="dense"
                    value={name}
                    onChange={(e) => {
                      setName(e.target.value)
                    }}
                  />
                  <TextField
                    required={true}
                    label="Website"
                    name="website"
                    fullWidth
                    margin="dense"
                    value={website}
                    onChange={(e) => {
                      setWebsite(e.target.value)
                    }}
                  />
                  <FormControl
                    fullWidth
                    required={false}
                    variant="standard"
                    sx={{ m: 1, maxWidth: 300 }}
                  >
                    <InputLabel id="demo-simple-select-label">
                      Copy Static Config from Store
                    </InputLabel>
                    <Select
                      labelId="demo-simple-select-label"
                      id="demo-simple-select"
                      value={fromShop}
                      label="Store"
                      disabled={!storeConfigs}
                      onChange={(e) => {
                        setFromShop(e.target.value)
                      }}
                    >
                      {storeConfigs &&
                        storeConfigs.map((storeConfig, index) => (
                          <MenuItem key={index} value={storeConfig.shop}>
                            {storeConfig.shop}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>

                  <br />
                  <Button
                    variant="contained"
                    disabled={
                      !(shop && appHandle && name && website) || loading
                    }
                    onClick={async () => {
                      if (shop && name && website && appHandle) {
                        setLoading(true)
                        try {
                          const {
                            storeIdCreated,
                            experimentCreated,
                            dynamicConfigCreated,
                          } = await initStore(
                            name,
                            website,
                            fromShop,
                            appHandle
                          )

                          const results = [
                            {
                              item: 'Create Store Config',
                              done: Boolean(storeIdCreated),
                            },
                            {
                              item: 'Create Model Experiment',
                              done: Boolean(experimentCreated),
                            },
                            {
                              item: 'Create Dynamic Config',
                              done: Boolean(dynamicConfigCreated),
                            },
                          ]
                          setInitStoreResults(results)
                        } finally {
                          setLoading(false)
                        }
                      }
                    }}
                  >
                    Initialize Store
                  </Button>
                </Box>
                {initStoreResults && (
                  <Box>
                    <TableContainer>
                      <Table>
                        <TableBody>
                          {initStoreResults.map((result, index) => (
                            <TableRow key={index}>
                              <TableCell>{result.item}</TableCell>
                              <TableCell>
                                {result.done ? `done` : `skipped`}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                )}
              </Stack>
            </Box>
          )}
          {tab === 'otherOnboarding' && (
            <Box>
              <div>
                <Button
                  onClick={() => {
                    window.open(
                      `https://partners.shopify.com/3823936/apps`,
                      '_blank'
                    )
                  }}
                >
                  Shopify Partners
                </Button>
              </div>
              <div>
                <Button
                  onClick={() => {
                    window.open(
                      `https://console.cloud.google.com/storage/browser/intentnow-cdn/cdn/shopify-images`,
                      '_blank'
                    )
                  }}
                >
                  Upload Image Assets
                </Button>
              </div>
              <div>
                <Button
                  onClick={() => {
                    window.open(
                      `https://github.com/intentnow/intentnow-monorepo/settings/environments/3783251593/edit`,
                      '_blank'
                    )
                  }}
                >
                  GitHub Action Variables (prod)
                </Button>
              </div>
            </Box>
          )}
        </Box>
      </Stack>
    </AdminLayout>
  )
}
