'use client'

import { AdminLayout } from '../../../admin-layout'
import { materialCells } from '@jsonforms/material-renderers'
import { JsonForms } from '@jsonforms/react'
import { Box } from '@mui/material'
import { useState } from 'react'
import { customRenderers } from '@/components/jsonforms/renderers'
import { JsonSchema, UISchemaElement } from '@jsonforms/core'

const schema: JsonSchema = {
  type: 'object',
  properties: {
    part1: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          minLength: 1,
        },
        done: {
          type: 'boolean',
        },
      },
    },
    due_date: {
      type: 'string',
      format: 'date',
    },
    recurrence: {
      type: 'string',
      enum: ['Never', 'Daily', 'Weekly', 'Monthly'],
    },
    pickColor: {
      type: 'string',
      format: 'color',
    },
  },
  required: ['name', 'due_date'],
}

const uischema: UISchemaElement & { elements: any[] } = {
  type: 'VerticalLayout',
  elements: [
    {
      type: 'Group',
      label: 'Input Group',
      elements: [
        {
          type: 'Control',
          label: false,
          scope: '#/properties/part1/properties/done',
        },
        {
          type: 'Control',
          scope: '#/properties/part1/properties/name',
        },
      ],
    },
    {
      type: 'Control',
      scope: '#/properties/due_date',
    },
    {
      type: 'Control',
      scope: '#/properties/recurrence',
    },
    {
      type: 'Group',
      label: 'Color Picker',
      elements: [
        {
          type: 'Control',
          scope: '#/properties/pickColor',
        },
      ],
    },
  ],
}

export default function AdminWidgetsPage() {
  const [data, setData] = useState({})

  return (
    <AdminLayout title="IntentNow Admin - Jsonforms POC">
      <Box maxWidth={300}>
        <div>
          <JsonForms
            schema={schema}
            uischema={uischema}
            data={data}
            renderers={customRenderers}
            cells={materialCells}
            onChange={({ data }) => setData(data)}
          />
        </div>

        <div>
          <pre>{JSON.stringify(data, null, 2)}</pre>
        </div>
      </Box>
    </AdminLayout>
  )
}
