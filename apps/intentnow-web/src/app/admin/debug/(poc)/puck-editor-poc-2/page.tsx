'use client'

import { getWidget3DialogPuckConfigWithCustomFields } from '@/components/puck/custom-fields'
import { Puck, Plugin } from '@measured/puck'
import '@measured/puck/puck.css'
import createEmotionCache from '@measured/puck-plugin-emotion-cache'
import { usePromoWidgetContext } from '@packages/intentnow-tag'
import { ReactNode, useEffect } from 'react'
import { PromoWidgetProvider } from '@packages/intentnow-tag'

const emotionCache = createEmotionCache('puck-editor-poc-2')
const puckConfig = getWidget3DialogPuckConfigWithCustomFields()

const MyPlugin: Plugin = {
  overrides: {
    header: () => {
      return <></>
    },
    iframe: ({
      children,
      document,
    }: {
      children: ReactNode
      document?: Document
    }) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      useEffect(() => {
        if (document) {
          document.body.setAttribute(
            'style',
            'background: lightgray; border: 2px dotted black;'
          )
        }
      }, [document])

      return <div>{children}</div>
    },
  },
}

function PuckEditorPoc2() {
  const initialData: any = {
    root: { props: {} },
    content: [
      {
        type: 'DialogContainer',
        props: {
          image: {
            imageUrl:
              'https://api.intentnow.com/cdn/shopify-images/oofos/oofos-1.jpg',
            desktopImagePostion: 'right',
            showMobileImage: true,
          },
          styles: {
            colors: { color: 'black', backgroundColor: 'white' },
            font: { name: 'Arial', size: 20 },
          },
          id: 'DialogContainer-f909ae55-48d6-46fc-808c-395fb6d56db0',
        },
      },
    ],
    zones: {
      'DialogContainer-f909ae55-48d6-46fc-808c-395fb6d56db0:content-panel': [
        {
          type: 'Image',
          props: {
            imageUrl:
              'https://api.intentnow.com/cdn/shopify-images/oofos/oofos-logo-1.png',
            styles: { size: { width: 200 } },
            id: 'Image-eee8a6b4-8736-4584-8e74-6ec91c704d1c',
          },
        },
        {
          type: 'Text',
          props: {
            text: 'Kickstart Spring with Style',
            align: 'center',
            id: 'Text-f75a8852-d109-4b0c-89a6-61e8058a2fac',
            styles: {
              font: { size: 26 },
              box: { paddingTop: 0, marginTop: 28, marginBottom: 17.5 },
            },
          },
        },
        {
          type: 'Text',
          props: {
            text: '{{discountTitle}}!',
            align: 'center',
            id: 'Text-785a6dbf-a71b-45d2-8899-b3455490352d',
            styles: { font: { size: 64 } },
          },
        },
        {
          type: 'Button',
          props: {
            text: 'COPY CODE',
            styles: {
              colors: {
                color: 'white',
                backgroundColor: 'blue',
                hoverBackgroundColor: 'black',
              },
              box: {
                paddingTop: 10,
                paddingBottom: 10,
                paddingLeft: 0,
                paddingRight: 0,
              },
              size: { width: 200 },
            },
            id: 'Button-e9b8f39c-feb0-4b89-b472-498aa83a60d5',
            action: {
              action: 'open-url',
              actionTarget: 'https://www.oofos.com',
            },
          },
        },
      ],
    },
  }

  const { setContent } = usePromoWidgetContext()
  useEffect(() => {
    setContent({
      discount: {
        title: '15% Off',
        code: 'ABCDEFGH',
        startsAt: new Date(),
        endsAt: new Date(),
      },
    })
  }, [setContent])

  return (
    <>
      <div>
        <Puck
          headerTitle="Widget Dialog"
          headerPath="abcdefg"
          config={puckConfig}
          data={initialData}
          onChange={(data) => {
            console.log('puck data changed', JSON.stringify(data))
          }}
          viewports={[
            {
              label: 'Desktop',
              width: 1000,
              height: 800,
              icon: 'Monitor',
            },
            {
              label: 'Desktop',
              width: 390,
              height: 700,
              icon: 'Smartphone',
            },
          ]}
          plugins={[MyPlugin, emotionCache]}
        ></Puck>
      </div>
    </>
  )
}

export default function PuckEditorPoc2Page() {
  return (
    <PromoWidgetProvider>
      <PuckEditorPoc2 />
    </PromoWidgetProvider>
  )
}
