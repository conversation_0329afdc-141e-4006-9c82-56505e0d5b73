/* eslint-disable @next/next/no-img-element */
'use client'

import { Config, DropZone, Puck } from '@measured/puck'
import '@measured/puck/puck.css'
import { PuckWrapper, usePuckContext } from '../puck-poc/puck'
import { ReactNode, useEffect } from 'react'

function RootComponent({
  desktopImagePostion,
}: {
  desktopImagePostion?: 'left' | 'right'
}) {
  const { isMobile } = usePuckContext()

  return (
    <>
      {isMobile && (
        <div
          style={{
            marginTop: '10px',
            justifyItems: 'center',
            alignItems: 'center',
            width: '100%',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              maxWidth: '330px',
              height: 'auto',
            }}
          >
            <DropZone zone="image-panel" />
            <DropZone zone="text-panel" />
          </div>
        </div>
      )}
      {!isMobile && (
        <div
          style={{
            display: 'flex',
            maxWidth: '810px',
            width: '810px',
            height: 'auto',
            background: 'white',
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
          }}
        >
          {desktopImagePostion === 'left' && (
            <>
              <DropZone zone="image-panel" allow={['SideImage']} />
              <DropZone zone="text-panel" disallow={['SideImage']} />
            </>
          )}
          {desktopImagePostion === 'right' && (
            <>
              <DropZone zone="text-panel" disallow={['SideImage']} />
              <DropZone zone="image-panel" allow={['SideImage']} />
            </>
          )}
        </div>
      )}
    </>
  )
}

type Props = {
  HeadingBlock: { title: string }
  //Container: NonNullable<unknown>
  SideImage: { imageUrl: string }
}

type RootProps = {
  desktopImagePostion?: 'left' | 'right'
}

const config: Config<Props, RootProps> = {
  root: {
    fields: {
      desktopImagePostion: {
        label: 'Desktop Image Position',
        type: 'radio',
        options: [
          {
            label: 'Left',
            value: 'left',
          },
          {
            label: 'Right',
            value: 'right',
          },
        ],
      },
    },
    defaultProps: {
      desktopImagePostion: 'left',
    },

    render: ({ desktopImagePostion, puck }) => {
      return (
        <PuckWrapper puck={puck}>
          <RootComponent desktopImagePostion={desktopImagePostion} />
        </PuckWrapper>
      )
    },
  },
  components: {
    // Container: {
    //   fields: {},
    //   render: () => {
    //     return (
    //       <div
    //         style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}
    //       >
    //         <DropZone zone="left-column" />
    //         <DropZone zone="right-column" />
    //       </div>
    //     )
    //   },
    // },
    HeadingBlock: {
      fields: {
        title: { type: 'text' },
      },
      defaultProps: {
        title: 'Heading',
      },
      render: ({ title }) => (
        <div style={{ padding: 64 }}>
          <h1>{title}</h1>
        </div>
      ),
    },
    SideImage: {
      fields: {
        imageUrl: { type: 'text', label: 'Image URL' },
      },
      defaultProps: {
        imageUrl:
          'https://api.intentnow.com/cdn/shopify-images/snowboard-1.png',
      },
      render: ({ imageUrl }) => (
        <div
          style={{
            display: 'flex',
          }}
        >
          <img src={imageUrl} alt="Side" style={{ width: '100%' }} />
        </div>
      ),
    },
  },
}

const MyPlugin = {
  overrides: {
    iframe: ({
      children,
      document,
    }: {
      children: ReactNode
      document?: Document
    }) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      useEffect(() => {
        if (document) {
          document.body.setAttribute(
            'style',
            'background: lightgray; border: 2px dotted black;'
          )
        }
      }, [document])

      return <div>{children}</div>
    },
  },
}

export default function PuckEditorPocPage() {
  const initialData: any = {
    root: { props: { desktopImagePostion: 'left' } },
    content: [],
    zones: {
      'root:image-panel': [
        {
          type: 'SideImage',
          props: {
            imageUrl:
              'https://api.intentnow.com/cdn/shopify-images/snowboard-1.png',
            id: 'SideImage-39699ed5-52f8-48d0-8d7f-03c03657e6b9',
          },
        },
      ],
      'root:text-panel': [
        {
          type: 'HeadingBlock',
          props: {
            title: 'This is a title!!',
            id: 'HeadingBlock-86af9b14-3c48-4db1-81aa-ea735dd7ce81',
          },
        },
      ],
    },
  }

  // Save the data to your database
  const save = (data: any) => {
    console.log('save puck data', JSON.stringify(data))
  }

  return (
    <Puck
      headerTitle="Widget Dialog"
      config={config}
      data={initialData}
      onPublish={save}
      viewports={[
        {
          label: 'Desktop',
          width: 1000,
          height: 800,
          icon: 'Monitor',
        },
        {
          label: 'Desktop',
          width: 390,
          height: 700,
          icon: 'Smartphone',
        },
      ]}
      plugins={[MyPlugin]}
    />
  )

  // return (
  //   <Puck config={config} data={initialData} onPublish={save}>
  //     <div
  //       style={{ display: 'grid', gridTemplateColumns: '1fr 2fr', gridGap: 16 }}
  //     >
  //       <div>
  //         {/* Render the drag-and-drop preview */}
  //         <Puck.Preview />
  //       </div>
  //       <div>
  //         {/* Render the component list */}
  //         <Puck.Components />
  //       </div>
  //     </div>
  //   </Puck>
  // )
}
