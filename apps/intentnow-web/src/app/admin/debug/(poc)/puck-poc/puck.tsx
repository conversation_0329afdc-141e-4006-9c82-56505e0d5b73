import { PuckContext, usePuck } from '@measured/puck'
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react'

const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  })

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    window.addEventListener('resize', handleResize)

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return { screenSize }
}

function usePuckManager({ puck: pk }: { puck: PuckContext }) {
  const [puck, setPuck] = useState<PuckContext>(pk)
  const [screenSize, setScreenSize] = useState<{
    width: number
    height: number
  }>()
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    if (screenSize) {
      const mobile = screenSize.width < 600
      setIsMobile(mobile)
    }
  }, [screenSize])

  return {
    puck,
    setPuck,
    screenSize,
    setScreenSize,
    isMobile,
  }
}

type PuckProviderType = ReturnType<typeof usePuckManager>

export const PuckComponentContext = createContext<PuckProviderType | undefined>(
  undefined
)

export function PuckProvider({
  children,
  puck,
}: {
  children: ReactNode
  puck: PuckContext
}) {
  const context = usePuckManager({ puck })

  return (
    <PuckComponentContext.Provider value={context}>
      {children}
    </PuckComponentContext.Provider>
  )
}

export function usePuckContext() {
  const context = useContext(PuckComponentContext)
  if (!context) {
    throw new Error('usePuckContext must be used within a PuckProvider')
  }
  return context
}

export function PuckEditWrapper({ children }: { children: ReactNode }) {
  const { appState } = usePuck()
  const { setScreenSize } = usePuckContext()

  useEffect(() => {
    setScreenSize({
      width: appState.ui.viewports.current.width,
      height:
        typeof appState.ui.viewports.current.height === 'number'
          ? appState.ui.viewports.current.height
          : 0,
    })
  }, [appState, setScreenSize])

  return <>{children}</>
}

export function PuckRenderWrapper({ children }: { children: ReactNode }) {
  const { screenSize } = useScreenSize()
  const { setScreenSize } = usePuckContext()

  setScreenSize(screenSize)

  return <>{children}</>
}

export function PuckWrapper({
  puck,
  children,
}: {
  puck: PuckContext
  children: ReactNode
}) {
  if (puck.isEditing) {
    return (
      <PuckProvider puck={puck}>
        <PuckEditWrapper>{children}</PuckEditWrapper>
      </PuckProvider>
    )
  }

  return (
    <PuckProvider puck={puck}>
      <PuckRenderWrapper>{children}</PuckRenderWrapper>
    </PuckProvider>
  )
}
