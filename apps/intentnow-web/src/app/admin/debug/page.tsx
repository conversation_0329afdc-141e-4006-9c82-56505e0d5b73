'use client'
import { useAuthedApiFetch } from '@/modules/auth/fetch'
import { useAuth } from '@/modules/auth/auth'
import useSWR from 'swr'
import { Button } from '@mui/material'

export default function DebugPage() {
  const { authedApiFetch } = useAuthedApiFetch()
  const { user, signOut } = useAuth()
  const {} = useSWR(user ? `/api/private-hello` : null, authedApiFetch)

  return (
    <>
      <div>
        User: <pre>{JSON.stringify(user, null, 2)}</pre>
      </div>
      <div>
        <Button onClick={signOut}>Sign Out</Button>
      </div>
    </>
  )
}
