'use client'
import { appConfig } from '@/modules/config/app'
import { AdminLayout } from '../admin-layout'
import useSWR from 'swr'
import { apiFetch } from '@/modules/utils/fetch'

export default function AdminSystemPage() {
  const { appName, appEnv, gitVersion } = appConfig
  const { data } = useSWR<{
    app: {
      name: string
      environment: string
      gitVersion: string
    }
  }>('/api/hello', apiFetch)

  return (
    <AdminLayout title={`IntentNow Admin - System`}>
      <div>Web: {`${appEnv}/${appName}#${gitVersion}`}</div>
      <div>
        Server:{' '}
        {`${data?.app.environment}/${data?.app.name}#${data?.app.gitVersion}`}
      </div>
    </AdminLayout>
  )
}
