'use client'

import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Box,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  Typography,
  AccordionDetails,
  Button,
  Switch,
  Link,
  Card,
  CardHeader,
  CardContent,
  Checkbox,
  Grid2 as Grid,
  TextField,
} from '@mui/material'
import { ExpandMore as ExpandMoreIcon } from '@mui/icons-material'
import { AdminLayout } from '../admin-layout'
import { useMemo, useState } from 'react'
import {
  useFeatureSettingsForShop,
  useStoreAppSettingsForShop,
  useStoreConfigs,
  useStoreInfo,
  useStoreConfigByShop,
  useStoreImagesAdmin,
} from '@/modules/intentnow/settings'
import { GeneratedDiscountsAdmin } from '@/components/generated-discounts'
import { StoreAnalytics } from '@/components/store-analytics'
import { ShopifyGraphQl } from '@/components/shopify-graphql'
import { useShopifyQueries } from '@/modules/shopify/query'
import { ShopifyGraphRest } from '@/components/shoipify-rest'
import { JSONTree, KeyPath } from 'react-json-tree'
import {
  legacyWidgetStores,
  ShopifyStore,
  WidgetType,
} from '@packages/shared-entities'
import DeleteIcon from '@mui/icons-material/Delete'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import { ConfirmDialog } from '@/components/confirm-dialog'
import {
  generateInjectWidgetScript,
  getPreviewWidgetUrl,
} from '@/modules/intentnow/widgets'
import { StoreImageList, StoreImageUploadForm } from '@/components/store-images'
import { JsonFormsDialog } from '@/components/jsonforms/jsonforms-dialog'
import {
  shopifyStoreJsonSchema,
  shopifyStoreUiSchema,
} from '@/modules/intentnow/forms'
import { LaunchConfigs } from '@/components/launch-configs'
import { appConfig } from '@/modules/config/app'

export default function AdminStoresPage() {
  const { adminShopifyGraphQlQuery, adminShopifyRestQuery } =
    useShopifyQueries()
  const [shop, setShop] = useState<string>('')
  const [tab, setTab] = useState<string>('status')
  const { storeConfigs } = useStoreConfigs()
  const {
    storeConfig,
    mutate,
    updateStoreConfig,
    createStoreWidget,
    deleteStoreWidget,
    duplicateStoreWidget,
  } = useStoreConfigByShop(shop)
  const [widgetIdToDelete, setWidgetIdToDelete] = useState<string>()
  const [storeFormData, setStoreFormData] = useState<ShopifyStore>()

  const { data: storeInfo } = useStoreInfo(
    storeConfig?.appHandle,
    !storeConfig?.pendingInstall ? storeConfig?.shop : undefined
  )
  const { data: appSettings } = useStoreAppSettingsForShop(
    storeConfig?.appHandle,
    !storeConfig?.pendingInstall ? storeConfig?.shop : undefined
  )
  const { data: expSettings } = useFeatureSettingsForShop(
    storeConfig?.appHandle,
    !storeConfig?.pendingInstall ? storeConfig?.shop : undefined
  )

  const { mutate: mutateStoreImages } = useStoreImagesAdmin(shop)

  const isLegacyStore = useMemo(() => {
    return storeConfig?.shop && legacyWidgetStores.includes(storeConfig.shop)
  }, [storeConfig])

  const { injectWidgetScript, injectable } = useMemo(() => {
    if (shop) {
      const injectable = true
      const injectWidgetScript = injectable
        ? generateInjectWidgetScript(
            shop,
            storeConfig?.appHandle ?? 'mock-app-handle',
            appConfig.tagVersion
          )
        : undefined
      return { injectWidgetScript, injectable }
    } else {
      return {}
    }
  }, [shop, storeConfig])

  const livePreviewLink = getPreviewWidgetUrl(
    storeConfig?.shop,
    storeConfig?.website,
    undefined
  )
  const firestoreDbPath =
    appConfig.appEnv === 'production' ? '-default-' : 'staging'

  return (
    <AdminLayout
      title={
        shop ? `IntentNow Admin - Stores - ${shop}` : `IntentNow Admin - Store`
      }
    >
      <Box>
        <FormControl
          variant="standard"
          sx={{ m: 1, maxWidth: 400, minWidth: 300 }}
        >
          <InputLabel id="demo-simple-select-label">Select Store</InputLabel>
          <Select
            labelId="demo-simple-select-label"
            id="demo-simple-select"
            value={shop}
            label="Store"
            disabled={!storeConfigs}
            onChange={(e) => {
              setShop(e.target.value)
            }}
          >
            {storeConfigs &&
              storeConfigs.map((storeConfig, index) => (
                <MenuItem key={index} value={storeConfig.shop}>
                  {storeConfig.name
                    ? `${storeConfig.name} [${storeConfig.shop}]`
                    : storeConfig.shop}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
        {shop && storeConfig && (
          <>
            <Box
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                height: '100%',
              }}
            >
              <Tabs
                value={tab}
                variant="scrollable"
                scrollButtons="auto"
                onChange={(e, value) => {
                  setTab(value)
                }}
              >
                <Tab label="Status" value="status" />
                <Tab label="Config" value="config" />
                <Tab label="Shopify App" value="shopify-app" />
                <Tab label="Widget & Discount Config" value="widget" />
                <Tab label="Image Assets" value="images" />
                <Tab label="Launch Config" value="launch-config" />
                <Tab label="Generated Discounts" value="discounts" />
                <Tab label="Analytics" value="analytics" />
                <Tab label="Inject Widget" value="inject-widget" />
                <Tab label="Shopify GraphQL" value="shopify-graphql" />
                <Tab label="Shopify REST" value="shopify-rest" />
              </Tabs>
            </Box>
            {tab === 'status' && (
              <Box>
                <Box>
                  {'IntentNow app installed '}
                  <Switch checked={Boolean(storeInfo)} disabled />
                </Box>
                <Box>
                  {'web pixel installed '}
                  <Switch checked={Boolean(appSettings?.webPixel)} disabled />
                </Box>
                <Box>
                  {'promo widget installed '}
                  <Switch
                    checked={Boolean(appSettings?.promoEmbedActivated)}
                    disabled
                  />
                </Box>
                <Box>
                  {'store config created '}
                  <Switch checked={Boolean(storeConfig)} disabled />
                </Box>
                {!!expSettings && (
                  <>
                    <Box>
                      save-events{' '}
                      <Switch
                        checked={
                          expSettings.storeConfig.isEnabled &&
                          expSettings.storeConfig.currentValue.saveEvents
                        }
                        onClick={() => {
                          window.open(
                            `https://console.statsig.com/2KQQHsH1BL4DV1I2qspjUC/dynamic_configs/${expSettings.storeConfig.id}`,
                            '_blank'
                          )
                        }}
                      />
                    </Box>
                    <Box>
                      send-amplitude{' '}
                      <Switch
                        checked={
                          expSettings.storeConfig.isEnabled &&
                          expSettings.storeConfig.currentValue
                            .sendAmplitudeEvents
                        }
                        onClick={() => {
                          window.open(
                            `https://console.statsig.com/2KQQHsH1BL4DV1I2qspjUC/dynamic_configs/${expSettings.storeConfig.id}`,
                            '_blank'
                          )
                        }}
                      />
                    </Box>
                    <Box>
                      get-discount{' '}
                      <Switch
                        checked={
                          expSettings.storeConfig.isEnabled &&
                          expSettings.storeConfig.currentValue.getDiscount
                        }
                        onClick={() => {
                          window.open(
                            `https://console.statsig.com/2KQQHsH1BL4DV1I2qspjUC/dynamic_configs/${expSettings.storeConfig.id}`,
                            '_blank'
                          )
                        }}
                      />
                    </Box>
                    <Box>
                      prediction{' '}
                      <Switch
                        checked={
                          expSettings.storeConfig.isEnabled &&
                          expSettings.storeConfig.currentValue.predictWithEvents
                        }
                        onClick={() => {
                          window.open(
                            `https://console.statsig.com/2KQQHsH1BL4DV1I2qspjUC/dynamic_configs/${expSettings.storeConfig.id}`,
                            '_blank'
                          )
                        }}
                      />
                    </Box>
                    <Box>
                      model-experiment{' '}
                      <Switch
                        checked={
                          expSettings.modelExperiment.status === 'active'
                        }
                        onClick={() => {
                          window.open(
                            `https://console.statsig.com/2KQQHsH1BL4DV1I2qspjUC/experiments/${expSettings.modelExperiment.id}`,
                            '_blank'
                          )
                        }}
                      />
                    </Box>
                    <Box>
                      event-shadow-ratio{' '}
                      <a
                        target="_blank"
                        href={`https://console.statsig.com/2KQQHsH1BL4DV1I2qspjUC/dynamic_configs/${expSettings.storeConfig.id}`}
                      >
                        {expSettings.storeConfig.currentValue.eventShadowRatio}
                      </a>
                    </Box>
                  </>
                )}
              </Box>
            )}
            {tab === 'config' && (
              <Box>
                <Accordion defaultExpanded={true}>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1-content"
                    id="panel1-header"
                  >
                    <Typography component="span">Store Config</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box>
                      {!!storeConfig && (
                        <>
                          <JsonFormsDialog
                            open={Boolean(storeFormData)}
                            title="Update Store Config"
                            description=""
                            dataSchema={shopifyStoreJsonSchema}
                            uiSchema={shopifyStoreUiSchema}
                            formData={storeFormData}
                            onSave={async (data: Partial<ShopifyStore>) => {
                              setStoreFormData(undefined)
                              if (data) {
                                const updateData = {
                                  appHandle: data.appHandle ?? '',
                                  name: data.name ?? '',
                                  website: data.website ?? '',
                                  pendingInstall: data.pendingInstall,
                                  promoConfig: {
                                    model: {
                                      // ...(data.promoConfig?.model?.overrides
                                      //   ? {
                                      //       overrides:
                                      //         data.promoConfig.model.overrides,
                                      //     }
                                      //   : {}),
                                      ...(data.promoConfig?.model?.parameters
                                        ? {
                                            parameters:
                                              data.promoConfig.model.parameters,
                                          }
                                        : {}),
                                    },
                                  },
                                  analyticsConfig: {
                                    ...(data.analyticsConfig?.chartIds
                                      ? {
                                          chartIds:
                                            data.analyticsConfig.chartIds,
                                        }
                                      : {}),
                                    ...(data.analyticsConfig?.internalDashboards
                                      ? {
                                          internalDashboards:
                                            data.analyticsConfig
                                              .internalDashboards,
                                        }
                                      : {}),
                                  },
                                }
                                await updateStoreConfig(updateData)
                                await mutate()
                              }
                            }}
                            onCancel={() => {
                              setStoreFormData(undefined)
                            }}
                          />
                          <Button
                            onClick={() => {
                              setStoreFormData(storeConfig)
                            }}
                          >
                            Update
                          </Button>
                          <Button
                            onClick={() => {
                              window.open(
                                `https://console.cloud.google.com/firestore/databases/${firestoreDbPath}/data/panel/ShopifyStores/${storeConfig._id}`,
                                '_blank'
                              )
                            }}
                          >
                            See in DB
                          </Button>
                          <JSONTree
                            data={{
                              ...storeConfig,
                            }}
                            shouldExpandNodeInitially={(
                              keyPath: KeyPath,
                              data: unknown,
                              level: number
                            ) => level <= 1}
                          />
                        </>
                      )}
                    </Box>
                  </AccordionDetails>
                </Accordion>
                <Accordion defaultExpanded={true}>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1-content"
                    id="panel1-header"
                  >
                    <Typography component="span">Launch Status</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box>
                      {!!expSettings && (
                        <>
                          <div>
                            get-discount{' '}
                            <Switch
                              checked={
                                expSettings.storeConfig.isEnabled &&
                                expSettings.storeConfig.currentValue.getDiscount
                              }
                              onClick={() => {
                                window.open(
                                  `https://console.statsig.com/2KQQHsH1BL4DV1I2qspjUC/dynamic_configs/${expSettings.storeConfig.id}`,
                                  '_blank'
                                )
                              }}
                            />
                          </div>
                          <div>
                            prediction{' '}
                            <Switch
                              checked={
                                expSettings.storeConfig.isEnabled &&
                                expSettings.storeConfig.currentValue
                                  .predictWithEvents
                              }
                              onClick={() => {
                                window.open(
                                  `https://console.statsig.com/2KQQHsH1BL4DV1I2qspjUC/dynamic_configs/${expSettings.storeConfig.id}`,
                                  '_blank'
                                )
                              }}
                            />
                          </div>
                          <div>
                            model-experiment{' '}
                            <Switch
                              checked={
                                expSettings.modelExperiment.status === 'active'
                              }
                              onClick={() => {
                                window.open(
                                  `https://console.statsig.com/2KQQHsH1BL4DV1I2qspjUC/experiments/${expSettings.modelExperiment.id}`,
                                  '_blank'
                                )
                              }}
                            />
                          </div>
                          <JSONTree
                            data={expSettings ?? {}}
                            shouldExpandNodeInitially={(
                              keyPath: KeyPath,
                              data: unknown,
                              level: number
                            ) => level <= 1}
                          />
                        </>
                      )}
                    </Box>
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}
            {tab === 'shopify-app' && (
              <Box>
                {Boolean(storeConfig?.pendingInstall || !storeInfo) && (
                  <Box>
                    <Typography component="span">
                      IntentNow Shopify app is not installed on this store.
                    </Typography>
                  </Box>
                )}
                {Boolean(!storeConfig?.pendingInstall && storeInfo) && (
                  <>
                    <Accordion defaultExpanded={true}>
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        aria-controls="panel1-content"
                        id="panel1-header"
                      >
                        <Typography component="span">
                          Shopify Store Info
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Box>
                          {!!storeInfo && (
                            <JSONTree
                              data={storeInfo ?? {}}
                              shouldExpandNodeInitially={(
                                keyPath: KeyPath,
                                data: unknown,
                                level: number
                              ) => level <= 1}
                            />
                          )}
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                    <Accordion defaultExpanded={true}>
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        aria-controls="panel1-content"
                        id="panel1-header"
                      >
                        <Typography component="span">
                          Shopify App Status
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Box>
                          <Box>
                            {'web pixel '}
                            <Switch
                              checked={Boolean(appSettings?.webPixel)}
                              disabled
                            />
                          </Box>
                          <Box>
                            {'promo widget '}
                            <Switch
                              checked={Boolean(
                                appSettings?.promoEmbedActivated
                              )}
                              disabled
                            />
                          </Box>
                          {!!appSettings && (
                            <JSONTree
                              data={appSettings ?? {}}
                              shouldExpandNodeInitially={(
                                keyPath: KeyPath,
                                data: unknown,
                                level: number
                              ) => level <= 1}
                            />
                          )}
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                  </>
                )}
              </Box>
            )}
            {tab === 'widget' && (
              <>
                {(isLegacyStore || storeConfig.promoConfig?.widget2) && (
                  <Accordion defaultExpanded={true}>
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls="panel1-content"
                      id="panel1-header"
                    >
                      <Typography component="span">Legacy Widget</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Box
                        sx={{
                          padding: '5px',
                        }}
                      >
                        <Button
                          variant="contained"
                          onClick={() => {
                            window.open(
                              `/admin/widget-edit?shop=${shop}`,
                              '_blank'
                            )
                          }}
                        >
                          Manage Widget & Discount Config
                        </Button>
                        <Button
                          variant="contained"
                          sx={{
                            marginLeft: '5px',
                          }}
                          onClick={() => {
                            window.open(livePreviewLink, '_blank')
                          }}
                        >
                          Live Preview
                        </Button>
                      </Box>
                    </AccordionDetails>
                  </Accordion>
                )}
                <Accordion defaultExpanded={true}>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1-content"
                    id="panel1-header"
                  >
                    <Typography component="span">New Widgets</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      <Box
                        sx={{
                          padding: '5px',
                        }}
                      >
                        <Button
                          variant="contained"
                          sx={{
                            marginLeft: '5px',
                          }}
                          disabled={!storeConfig._widgets?.length}
                          onClick={() => {
                            window.open(
                              getPreviewWidgetUrl(
                                storeConfig.shop,
                                storeConfig.website,
                                undefined
                              ),
                              '_blank'
                            )
                          }}
                        >
                          Live Preview
                        </Button>
                        {/* <Button
                        variant="contained"
                        sx={{
                          marginLeft: '5px',
                        }}
                        onClick={async () => {
                          await createStoreWidget(shop, WidgetType.widget2)
                          await mutate()
                        }}
                      >
                        Create V2 Widget
                      </Button> */}
                        <Button
                          variant="contained"
                          sx={{
                            marginLeft: '5px',
                          }}
                          onClick={async () => {
                            await createStoreWidget(WidgetType.widget3)
                            await mutate()
                          }}
                        >
                          Create New Widget
                        </Button>
                      </Box>
                      <Box>
                        {storeConfig._widgets?.map((widget, index) => {
                          const isSelected =
                            widget._id ===
                            storeConfig.promoConfig?.selectedWidgetId
                          return (
                            <Card
                              key={index}
                              sx={{
                                margin: '5px',
                              }}
                            >
                              <CardHeader
                                title={`${widget.name} [${widget._id}]`}
                              />
                              <CardContent>
                                <Grid
                                  container
                                  spacing={2}
                                  sx={{
                                    alignItems: 'center',
                                  }}
                                >
                                  <Grid size={2}>
                                    <Button
                                      onClick={async () => {
                                        await updateStoreConfig({
                                          promoConfig: {
                                            selectedWidgetId: isSelected
                                              ? ''
                                              : widget?._id,
                                          },
                                        })
                                        await mutate()
                                      }}
                                    >
                                      <Checkbox checked={isSelected} />
                                      {isSelected ? 'In Use' : 'Unselected'}
                                    </Button>
                                  </Grid>
                                  <Grid size={2}>
                                    <Button
                                      variant="outlined"
                                      onClick={() => {
                                        if (
                                          widget.type === WidgetType.widget2
                                        ) {
                                          window.open(
                                            `/admin/store-widget2-edit?shop=${shop}&widget-id=${widget._id}`,
                                            '_blank'
                                          )
                                        } else if (
                                          widget.type === WidgetType.widget3
                                        ) {
                                          window.open(
                                            `/admin/store-widget3-edit?shop=${shop}&widget-id=${widget._id}`,
                                            '_blank'
                                          )
                                        }
                                      }}
                                    >
                                      Edit Widget
                                    </Button>
                                  </Grid>
                                  <Grid size={2}>
                                    <Button
                                      variant="outlined"
                                      onClick={() => {
                                        window.open(
                                          getPreviewWidgetUrl(
                                            storeConfig.shop,
                                            storeConfig.website,
                                            widget
                                          ),
                                          '_blank'
                                        )
                                      }}
                                    >
                                      Live Preview
                                    </Button>
                                  </Grid>
                                  <Grid size={2}>
                                    <Button
                                      variant="outlined"
                                      onClick={async () => {
                                        try {
                                          await duplicateStoreWidget(widget._id)
                                        } finally {
                                          await mutate()
                                        }
                                      }}
                                    >
                                      <ContentCopyIcon />
                                    </Button>
                                  </Grid>
                                  <Grid size={2}>
                                    <Button
                                      variant="outlined"
                                      onClick={() => {
                                        if (!widgetIdToDelete) {
                                          setWidgetIdToDelete(widget._id)
                                        }
                                      }}
                                    >
                                      <DeleteIcon />
                                    </Button>
                                  </Grid>
                                </Grid>
                              </CardContent>
                            </Card>
                          )
                        })}
                        <ConfirmDialog
                          title="Delete Widget"
                          description={`You are about to delete widget "${storeConfig?._widgets?.find((w) => w._id === widgetIdToDelete)?.name}". Deleted widget can't be recovered. Do you want to continue?`}
                          open={Boolean(widgetIdToDelete)}
                          defaultAnswer={false}
                          confirm={async (answer) => {
                            try {
                              if (answer && widgetIdToDelete) {
                                await deleteStoreWidget(widgetIdToDelete)
                              }
                            } finally {
                              setWidgetIdToDelete(undefined)
                              await mutate()
                            }
                          }}
                        />
                      </Box>
                    </Box>
                  </AccordionDetails>
                </Accordion>
              </>
            )}

            {tab === 'images' && (
              <Box width="100%">
                <Box margin="5px">
                  <StoreImageUploadForm
                    shop={shop}
                    onUpload={async () => {
                      await mutateStoreImages()
                    }}
                  />
                </Box>
                <Box margin="20px" />
                <Box margin="5px">
                  <StoreImageList shop={shop} />
                </Box>
              </Box>
            )}
            {tab === 'launch-config' && (
              <Box width="100%">
                <Box margin="5px">
                  <LaunchConfigs shop={shop} />
                </Box>
              </Box>
            )}
            {tab === 'discounts' && (
              <>
                <GeneratedDiscountsAdmin
                  shop={!storeConfig?.pendingInstall ? shop : undefined}
                  appHandle={storeConfig?.appHandle}
                />
              </>
            )}
            {tab === 'analytics' && (
              <Box>
                <Accordion defaultExpanded={true}>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1-content"
                    id="panel1-header"
                  >
                    <Typography component="span">
                      Internal Dashboards
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box>
                      {storeConfig.analyticsConfig?.internalDashboards?.map(
                        (dash, index) => (
                          <div key={index}>
                            <Link href={dash.link} target="_blank">
                              {dash.name}
                            </Link>
                          </div>
                        )
                      )}
                    </Box>
                  </AccordionDetails>
                </Accordion>
                <Accordion defaultExpanded={true}>
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1-content"
                    id="panel1-header"
                  >
                    <Typography component="span">Merchant Analytics</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box>
                      <StoreAnalytics
                        analyticsConfig={storeConfig.analyticsConfig}
                      />
                    </Box>
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}
            {tab === 'inject-widget' && (
              <>
                <Box
                  display="flex"
                  flexDirection="column"
                  style={{
                    maxWidth: '1000px',
                    width: '100%',
                  }}
                >
                  <Card
                    style={{
                      marginTop: '10px',
                    }}
                  >
                    <CardHeader title="Prerequisites" />
                    <CardContent>
                      <ul>
                        <li>{`IntentNow promo widget hasn't been turned on`}</li>
                      </ul>
                    </CardContent>
                  </Card>
                  {injectable && (
                    <>
                      <Card
                        style={{
                          marginTop: '10px',
                        }}
                      >
                        <CardHeader title="Step 1: prepare the Chrome Debugger" />
                        <CardContent>
                          <ul>
                            <li>
                              <a
                                href={livePreviewLink}
                                target="_blank"
                              >{`Open the live view link of the merchant site (with the "intentnow-preview=true") parameter`}</a>
                            </li>
                            <li>{`Run "allow pasting" command in the debugger console`}</li>
                          </ul>
                        </CardContent>
                      </Card>
                      <Card
                        style={{
                          marginTop: '10px',
                        }}
                      >
                        <CardHeader title="Step 2: copy paste script and run in the console" />
                        <CardContent>
                          <Box>
                            <Button
                              variant="outlined"
                              onClick={async () => {
                                await navigator.clipboard.writeText(
                                  injectWidgetScript ?? ''
                                )
                              }}
                            >
                              <ContentCopyIcon />
                            </Button>
                          </Box>
                          <TextField
                            multiline
                            maxRows={20}
                            style={{
                              width: '100%',
                            }}
                            value={injectWidgetScript}
                          />
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader title="Step 3 (optional): use Chrome extension to auto-run the above script" />
                        <CardContent>
                          <ul>
                            <li>
                              {`Enable Chrome Extensions "developer mode": chrome://extensions/`}
                            </li>
                            <li>
                              <a
                                href="https://chromewebstore.google.com/detail/user-javascript-and-css/nbhcbdghjpllgmfilhnhkllmkecfmpld?hl=en-US"
                                target="_blank"
                              >{`Install the "User JavaScript and CSS" Chrome extension`}</a>
                            </li>
                            <li>{`Set up a URL based rule in "User JavaScript and CSS" extension to auto-load the script`}</li>
                          </ul>
                        </CardContent>
                      </Card>
                    </>
                  )}
                </Box>
              </>
            )}
            {tab === 'shopify-graphql' && (
              <>
                <ShopifyGraphQl
                  shop={shop}
                  queryFunc={async (
                    shop: string,
                    query: string,
                    variables: any
                  ) => {
                    return await adminShopifyGraphQlQuery(
                      storeConfig.appHandle,
                      shop,
                      {
                        query,
                        variables,
                      }
                    )
                  }}
                />
              </>
            )}
            {tab === 'shopify-rest' && (
              <>
                <ShopifyGraphRest
                  shop={shop}
                  queryFunc={async (shop, op, path, query, data) => {
                    return await adminShopifyRestQuery(
                      storeConfig.appHandle,
                      shop,
                      {
                        op,
                        path,
                        query,
                        data,
                      }
                    )
                  }}
                />
              </>
            )}
          </>
        )}
      </Box>
    </AdminLayout>
  )
}
