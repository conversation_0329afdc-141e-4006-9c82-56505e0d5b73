import './global.css'
import { AdminApp } from './admin-app'
import { Clerk<PERSON>rovider } from '@clerk/nextjs'

export const metadata = {
  title: 'IntentNow Admin',
  description: '',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    //TODO: We put an null ClerkProvider here so we can support both Clerk and Firebase Auth in useAuth() temporarily until we retire the Firebase Auth completely
    <ClerkProvider publishableKey="pk_test_YWJvdmUtZ29ibGluLTQxLmNsZXJrLmFjY291bnRzLmRldiQ">
      <html lang="en">
        <body>
          <AdminApp>{children}</AdminApp>
        </body>
      </html>
    </ClerkProvider>
  )
}
