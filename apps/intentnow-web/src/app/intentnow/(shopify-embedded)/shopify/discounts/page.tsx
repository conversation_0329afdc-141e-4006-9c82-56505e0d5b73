'use client'
import { Box, Typography } from '@mui/material'
import { useShopifyApp } from '@/modules/shopify/app'
import { GeneratedDiscountsEmbedded } from '@/components/generated-discounts'

export default function DiscountsPage() {
  const shopifyApp = useShopifyApp()

  if (!shopifyApp) {
    return <></>
  }

  return (
    <Box justifyItems="center">
      <Box minWidth={250} width={1500} maxWidth="95%">
        <Typography fontSize={30}>Generated Discounts</Typography>
        <Typography fontSize={15}>{shopifyApp?.shop}</Typography>
        <br />

        <GeneratedDiscountsEmbedded />

        <br />
      </Box>
    </Box>
  )
}
