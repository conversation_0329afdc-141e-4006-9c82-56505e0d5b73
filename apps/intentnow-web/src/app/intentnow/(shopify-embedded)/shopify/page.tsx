'use client'
import { useShopifyApp } from '@/modules/shopify/app'
import {
  shopifyApiDeleteFetch,
  shopifyApiPostFetch,
} from '@/modules/shopify/fetch'
import { useState } from 'react'
import {
  Box,
  Button,
  CircularProgress,
  Paper,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
//import Link from 'next/link'
import { useStoreAppSettings } from '@/modules/intentnow/settings'
import { useAdminAppConfig } from '@/modules/config/dynamic-config'
import {
  DeleteWebPixelResponseDto,
  LinkStoreRequestDto,
  WebPixelDto,
} from '@packages/shared-entities'

export default function SettingsPage() {
  const shopifyApp = useShopifyApp()
  const [processing, setProcessing] = useState(false)
  const { merchantPortal } = useAdminAppConfig()

  const {
    data: appSettings,
    mutate: mutateAppStatus,
    isLoading: loadingAppStatus,
    error: errorAppStatus,
  } = useStoreAppSettings()

  const webPixelOn = !!appSettings?.webPixel
  const promoWidgetOn = appSettings?.promoEmbedActivated
  const loading = loadingAppStatus

  async function enableWebPixel() {
    console.debug('enableWebPixel called')
    await shopifyApiPostFetch<WebPixelDto>(shopifyApp)([
      '/intentnow/web-pixel',
      {},
    ])
    await mutateAppStatus()
  }

  async function deleteWebPixel() {
    console.debug('deleteWebPixel called')
    await shopifyApiDeleteFetch<DeleteWebPixelResponseDto>(shopifyApp)(
      '/intentnow/web-pixel'
    )
    await mutateAppStatus()
  }

  async function createLinkStoreRequest() {
    const linkStoreRequest = await shopifyApiPostFetch<LinkStoreRequestDto>(
      shopifyApp
    )(['/intentnow/create-link-store-request', {}])
    window.open(`/merchant/link-store/${linkStoreRequest._id}`, '_blank')
  }

  return (
    <>
      {merchantPortal && (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'left',
            height: '100%',
            marginLeft: '40px',
          }}
        >
          {appSettings && (
            <>
              {appSettings?.merchantPortal?.linkedStoreId && (
                <>
                  <Typography variant="h6">
                    This store has been linked to an IntentNow Merchant Account.
                  </Typography>
                  <Box
                    sx={{
                      width: '100%',
                      display: 'flex',
                      marginTop: '20px',
                    }}
                  >
                    <Button
                      variant="contained"
                      onClick={() => {
                        window.open(
                          `/merchant/store/${appSettings.merchantPortal!.linkedStoreId}`,
                          '_blank'
                        )
                      }}
                    >
                      Open Store Dashboard
                    </Button>
                  </Box>
                </>
              )}
              {!appSettings?.merchantPortal?.linkedStoreId && (
                <>
                  <Typography variant="h6">
                    You need to link this store into an IntentNow merchant
                    account.
                  </Typography>
                  <Box
                    sx={{
                      width: '100%',
                      display: 'flex',
                      marginTop: '20px',
                    }}
                  >
                    <Button
                      variant="contained"
                      onClick={createLinkStoreRequest}
                    >
                      Link to an IntentNow Account
                    </Button>
                  </Box>
                </>
              )}
            </>
          )}
          {!appSettings && (
            <>
              <CircularProgress />
            </>
          )}
        </Box>
      )}
      {!merchantPortal && (
        <Box justifyItems="center">
          <Box minWidth={250} width={700} maxWidth="90%">
            <Typography fontSize={30}>IntentNow Settings</Typography>
            <Typography fontSize={15}>{shopifyApp?.shop}</Typography>
            <br />
            <TableContainer component={Paper} sx={{ width: '100%' }}>
              <Table>
                <TableBody sx={{ fontSize: 16 }}>
                  <TableRow>
                    <TableCell>Web Pixel</TableCell>
                    <TableCell>
                      {loading && <CircularProgress />}
                      {!loading && (
                        <Switch
                          checked={webPixelOn}
                          disabled={processing || loading || errorAppStatus}
                          onClick={async () => {
                            if (processing || !appSettings) {
                              return
                            }

                            setProcessing(true)
                            if (appSettings.webPixel) {
                              await deleteWebPixel()
                            } else {
                              await enableWebPixel()
                            }
                            setProcessing(false)
                          }}
                        ></Switch>
                      )}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>Promo Widget</TableCell>
                    <TableCell>
                      {loading && <CircularProgress />}
                      {!loading && (
                        <Switch
                          checked={promoWidgetOn}
                          disabled={
                            processing ||
                            loading ||
                            errorAppStatus ||
                            !appSettings?.promoEmbedActivationLink
                          }
                          onClick={async () => {
                            if (processing || !appSettings) {
                              return
                            }

                            setProcessing(true)
                            window.open(
                              appSettings.promoEmbedActivationLink,
                              '_blank'
                            )
                            await mutateAppStatus()
                            setProcessing(false)
                          }}
                        ></Switch>
                      )}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </Box>
      )}
    </>
  )
}
