'use client'
import { StoreAnalytics } from '@/components/store-analytics'
import { useStoreAnalyticsSettings } from '@/modules/intentnow/settings'
import { useShopifyApp } from '@/modules/shopify/app'
import { Box, Typography } from '@mui/material'

export default function AnalyticsPage() {
  const shopifyApp = useShopifyApp()

  const { data: analyticsConfig } = useStoreAnalyticsSettings()

  if (!shopifyApp) {
    return <></>
  }

  return (
    <Box justifyItems="center">
      <Box minWidth={250} width={1500} maxWidth="95%">
        <Typography fontSize={30}>Analytics</Typography>
        <Typography fontSize={15}>{shopifyApp?.shop}</Typography>
        <br />

        <StoreAnalytics analyticsConfig={analyticsConfig} />
      </Box>
    </Box>
  )
}
