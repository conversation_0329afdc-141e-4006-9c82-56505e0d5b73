'use client'
import { useAdminAppConfig } from '@/modules/config/dynamic-config'
import { NavigationMenu } from '@shopify/app-bridge-react'

export function IntentnowShopifyNavMenu() {
  const navList = []
  const { debug, discounts, analytics } = useAdminAppConfig()

  if (debug || discounts || analytics) {
    navList.push({
      label: 'Settings',
      destination: '/intentnow/shopify',
    })
  }

  if (discounts) {
    navList.push({
      label: 'Discounts',
      destination: '/intentnow/shopify/discounts',
    })
  }

  if (analytics) {
    navList.push({
      label: 'Analytics',
      destination: '/intentnow/shopify/analytics',
    })
  }

  if (debug) {
    navList.push({
      label: 'Debug',
      destination: '/intentnow/shopify-debug',
    })
  }

  return (
    <>
      {typeof window !== 'undefined' && (
        <NavigationMenu navigationLinks={navList} />
      )}
    </>
  )
}
