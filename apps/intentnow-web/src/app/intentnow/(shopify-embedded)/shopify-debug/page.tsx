'use client'
import {
  useStoreAnalyticsSettings,
  useStoreAppSettings,
} from '@/modules/intentnow/settings'
import { useShopifyApp } from '@/modules/shopify/app'
import {
  shopifyApiDeleteFetch,
  shopifyApiFetch,
  shopifyApiPostFetch,
} from '@/modules/shopify/fetch'
//import Link from 'next/link'
import useSWR from 'swr'

export default function DebugPage() {
  const shopifyApp = useShopifyApp()
  const { data: debugInfo } = useSWR(
    shopifyApp ? `/debug-info` : null,
    shopifyApiFetch<any>(shopifyApp)
  )
  const { data: appSettings, mutate: mutateAppStatus } = useStoreAppSettings()
  const { data: analyticsSettings } = useStoreAnalyticsSettings()

  async function enableWebPixel() {
    console.debug('enableWebPixel called')
    await shopifyApiPostFetch<any>(shopifyApp)(['/intentnow/web-pixel', {}])
    await mutateAppStatus()
  }

  async function deleteWebPixel() {
    console.debug('deleteWebPixel called')
    await shopifyApiDeleteFetch<any>(shopifyApp)('/intentnow/web-pixel')
    await mutateAppStatus()
  }

  return (
    <>
      <h2>IntentNow Shopify Admin Debug Page</h2>
      <div>shop: {shopifyApp?.shop}</div>
      <br />
      <div>debug info:</div>
      <div>
        <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
      </div>
      <br />
      <div>app settings:</div>
      <div>
        <pre>{JSON.stringify(appSettings, undefined, 2)}</pre>
      </div>
      <br />
      <div>analytics settings:</div>
      <div>
        <pre>{JSON.stringify(analyticsSettings, undefined, 2)}</pre>
      </div>
      {/* <div>
        <Link href="/intentnow/shopify-debug/graphql">GraphQL Console</Link>
      </div> */}
      <br />
      <div>
        <button onClick={enableWebPixel}>Enable Web Pixel</button>
      </div>
      <br />
      <div>
        <button onClick={deleteWebPixel}>Delete Web Pixel</button>
      </div>
    </>
  )
}
