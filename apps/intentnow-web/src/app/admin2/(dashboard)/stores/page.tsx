'use client'

import {
  Box,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  TextField,
  Typography,
  Grid2 as Grid,
  <PERSON>alogA<PERSON>,
  Button,
} from '@mui/material'
import { StoreCreateDto, StoreDto } from '@packages/shared-entities'
import { refineConfig } from '@/modules/config/refine'
import { List, SaveButton, useDataGrid } from '@refinedev/mui'
import { useMemo, useState } from 'react'
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridValidRowModel,
} from '@mui/x-data-grid'
import { OpenInNew as OpenInNewIcon, SearchOutlined } from '@mui/icons-material'
import { debounce } from 'lodash'
import { HttpError } from '@refinedev/core'
import {
  useModalForm,
  UseModalFormReturnType,
} from '@refinedev/react-hook-form'

export default function AdminStoresPage() {
  const resource = 'stores'
  const [search, setSearch] = useState('')

  const createStoreModalFormProps = useModalForm<
    StoreCreateDto,
    HttpError,
    StoreCreateDto
  >({
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
    modalProps: {},
    refineCoreProps: {
      resource,
      action: 'create',
      onMutationSuccess: async () => {},
    },
    syncWithLocation: false,
    warnWhenUnsavedChanges: true,
    resetOptions: {
      keepDirty: false,
      keepDefaultValues: false,
      keepErrors: false,
      keepTouched: false,
      keepIsValid: false,
      keepIsSubmitted: false,
      keepSubmitCount: false,
    },
  })
  const {
    modal: { show: showCreateModal, visible: createModalVisible },
    reset: resetCreateModal,
  } = createStoreModalFormProps

  const { dataGridProps, setFilters } = useDataGrid<StoreDto>({
    resource,
    sorters: { initial: [{ field: 'name', order: 'asc' }] },
    filters: {
      initial: [],
    },
    syncWithLocation: false,
    queryOptions: {
      ...refineConfig.defaultUseListQueryOptions,
    },
  })

  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'name',
        headerName: 'Store Name',
        flex: 1,
        sortable: true,
        filterable: false,
      },
      {
        field: 'website',
        headerName: 'Website',
        flex: 1,
        sortable: true,
        filterable: false,
        renderCell: (params) => {
          return (
            <a href={params.row.website} target="_blank">
              {params.row.website}
            </a>
          )
        },
      },
      {
        field: 'shopifyConfig',
        headerName: 'Shopify Config',
        flex: 1,
        sortable: true,
        filterable: false,
        renderCell: (params) => {
          return (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Typography>
                {params.row.shopifyConfig?.myshopifyDomain}
              </Typography>
              <Typography>{params.row.shopifyConfig?.appHandle}</Typography>
            </Box>
          )
        },
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Action',
        width: 150,
        sortable: false,
        filterable: false,
        getActions: ({ row }) => [
          <GridActionsCellItem
            key={'action-edit'}
            icon={<OpenInNewIcon />}
            sx={{ padding: '2px 6px' }}
            label="Edit"
            //showInMenu
            onClick={() => {
              window.open(`/merchant/store/${row._id}`, '_blank')
            }}
          />,
        ],
      },
    ],
    []
  )

  const debouncedFn = useMemo(
    () =>
      debounce((newSearch: string) => {
        if (newSearch) {
          setFilters([
            {
              field: 'name',
              operator: 'contains',
              value: newSearch,
            },
          ])
        } else {
          setFilters([])
        }
      }, 1000),
    [setFilters]
  )

  if (dataGridProps.loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      <Box
        display="flex"
        gap={2}
        alignItems="center"
        sx={{
          marginBottom: '20px',
        }}
      >
        <TextField
          placeholder="Search..."
          autoFocus={true}
          value={search}
          onChange={(e) => {
            const newSearch = e.target.value
            setSearch(newSearch)
            debouncedFn(newSearch)
          }}
          InputProps={{
            startAdornment: <SearchOutlined />,
          }}
          size="small"
          sx={{
            width: '300px',
          }}
        />
      </Box>

      <List
        createButtonProps={{
          onClick: () => {
            if (!createModalVisible) {
              resetCreateModal()
              showCreateModal()
            }
          },
          variant: 'contained',
        }}
      >
        <DataGrid
          {...dataGridProps}
          columns={columns}
          sx={{
            '.MuiDataGrid-row:hover': {
              cursor: 'pointer',
            },
          }}
        />
      </List>
      <CreateStoreModal {...createStoreModalFormProps} />
    </Box>
  )
}

export function CreateStoreModal({
  saveButtonProps,
  modal: { visible, close },
  register,
  getValues,
  formState: { errors },
}: UseModalFormReturnType<StoreCreateDto, HttpError, StoreCreateDto>) {
  return (
    <Dialog
      open={visible}
      onClose={close}
      maxWidth={false}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '90vw',
          maxHeight: '90vh',
          backgroundColor: 'white',
        },
      }}
    >
      <DialogTitle>Create Store</DialogTitle>
      <DialogContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px',
          }}
        >
          <Box
            component="form"
            autoComplete="off"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '500px',
              //gap: '10px',
            }}
          >
            <Grid
              container
              spacing={2}
              sx={{
                justifyItems: 'center',
                alignItems: 'center',
              }}
            >
              <Grid
                size={3}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography>Store Name</Typography>
              </Grid>
              <Grid
                size={9}
                sx={{
                  alignItems: 'center',
                }}
              >
                <TextField
                  {...register('name', {
                    required: 'Required',
                  })}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  margin="normal"
                  fullWidth
                />
              </Grid>

              <Grid
                size={3}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography>Website</Typography>
              </Grid>
              <Grid
                size={9}
                sx={{
                  alignItems: 'center',
                }}
              >
                <TextField
                  {...register('website', {
                    required: 'Required',
                    validate: (value) => {
                      //validate this is a valid URL
                      if (!value) {
                        return 'Required'
                      }

                      try {
                        new URL(value)
                      } catch (e) {
                        return 'Invalid URL'
                      }
                      return true
                    },
                  })}
                  error={!!errors.website}
                  helperText={errors.website?.message}
                  margin="normal"
                  fullWidth
                />
              </Grid>

              <Grid
                size={3}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography>Myshopify Domain</Typography>
              </Grid>
              <Grid
                size={9}
                sx={{
                  alignItems: 'center',
                }}
              >
                <TextField
                  {...register('shopifyConfig.myshopifyDomain', {
                    required: false,
                    validate: (value) => {
                      if (value && !getValues('shopifyConfig.appHandle')) {
                        return `Myshopify domain can't be set when app handle is empty`
                      } else if (
                        !value &&
                        getValues('shopifyConfig.appHandle')
                      ) {
                        return `Myshopify domain can't be empty when app handle is set`
                      }
                      return true
                    },
                  })}
                  error={!!(errors.shopifyConfig as any)?.myshopifyDomain}
                  helperText={String(
                    (errors.shopifyConfig as any)?.myshopifyDomain?.message ??
                      ''
                  )}
                  margin="normal"
                  fullWidth
                />
              </Grid>

              <Grid
                size={3}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography>Shopify App Handle</Typography>
              </Grid>
              <Grid
                size={9}
                sx={{
                  alignItems: 'center',
                }}
              >
                <TextField
                  {...register('shopifyConfig.appHandle', {
                    required: false,
                    validate: (value) => {
                      if (
                        value &&
                        !getValues('shopifyConfig.myshopifyDomain')
                      ) {
                        return `App handle can't be set when myshopify domain is empty`
                      } else if (
                        !value &&
                        getValues('shopifyConfig.myshopifyDomain')
                      ) {
                        return `App handle can't be empty when myshopify domain is set`
                      }
                      return true
                    },
                  })}
                  error={!!(errors.shopifyConfig as any)?.appHandle}
                  helperText={String(
                    (errors.shopifyConfig as any)?.appHandle?.message ?? ''
                  )}
                  margin="normal"
                  fullWidth
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={close}>
          Cancel
        </Button>
        <SaveButton {...saveButtonProps} variant="outlined">
          Create
        </SaveButton>
      </DialogActions>
    </Dialog>
  )
}
