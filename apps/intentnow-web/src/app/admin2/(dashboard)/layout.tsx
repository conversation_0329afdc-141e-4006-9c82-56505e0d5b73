'use client'

import { Refine } from '@refinedev/core'
import { RefineSnackbarProvider, useNotificationProvider } from '@refinedev/mui'
import routerProvider from '@refinedev/nextjs-router'
import { Box, GlobalStyles, Stack, Typography } from '@mui/material'
import {
  Home as HomeIcon,
  Store as StoreIcon,
  Extension as ExtensionIcon,
  Construction as ConstructionIcon,
} from '@mui/icons-material'
import { NextAppProvider } from '@toolpad/core/nextjs'
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter'
import { DashboardLayout } from '@toolpad/core/DashboardLayout'
import { PageContainer } from '@toolpad/core/PageContainer'
import type { Navigation, Session } from '@toolpad/core/AppProvider'
import { DialogsProvider } from '@toolpad/core/useDialogs'
import theme from '@/components/admin-theme'
import { useAuth as useClerkAuth, UserButton } from '@clerk/nextjs'
import { useEffect, useMemo } from 'react'
import { useAuth } from '@/modules/auth/auth'
import { useRefineDataProvider } from '@/modules/intentnow/refine-data-provider'

function CustomActions() {
  return (
    <Stack direction="row" alignItems="center">
      {/* <ThemeSwitcher /> */}
    </Stack>
  )
}

function CustomAccount() {
  return <UserButton />
}

function CustomAppTitle() {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <ConstructionIcon
        //color={currentStoreId === store._id ? 'primary' : undefined}
        sx={{ marginLeft: '20px', marginRight: '10px' }}
      />
      <Typography variant="h6">Intentnow Admin</Typography>
    </Box>
  )
}

export function AdminDashboardLayoutInner({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <>
      <DashboardLayout
        sidebarExpandedWidth={200}
        slots={{
          appTitle: CustomAppTitle,
          toolbarActions: CustomActions,
          toolbarAccount: CustomAccount,
        }}
        sx={{
          '& .MuiAppBar-root': {
            backgroundColor: '#000000',
            color: '#fff',
          },
        }}
      >
        <PageContainer maxWidth="xl">{children}</PageContainer>
      </DashboardLayout>
    </>
  )
}

export default function AdminDashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { dataProvider } = useRefineDataProvider()

  const clerkAuth = useClerkAuth()
  const { user } = useAuth()
  const session: Session | undefined = useMemo(() => {
    return user
      ? {
          user: {
            id: user.userId,
            name: user.userInfo?.displayName,
            email: user.userInfo?.email,
          },
        }
      : undefined
  }, [user])

  const navigation: Navigation = [
    {
      segment: `admin2`,
      title: 'Admin Home',
      icon: <HomeIcon />,
    },
    {
      segment: `admin2/stores`,
      title: 'Stores',
      icon: <StoreIcon />,
    },
    {
      segment: `admin2/shopify-apps`,
      title: 'Shopify Apps',
      icon: <ExtensionIcon />,
    },
  ]

  useEffect(() => {
    document.title = `IntentNow Admin`
  }, [])

  return (
    <AppRouterCacheProvider options={{ enableCssLayer: true }}>
      <NextAppProvider
        theme={theme}
        navigation={navigation}
        branding={{
          title: `IntentNow Admin`,
          homeUrl: `/admin2`,
        }}
        session={session}
        authentication={{
          signIn: () => {},
          signOut: clerkAuth.signOut,
        }}
      >
        <GlobalStyles styles={{ html: { WebkitFontSmoothing: 'auto' } }} />
        <DialogsProvider>
          <RefineSnackbarProvider>
            <Refine
              dataProvider={dataProvider}
              routerProvider={routerProvider}
              notificationProvider={useNotificationProvider}
            >
              <AdminDashboardLayoutInner>{children}</AdminDashboardLayoutInner>
            </Refine>
          </RefineSnackbarProvider>
        </DialogsProvider>
      </NextAppProvider>
    </AppRouterCacheProvider>
  )
}
