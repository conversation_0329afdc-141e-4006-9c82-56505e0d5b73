import {
  Clerk<PERSON><PERSON><PERSON>,
  RedirectToSignIn,
  SignedIn,
  SignedOut,
  SignOutButton,
} from '@clerk/nextjs'
import { UserIsAdmin, UserIsNotAdmin } from '@/modules/auth/auth-check'
import { clerkConfig } from '@/modules/config/clerk'

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider
      publishableKey={clerkConfig.publishableKey}
      afterSignOutUrl="/admin2"
    >
      <html lang="en">
        <body>
          <SignedOut>
            <RedirectToSignIn />
          </SignedOut>
          <SignedIn>
            <UserIsAdmin>{children}</UserIsAdmin>
            <UserIsNotAdmin>
              <div>Admin user requried</div>
              <SignOutButton redirectUrl="/admin2" />
            </UserIsNotAdmin>
          </SignedIn>
        </body>
      </html>
    </ClerkProvider>
  )
}
