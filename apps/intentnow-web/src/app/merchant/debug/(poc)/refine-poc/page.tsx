'use client'

import { StoreDto } from '@packages/shared-entities'
import { useOne, useCreate } from '@refinedev/core'
import { List, useDataGrid } from '@refinedev/mui'
import { DataGrid, GridColDef } from '@mui/x-data-grid'
import { useMemo } from 'react'

export default function RefinePocPage() {
  // const {
  //   tableQuery: { data: storesData },
  // } = useTable<StoreDto>({
  //   resource: 'stores',
  //   // queryOptions: {
  //   //   refetchOnWindowFocus: 'always',
  //   // },
  // })
  const { dataGridProps } = useDataGrid<StoreDto>({
    resource: 'stores',
    sorters: { initial: [] },
    syncWithLocation: true,
  })
  const columns = useMemo<GridColDef<StoreDto>[]>(
    () => [
      {
        field: 'id',
        headerName: 'ID',
        width: 250,
        sortable: false,
      },
      {
        field: 'name',
        headerName: 'Name',
        width: 200,
        sortable: false,
      },
      {
        field: 'website',
        headerName: 'Website',
        width: 300,
        sortable: false,
      },
      {
        field: 'shopifyConfig',
        headerName: 'Shopify Config',
        maxWidth: 400,
        flex: 1,
        sortable: false,
        valueFormatter: (value: StoreDto['shopifyConfig']) => {
          return value
            ? `${value.myshopifyDomain}@${value.appHandle}`
            : undefined
        },
      },
    ],
    []
  )

  const { data: storeData } = useOne<StoreDto>({
    resource: 'stores',
    id: '68550d636488aea5a98fa1f8',
    // queryOptions: {
    //   refetchOnWindowFocus: 'always',
    // },
  })
  const {
    // mutate: createStore
  } = useCreate<StoreDto>({
    resource: 'stores',
  })
  const store = storeData?.data
  //const stores = storesData?.data

  return (
    <>
      <div>
        Store {store?._id}:<pre>{JSON.stringify(store, null, 2)}</pre>
      </div>
      <div>
        Stores:
        <List>
          <DataGrid {...dataGridProps} columns={columns} />
        </List>
      </div>
    </>
  )
}
