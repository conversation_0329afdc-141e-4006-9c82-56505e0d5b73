'use client'

import { Box } from '@mui/material'
import { <PERSON><PERSON><PERSON>, BarItem } from '@mui/x-charts/BarChart'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>nel,
  LabelList,
  ResponsiveContainer,
  XAxis,
  YAxis,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
} from 'recharts'
import React from 'react'

const VerticalTick = ({ x, y, payload }: any) => (
  <g transform={`translate(${x},${y})`}>
    <text
      x={0}
      y={0}
      dy={4}
      textAnchor="end"
      transform="rotate(-90)"
      fontSize={12}
      fill="#8884d8"
    >
      {payload.value}
    </text>
  </g>
)

export default function ChartPocPage() {
  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexWrap: 'wrap',
        gap: 2,
      }}
    >
      <Box
        sx={{
          width: '400px',
          height: '300px',
        }}
      >
        <BarChart
          layout="horizontal"
          yAxis={[
            {
              data: ['sessions', 'checkouts'],
              tickLabelStyle: {
                angle: -90,
              },
            },
          ]}
          xAxis={[{ label: 'conversion %' }]}
          series={[
            { data: [100, 1.8], label: 'control', id: 'control' },
            { data: [100, 2.5], label: 'test', id: 'test' },
          ]}
          barLabel={(item: BarItem) => {
            if (item.seriesId === 'control') {
              if (item.dataIndex === 0) {
                return '1024'
              } else {
                return '18 (1.8%)'
              }
            } else if (item.seriesId === 'test') {
              if (item.dataIndex === 0) {
                return '856'
              } else {
                return '21 (2.5%)'
              }
            }
          }}
        />
      </Box>

      <Box
        sx={{
          width: '400px',
          height: '300px',
          display: 'flex',
        }}
      >
        <ResponsiveContainer>
          <FunnelChart>
            <Funnel
              dataKey="value"
              data={[
                {
                  name: 'sessions',
                  value: 1024,
                  fill: '#8884d8',
                },
                {
                  name: 'checkouts',
                  value: 184,
                  fill: '#83a6ed',
                },
              ]}
              isAnimationActive
            >
              <LabelList
                position="right"
                fill="#000"
                stroke="none"
                dataKey="name"
              />
            </Funnel>
          </FunnelChart>
        </ResponsiveContainer>
        <ResponsiveContainer>
          <FunnelChart>
            <Funnel
              dataKey="value"
              data={[
                {
                  name: 'sessions',
                  value: 856,
                  fill: '#8884d8',
                },
                {
                  name: 'checkouts',
                  value: 214,
                  fill: '#83a6ed',
                },
              ]}
              isAnimationActive
            >
              <LabelList
                position="center"
                fill="#000"
                stroke="none"
                dataKey="name"
              />
            </Funnel>
          </FunnelChart>
        </ResponsiveContainer>
      </Box>
      <Box
        sx={{
          width: '400px',
          height: '300px',
        }}
      >
        <ResponsiveContainer width="100%" height="100%">
          <RechartsBarChart
            layout="vertical"
            width={500}
            height={300}
            data={[
              {
                name: 'sessions',
                control: 100,
                test: 100,
              },
              {
                name: 'checkouts',
                control: 18,
                test: 25,
              },
            ]}
            // margin={{
            //   top: 5,
            //   right: 30,
            //   left: 20,
            //   bottom: 5,
            // }}
          >
            <XAxis type="number" label="conversion %" />
            <YAxis type="category" dataKey="name" tick={<VerticalTick />} />
            <Tooltip />
            <Legend />
            <Bar dataKey="control" fill="blue" />
            <Bar dataKey="test" fill="orange" />
          </RechartsBarChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  )
}
