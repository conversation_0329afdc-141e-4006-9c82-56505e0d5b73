'use client'

import { EmbeddedDialog } from '@/components/embedded-dialog'
import { useAuthedApiFetch } from '@/modules/auth/fetch'
import { refineConfig } from '@/modules/config/refine'
import { Box, Button, CircularProgress, Typography } from '@mui/material'
import { CommitLinkStoreRequestDto } from '@packages/shared-entities'
import { useOne } from '@refinedev/core'
import { redirect, RedirectType, useParams } from 'next/navigation'
import { useEffect, useState } from 'react'

export default function LinkStorePage() {
  const { authedApiPostFetch } = useAuthedApiFetch()
  const { requestId }: { requestId: string } = useParams()
  const [step, setStep] = useState<
    'loading' | 'confirm' | 'linking' | 'complete' | 'error'
  >('loading')
  const { data: { data: linkStoreRequest } = {}, isLoading } = useOne({
    resource: 'link-store-requests',
    id: requestId,
    queryOptions: refineConfig.defaultUseOneQueryOptions,
    errorNotification: (data, values) => {
      return {
        message: `Failed to load link store request ${values?.id}`,
        description: 'Error',
        type: 'error',
      }
    },
  })
  const [storeId, setStoreId] = useState<string>()

  useEffect(() => {
    if (step === 'loading') {
      if (!isLoading) {
        if (linkStoreRequest) {
          if (linkStoreRequest.status === 'pending') {
            setStep('confirm')
          } else {
            setStep('error')
          }
        } else {
          setStep('error')
        }
      }
    } else if (step === 'linking') {
    }
  }, [step, linkStoreRequest, isLoading])

  async function commitLinkStoreRequest() {
    setStep('linking')
    try {
      const { storeId } = await authedApiPostFetch<CommitLinkStoreRequestDto>([
        `/api/intentnow/link-store-requests/${requestId}/commit`,
        {},
      ])
      setStoreId(storeId)
      setStep('complete')
    } catch (error) {
      setStep('error')
    }
  }

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
      }}
    >
      <EmbeddedDialog>
        {(step === 'loading' || step === 'linking') && (
          <>
            <CircularProgress />
          </>
        )}
        {step === 'confirm' && (
          <>
            <Typography
              variant="h5"
              sx={{
                marginBottom: '20px',
              }}
            >
              Link Shopify Store
            </Typography>
            <Typography
              sx={{
                marginBottom: '20px',
              }}
            >
              <b>Store Name:</b> {`${linkStoreRequest?.storeName}`}
              <br />
              <b>Store Website:</b> {`${linkStoreRequest?.storeWebsite}`}
              <br />
              <b>MyshopifyDomain:</b> {`${linkStoreRequest?.storeRef}`}
              <br />
            </Typography>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'center',
                gap: '40px',
              }}
            >
              <Button variant="contained" onClick={commitLinkStoreRequest}>
                Link Store
              </Button>
              <Button
                variant="contained"
                onClick={() => {
                  redirect('/merchant', RedirectType.push)
                }}
              >
                Cancel
              </Button>
            </Box>
          </>
        )}
        {step === 'complete' && (
          <>
            <Typography
              variant="h5"
              sx={{
                marginBottom: '20px',
              }}
            >
              This Shopify store has been linked to your IntentNow merchant
              account successfully!
            </Typography>
            <Box
              sx={{
                marginTop: '20px',
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'center',
                gap: '40px',
              }}
            >
              <Button
                variant="contained"
                onClick={() => {
                  redirect(`/merchant/store/${storeId}`, RedirectType.push)
                }}
              >
                Go to Store Dashboard
              </Button>
            </Box>
          </>
        )}
        {step === 'error' && (
          <>
            <Typography
              variant="h5"
              sx={{
                marginBottom: '20px',
              }}
            >
              We could not complete your store linking request.
            </Typography>
            <Box
              sx={{
                marginTop: '20px',
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'center',
                gap: '40px',
              }}
            >
              <Button
                variant="contained"
                onClick={() => {
                  redirect('/merchant', RedirectType.push)
                }}
              >
                Go to Merchant Portal
              </Button>
            </Box>
          </>
        )}
      </EmbeddedDialog>
    </Box>
  )
}
