'use client'

import { useAuth } from '@/modules/auth/auth'
import {
  Box,
  Button,
  CircularProgress,
  TextField,
  Typography,
} from '@mui/material'
import { StoreDto } from '@packages/shared-entities'
import { useSearchParams } from 'next/navigation'
import { useRouter } from 'next/navigation'
import { EmbeddedDialog } from '@/components/embedded-dialog'
import { refineConfig } from '@/modules/config/refine'
import { List, useDataGrid } from '@refinedev/mui'
import { useEffect, useMemo, useState } from 'react'
import { DataGrid, GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { SearchOutlined } from '@mui/icons-material'
import { debounce } from 'lodash'

export default function MerchantHomePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user } = useAuth()
  const openFrom = searchParams.get('open-from')
  const [search, setSearch] = useState('')

  const {
    dataGridProps,
    setFilters,
    filters,
    tableQuery: { error },
  } = useDataGrid<StoreDto>({
    resource: `users/${user?.userId}/stores`,
    sorters: { initial: [{ field: 'name', order: 'asc' }] },
    filters: {
      initial: [],
    },
    syncWithLocation: false,
    pagination: {
      pageSize: 10,
    },
    queryOptions: {
      ...refineConfig.defaultUseListQueryOptions,
      enabled: Boolean(user?.userId),
    },
  })

  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'name',
        headerName: 'Store Name',
        flex: 1,
        sortable: true,
        filterable: false,
      },
    ],
    []
  )

  const debouncedFn = useMemo(
    () =>
      debounce((newSearch: string) => {
        if (newSearch) {
          setFilters([
            {
              field: 'name',
              operator: 'contains',
              value: newSearch,
            },
          ])
        } else {
          setFilters([])
        }
      }, 1000),
    [setFilters]
  )

  useEffect(() => {
    if (
      !filters.length &&
      !dataGridProps.loading &&
      dataGridProps.rowCount === 1 &&
      openFrom !== 'store-not-found'
    ) {
      //Auto redirect to the only store
      router.push(`/merchant/store/${dataGridProps.rows[0]._id}`)
    }
  }, [
    openFrom,
    debouncedFn,
    filters.length,
    dataGridProps.loading,
    dataGridProps.rowCount,
    dataGridProps.rows,
    router,
  ])

  if (
    !filters.length &&
    !dataGridProps.loading &&
    dataGridProps.rowCount === 1 &&
    openFrom !== 'store-not-found'
  ) {
    return <></>
  }

  if (dataGridProps.loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  const hasStore = dataGridProps.rowCount > 0 || Boolean(filters.length)

  if (error) {
    return (
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography>
          Failed to load data. Please refresh the page to try again.
        </Typography>
      </Box>
    )
  }

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
      }}
    >
      <EmbeddedDialog>
        {hasStore && (
          <>
            <Typography
              variant="h5"
              sx={{
                marginBottom: '20px',
              }}
            >
              Select a store to manage
            </Typography>

            <List
              headerButtons={() => (
                <Box display="flex" gap={2} alignItems="center">
                  <TextField
                    placeholder="Search..."
                    autoFocus={true}
                    value={search}
                    onChange={(e) => {
                      const newSearch = e.target.value
                      setSearch(newSearch)
                      debouncedFn(newSearch)
                    }}
                    InputProps={{
                      startAdornment: <SearchOutlined />,
                    }}
                    size="small"
                  />
                </Box>
              )}
            >
              <DataGrid
                {...dataGridProps}
                columns={columns}
                sx={{
                  '.MuiDataGrid-row:hover': {
                    cursor: 'pointer',
                  },
                }}
                onRowClick={(params) => {
                  router.push(`/merchant/store/${params.row._id}`)
                }}
              />
            </List>
          </>
        )}
        {!hasStore && (
          <>
            <Typography
              variant="h5"
              sx={{
                marginBottom: '20px',
              }}
            >
              No store is linked to this account.
            </Typography>
            <Typography>
              Please install the IntentNow Shopify App, and link a store from
              inside your Shopify Admin.
            </Typography>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Box
                sx={{
                  marginTop: '40px',
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'center',
                  gap: '40px',
                }}
              >
                <Button
                  variant="contained"
                  sx={{
                    margin: '10px',
                  }}
                >
                  Install IntentNow App
                </Button>
                <Button
                  variant="contained"
                  sx={{
                    margin: '10px',
                  }}
                  onClick={() => {
                    window.open(`https://admin.shopify.com`, '_blank')
                  }}
                >
                  Go to Shopify Admin
                </Button>
              </Box>
            </Box>
          </>
        )}
      </EmbeddedDialog>
    </Box>
  )
}
