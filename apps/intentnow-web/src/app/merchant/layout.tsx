import {
  Clerk<PERSON>rovider,
  RedirectToSignIn,
  SignedIn,
  SignedOut,
  SignOutButton,
} from '@clerk/nextjs'
import { UserIsMerchant, UserIsNotMerchant } from '@/modules/auth/auth-check'
import { Metadata } from 'next'
import { clerkConfig } from '@/modules/config/clerk'

export const metadata: Metadata = {
  //title: 'Merchant Portal',
  description: 'IntentNow Merchant Portal',
  icons: {
    icon: 'https://api.intentnow.com/cdn/intentnow-images/intentnow-logo-1.png',
  },
}

export default function MerchantRootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <ClerkProvider
          publishableKey={clerkConfig.publishableKey}
          afterSignOutUrl="/merchant"
        >
          <SignedOut>
            <RedirectToSignIn />
          </SignedOut>
          <SignedIn>
            <UserIsMerchant>{children}</UserIsMerchant>
            <UserIsNotMerchant>
              <div>Merchant user requried</div>
              <SignOutButton redirectUrl="/merchant" />
            </UserIsNotMerchant>
          </SignedIn>
        </ClerkProvider>
      </body>
    </html>
  )
}
