'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Card,
  CardContent,
  CardHeader,
  Grid2 as Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material'
import {
  Edit as EditIcon,
  Check as CheckIcon,
  Clear as ClearIcon,
} from '@mui/icons-material'
import { StoreDto } from '@packages/shared-entities'
import { useAuth } from '@/modules/auth/auth'
import { useState } from 'react'
import { useForm } from '@refinedev/react-hook-form'

export function StoreInfoSettingsCard({ storeId }: { storeId: string }) {
  const { user } = useAuth()
  const [editing, setEditing] = useState(false)

  const {
    register,
    reset,
    getValues,
    formState: { errors, isDirty, isValid, isReady },
    refineCore: {
      query,
      mutation: { mutateAsync: updateStore },
    },
  } = useForm<StoreDto>({
    mode: 'onChange',
    refineCoreProps: {
      resource: 'stores',
      id: storeId,
      action: 'edit',
      queryOptions: refineConfig.defaultUseOneQueryOptions,
    },
    resetOptions: {
      keepDirty: false,
      keepDefaultValues: false,
      keepErrors: false,
      keepTouched: false,
      keepIsValid: false,
      keepIsSubmitted: false,
      keepSubmitCount: false,
    },
  })
  const store = query?.data?.data

  if (!store) {
    return <></>
  }

  return (
    <Card
      sx={{
        marginBottom: '20px',
      }}
    >
      <CardHeader
        title="Store Info"
        action={
          user?.roles?.admin && (
            <>
              {editing && (
                <>
                  <IconButton
                    disabled={!isDirty || !isValid}
                    onClick={async () => {
                      if (isValid) {
                        await updateStore({
                          resource: 'stores',
                          id: store._id,
                          values: {
                            name: getValues('name') ?? '',
                            website: getValues('website') ?? '',
                          },
                        })
                      }
                      setEditing(false)
                    }}
                  >
                    <CheckIcon />
                  </IconButton>
                  <IconButton
                    onClick={() => {
                      reset(query?.data?.data)
                      setEditing(false)
                    }}
                  >
                    <ClearIcon />
                  </IconButton>
                </>
              )}
              {!editing && (
                <IconButton
                  disabled={!isReady}
                  onClick={() => {
                    if (isReady && query?.data?.data) {
                      reset(query?.data?.data)
                      setEditing(true)
                    }
                  }}
                >
                  <EditIcon />
                </IconButton>
              )}
            </>
          )
        }
      />
      <CardContent>
        <Grid container spacing={2}>
          <Grid size={4}>
            <Typography>Name</Typography>
          </Grid>
          <Grid size={8}>
            {editing && (
              <>
                <TextField
                  type="string"
                  {...register('name', {
                    required: 'Required',
                  })}
                  error={!!errors.name}
                  helperText={String(errors.name?.message ?? '')}
                  fullWidth
                />
              </>
            )}
            {!editing && <Typography>{store.name}</Typography>}
          </Grid>
          <Grid size={4}>
            <Typography>Website</Typography>
          </Grid>
          <Grid size={8}>
            {editing && (
              <>
                <TextField
                  type="string"
                  {...register('website', {
                    required: 'Required',
                  })}
                  error={!!errors.website}
                  helperText={String(errors.website?.message ?? '')}
                  fullWidth
                />
              </>
            )}
            {!editing && (
              <Typography>
                <a
                  href={store.website}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {store.website}
                </a>
              </Typography>
            )}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}
