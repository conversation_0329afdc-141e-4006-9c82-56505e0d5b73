'use client'

import { useParams } from 'next/navigation'
import { StoreSettingsSubLayout } from '../store-settings-sub-layout'

export default function StoreSettingsAccountLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { storeId }: { storeId: string } = useParams()

  return (
    <>
      <StoreSettingsSubLayout
        navigation={[
          {
            title: 'Users',
            rootPath: `/merchant/store/${storeId}/settings/account/users`,
          },
          {
            title: 'Personal',
            rootPath: `/merchant/store/${storeId}/settings/account/personal`,
          },
        ]}
      >
        {children}
      </StoreSettingsSubLayout>
    </>
  )
}
