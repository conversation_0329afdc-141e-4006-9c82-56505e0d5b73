'use client'

import { refineConfig } from '@/modules/config/refine'
import { Box, Button, Dialog } from '@mui/material'
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  Preview as PreviewIcon,
} from '@mui/icons-material'
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridValidRowModel,
} from '@mui/x-data-grid'
import { DialogProps } from '@toolpad/core/useDialogs'
import { StoreDto, StoreOfferDto } from '@packages/shared-entities'
import { List, useDataGrid } from '@refinedev/mui'
import { useParams } from 'next/navigation'
import { useMemo, useState } from 'react'
import { storeOfferStatusLabels } from '@/modules/intentnow/stores'
import { useDialogs } from '@toolpad/core/useDialogs'
import { ConfirmDialog2 } from '@/components/confirm-dialog'
import { useDelete, useOne } from '@refinedev/core'
import { StoreOfferEditor } from '@/components/widgets/store-offer-editor'
import { getPreviewWidgetUrl } from '@/modules/intentnow/widgets'
import { StoreImagesManager } from '@/components/widgets/store-image-manager'

export default function StoreOffersPage() {
  const { storeId }: { storeId: string } = useParams()
  const resource = `stores/${storeId}/offers`

  const { data: { data: store } = {} } = useOne<StoreDto>({
    resource: 'stores',
    id: storeId,
    queryOptions: refineConfig.defaultUseOneQueryOptions,
  })

  const { mutateAsync: deleteItem } = useDelete<StoreOfferDto>()
  const dialogs = useDialogs()

  const [openOfferEditor, setOpenOfferEditor] = useState(false)
  const [editOfferId, setEditOfferId] = useState<string>()
  const [initialOfferData, setInitialOfferData] = useState<StoreOfferDto>()

  const [openImagesManager, setOpenImagesManager] = useState(false)

  const { dataGridProps } = useDataGrid<StoreOfferDto>({
    resource,
    sorters: { initial: [] },
    syncWithLocation: false,
    queryOptions: refineConfig.defaultUseListQueryOptions,
  })
  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'name',
        headerName: 'Name',
        flex: 1,
        sortable: true,
        filterable: false,
      },
      {
        field: 'status',
        headerName: 'Status',
        width: 150,
        sortable: true,
        filterable: true,
        valueFormatter: (value: string) => {
          return storeOfferStatusLabels[value]
        },
      },
      {
        field: 'discountConfig',
        headerName: 'Discount',
        width: 150,
        sortable: false,
        filterable: false,
        valueFormatter: (value: any) => {
          return value ? value.discount?.title || 'Created' : 'Not Created'
        },
      },
      {
        field: 'widgetConfig',
        headerName: 'Dialog/Teaser',
        width: 150,
        sortable: false,
        filterable: false,
        valueFormatter: (value: any) => {
          return value ? 'Created' : 'Not Created'
        },
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Action',
        width: 150,
        sortable: false,
        filterable: false,
        getActions: ({ row }) => [
          <GridActionsCellItem
            key={2}
            icon={<EditIcon />}
            sx={{ padding: '2px 6px' }}
            label="Edit"
            onClick={() => {
              if (!openOfferEditor) {
                setEditOfferId(row._id)
                setInitialOfferData(undefined)
                setOpenOfferEditor(true)
              }
            }}
          />,
          <GridActionsCellItem
            key={2}
            icon={<PreviewIcon />}
            sx={{ padding: '2px 6px' }}
            label="Preview"
            disabled={
              row.status !== 'complete' ||
              !store?.shopifyConfig?.myshopifyDomain
            }
            onClick={() => {
              if (
                row.status === 'complete' &&
                store?.shopifyConfig?.myshopifyDomain
              ) {
                const livePreviewLink = getPreviewWidgetUrl(
                  store.shopifyConfig.myshopifyDomain,
                  store.website,
                  row as any
                )
                window.open(livePreviewLink, '_blank')
              }
            }}
          />,
          <GridActionsCellItem
            key={2}
            icon={<CopyIcon />}
            sx={{ padding: '2px 6px' }}
            label="Duplicate"
            showInMenu
            onClick={() => {
              if (!openOfferEditor) {
                setEditOfferId(undefined)
                setInitialOfferData(row as any)
                setOpenOfferEditor(true)
              }
            }}
          />,
          <GridActionsCellItem
            key={2}
            icon={<DeleteIcon />}
            sx={{ padding: '2px 6px' }}
            label="Delete"
            showInMenu
            onClick={async () => {
              const { answer } = await dialogs.open(ConfirmDialog2, {
                title: 'Delete Offer Popup',
                description: `You are about to delete the offer popup "${row.name}". Deleted offer popup can't be recovered. Do you want to continue?`,
              })
              if (answer) {
                //Delete the offer
                await deleteItem({
                  resource,
                  id: row._id,
                })
              }
            }}
          />,
        ],
      },
    ],
    [store, openOfferEditor, dialogs, deleteItem, resource]
  )

  return (
    <Box>
      <List
        headerButtons={({ defaultButtons }) => (
          <Box display="flex" gap={2} alignItems="center">
            <Button
              variant="contained"
              onClick={() => {
                setOpenImagesManager(true)
              }}
            >
              Manage Images
            </Button>
            {defaultButtons}
          </Box>
        )}
        createButtonProps={{
          onClick: () => {
            if (!openOfferEditor) {
              setEditOfferId(undefined)
              setInitialOfferData(undefined)
              setOpenOfferEditor(true)
            }
          },
          variant: 'contained',
        }}
      >
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
      <StoreOfferEditorDialog
        open={openOfferEditor}
        onClose={async () => {
          setOpenOfferEditor(false)
        }}
        payload={{
          storeId,
          offerId: editOfferId,
          initialOfferData,
        }}
      />
      <StoreImagesManager
        open={openImagesManager}
        onClose={() => {
          setOpenImagesManager(false)
        }}
        storeId={storeId}
      />
    </Box>
  )
}

export function StoreOfferEditorDialog({
  payload,
  open,
  onClose,
}: DialogProps<
  {
    storeId: string
    offerId?: string
    initialOfferData?: StoreOfferDto
  },
  any
>) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      disableEscapeKeyDown={true}
      maxWidth={false}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '100vw',
          maxHeight: '100vh',
          width: '100vw',
          height: '100vh',
          margin: 0,
          padding: 0,
          backgroundColor: '#fff',
        },
      }}
    >
      <Box
        sx={{
          width: '100%',
          height: '100%',
          overflow: 'hidden',
        }}
      >
        <StoreOfferEditor
          storeId={payload.storeId}
          offerId={payload.offerId}
          initialOfferData={payload.initialOfferData}
          outsideHeight={0}
          onClose={onClose}
        />
      </Box>
    </Dialog>
  )
}
