'use client'

import { Refine, useList, useOne } from '@refinedev/core'
import { RefineSnackbarProvider, useNotificationProvider } from '@refinedev/mui'
import routerProvider from '@refinedev/nextjs-router'
import {
  Box,
  Button,
  CircularProgress,
  GlobalStyles,
  MenuItem,
  Select,
  Stack,
  Typography,
} from '@mui/material'
import {
  Home as HomeIcon,
  Store as StoreIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
  LocalOffer as LocalOfferIcon,
  RocketLaunch as RocketLaunchIcon,
  Percent as PercentIcon,
} from '@mui/icons-material'
import { NextAppProvider } from '@toolpad/core/nextjs'
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter'
import { DashboardLayout } from '@toolpad/core/DashboardLayout'
import { PageContainer } from '@toolpad/core/PageContainer'
import type { Navigation, Session } from '@toolpad/core/AppProvider'
import { DialogsProvider } from '@toolpad/core/useDialogs'
import theme from '@/components/admin-theme'
import { useAuth as useClerkAuth, UserButton } from '@clerk/nextjs'
import { StoreDto } from '@packages/shared-entities'
import { useEffect, useMemo } from 'react'
import { useAuth } from '@/modules/auth/auth'
import { usePathname, useRouter } from 'next/navigation'
import { useRefineDataProvider } from '@/modules/intentnow/refine-data-provider'
import { refineConfig } from '@/modules/config/refine'

function CustomActions() {
  return (
    <Stack direction="row" alignItems="center">
      {/* <ThemeSwitcher /> */}
    </Stack>
  )
}

function CustomAccount() {
  return <UserButton />
}

function CustomSidebarFooter({
  mini,
  storeId,
}: {
  mini: boolean
  storeId: string
}) {
  const router = useRouter()
  return (
    <Box
      sx={{
        display: 'flex',
      }}
    >
      <Button
        sx={{
          flex: 1,
          margin: '5px',
          justifyContent: 'left',
        }}
        onClick={() => {
          router.push(`/merchant/store/${storeId}/settings/store`)
        }}
      >
        <SettingsIcon
          sx={{
            margin: '15px',
          }}
        />
        {!mini && <>Settings</>}
      </Button>
    </Box>
  )
}

function CustomAppTitle({
  currentStore,
  stores,
}: {
  currentStore?: StoreDto
  stores?: StoreDto[]
}) {
  const router = useRouter()

  if (!currentStore) {
    return <></>
  }

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <Select
        value={currentStore._id}
        sx={{
          backgroundColor: '#073035',
          color: '#fff',

          marginLeft: '10px',
          marginTop: '0px',
          marginBottom: '0px',
          border: 'none',
          boxShadow: 'none',

          '& .MuiSelect-icon': {
            color: '#fff',
          },

          // Show box shadow on hover
          transition: 'box-shadow 0.3s',
          '&:hover': {
            backgroundColor: '#37565a',
            color: '#fff',
            //boxShadow: 2,
          },
        }}
        onChange={(e) => {
          const newStoreId = e.target.value
          if (newStoreId !== currentStore._id) {
            router.push(`/merchant/store/${newStoreId}`)
          }
        }}
      >
        {stores?.map((store) => (
          <MenuItem key={store._id} value={store._id}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <StoreIcon
                //color={currentStoreId === store._id ? 'primary' : undefined}
                sx={{ marginRight: '10px' }}
              />
              <Typography
                //color={currentStoreId === store._id ? 'primary' : undefined}
                variant={currentStore._id === store._id ? 'h6' : undefined}
              >
                {store.name}
              </Typography>
            </Box>
          </MenuItem>
        ))}
      </Select>
    </Box>
  )
}

export function StoreDashboardLayoutInner({
  storeId,
  children,
}: Readonly<{
  storeId: string
  children: React.ReactNode
}>) {
  const router = useRouter()
  const pathname = usePathname()
  const { user } = useAuth()
  const {
    data: { data: store } = {},
    isLoading,
    error,
  } = useOne<StoreDto>({
    resource: 'stores',
    id: storeId,
    queryOptions: refineConfig.defaultUseOneQueryOptions,
  })
  const { data: { data: stores } = {}, isLoading: storesLoading } =
    useList<StoreDto>({
      resource: user ? `users/${user.userId}/stores` : undefined,
      pagination: {
        current: 1,
        pageSize: 1000,
      },
      sorters: [{ field: 'name', order: 'asc' }],
      queryOptions: refineConfig.defaultUseListQueryOptions,
    })

  useEffect(() => {
    if (store) {
      document.title = `Merchant Portal - ${store.name}`
    } else {
      document.title = `Merchant Portal`
    }
  }, [store])

  const selectStores = useMemo(() => {
    if (store && stores) {
      if (!stores.find((x) => x._id === store._id)) {
        return [store, ...stores]
      }
    }
    return stores
  }, [stores, store])

  const isSettingsPage = pathname.includes('/settings/')

  if (isLoading || storesLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  if (!store && !error) {
    router.push(`/merchant?open-from=store-not-found`)
    return <></>
  }

  return (
    <>
      <DashboardLayout
        sidebarExpandedWidth={200}
        slots={{
          appTitle: () => (
            <CustomAppTitle currentStore={store} stores={selectStores ?? []} />
          ),
          toolbarActions: CustomActions,
          toolbarAccount: CustomAccount,
          sidebarFooter: ({ mini }) => (
            <CustomSidebarFooter storeId={storeId} mini={mini} />
          ),
        }}
        sx={{
          '& .MuiAppBar-root': {
            backgroundColor: '#073035',
            color: '#fff',
          },
        }}
      >
        <PageContainer
          maxWidth="xl"
          title={isSettingsPage ? 'Settings' : undefined}
        >
          {error && (
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Typography>
                Failed to load data. Please refresh the page to try again.
              </Typography>
            </Box>
          )}
          {!error && <>{children}</>}
        </PageContainer>
      </DashboardLayout>
    </>
  )
}

export default function StoreDashboardLayout({
  storeId,
  children,
}: Readonly<{
  storeId: string
  children: React.ReactNode
}>) {
  const { dataProvider } = useRefineDataProvider()

  const clerkAuth = useClerkAuth()
  const { user } = useAuth()
  const session: Session | undefined = useMemo(() => {
    return user
      ? {
          user: {
            id: user.userId,
            name: user.userInfo?.displayName,
            email: user.userInfo?.email,
          },
        }
      : undefined
  }, [user])

  const navigation: Navigation = [
    {
      segment: `merchant/store/${storeId}`,
      title: 'Home',
      icon: <HomeIcon />,
    },
    {
      segment: `merchant/store/${storeId}/campaigns`,
      title: 'Campaigns',
      icon: <RocketLaunchIcon />,
    },
    {
      segment: `merchant/store/${storeId}/offers`,
      title: 'Offer Popups',
      icon: <LocalOfferIcon />,
    },
    {
      segment: `merchant/store/${storeId}/insights`,
      title: 'Insights',
      icon: <AnalyticsIcon />,
    },
    {
      segment: `merchant/store/${storeId}/discounts`,
      title: 'Discounts',
      icon: <PercentIcon />,
    },
  ]

  return (
    <AppRouterCacheProvider options={{ enableCssLayer: true }}>
      <NextAppProvider
        theme={theme}
        navigation={navigation}
        branding={{
          title: `Store`,
          homeUrl: `/merchant/store/${storeId}`,
        }}
        session={session}
        authentication={{
          signIn: () => {},
          signOut: clerkAuth.signOut,
        }}
      >
        <GlobalStyles styles={{ html: { WebkitFontSmoothing: 'auto' } }} />
        <DialogsProvider>
          <RefineSnackbarProvider>
            <Refine
              dataProvider={dataProvider}
              routerProvider={routerProvider}
              notificationProvider={useNotificationProvider}
            >
              <StoreDashboardLayoutInner storeId={storeId}>
                {children}
              </StoreDashboardLayoutInner>
            </Refine>
          </RefineSnackbarProvider>
        </DialogsProvider>
      </NextAppProvider>
    </AppRouterCacheProvider>
  )
}
