'use client'

import { refineConfig } from '@/modules/config/refine'
import { StoreDto } from '@packages/shared-entities'
import { useOne } from '@refinedev/core'
import { useParams } from 'next/navigation'

export default function StoreDashboardPage() {
  const { storeId }: { storeId: string } = useParams()
  const { data: { data: store } = {} } = useOne<StoreDto>({
    resource: 'stores',
    id: storeId,
    queryOptions: refineConfig.defaultUseOneQueryOptions,
  })

  return (
    <>
      <div>Store: {store?.name}</div>
      <div>Website: {store?.website}</div>
      {store?.shopifyConfig && (
        <>
          <div>Shopify Shop: {store?.shopifyConfig?.myshopifyDomain}</div>
          <div>Shopify App: {store?.shopifyConfig?.appHandle}</div>
        </>
      )}
    </>
  )
}
