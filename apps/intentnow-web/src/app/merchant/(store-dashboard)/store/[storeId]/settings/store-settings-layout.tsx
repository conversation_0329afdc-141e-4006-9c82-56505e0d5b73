'use client'

import { Box, Container, Tab, Tabs } from '@mui/material'
import { usePathname } from 'next/navigation'
import { useRouter } from 'next/navigation'

export function StoreSettingsLayout({
  navigation,
  children,
}: Readonly<{
  navigation: {
    title: string
    rootPath: string
    startPath?: string
  }[]
  children: React.ReactNode
}>) {
  const router = useRouter()
  const pathname = usePathname()
  const currentIndex = navigation.findIndex((item) => {
    return pathname.startsWith(item.rootPath)
  })

  if (!navigation.length || currentIndex < 0) {
    return <></>
  }

  return (
    <>
      <Box
        sx={{
          flex: 1,
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Box
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
          }}
        >
          <Tabs
            value={currentIndex}
            variant="scrollable"
            scrollButtons="auto"
            onChange={(e, value) => {
              const newIndex = value as number
              const newSegment = navigation[newIndex]?.rootPath
              const newPath = navigation[newIndex]?.startPath
              if (newSegment) {
                router.push(newPath ?? newSegment)
              }
            }}
          >
            {navigation.map((item, index) => (
              <Tab key={index} label={item.title} value={index} />
            ))}
          </Tabs>
        </Box>
        <Box
          sx={{
            flex: 1,
            overflow: 'hidden',
            overflowY: 'auto',
            marginTop: '20px',
          }}
        >
          <Container
            maxWidth="md"
            sx={{
              flex: 1,
              display: 'flex',
            }}
          >
            {children}
          </Container>
        </Box>
      </Box>
    </>
  )
}
