'use client'

import { useParams } from 'next/navigation'
import { StoreSettingsLayout } from './store-settings-layout'

export default function StoreSettingsRootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { storeId }: { storeId: string } = useParams()

  return (
    <>
      <StoreSettingsLayout
        navigation={[
          {
            title: 'Store',
            rootPath: `/merchant/store/${storeId}/settings/store`,
          },
          {
            title: 'Integrations',
            rootPath: `/merchant/store/${storeId}/settings/integrations`,
            startPath: `/merchant/store/${storeId}/settings/integrations/shopify`,
          },
          {
            title: 'Billing',
            rootPath: `/merchant/store/${storeId}/settings/billing`,
          },
          {
            title: 'Supports',
            rootPath: `/merchant/store/${storeId}/settings/supports`,
          },
        ]}
      >
        {children}
      </StoreSettingsLayout>
    </>
  )
}
