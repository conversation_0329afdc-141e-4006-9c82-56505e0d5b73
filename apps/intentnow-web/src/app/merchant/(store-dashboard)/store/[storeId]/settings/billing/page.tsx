'use client'

import { Box } from '@mui/material'
import { StoreBillingSettingsCard } from '../store/store-billing-settings'
import { useParams } from 'next/navigation'

export default function StoreSettingsBillingPage() {
  const { storeId }: { storeId: string } = useParams()

  return (
    <>
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Box>
          <StoreBillingSettingsCard storeId={storeId} />
        </Box>
      </Box>
    </>
  )
}
