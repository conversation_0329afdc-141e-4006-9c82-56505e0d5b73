'use client'

import { Box, Tab, Tabs } from '@mui/material'
import { usePathname } from 'next/navigation'
import { useRouter } from 'next/navigation'

export function StoreSettingsSubLayout({
  navigation,
  children,
}: Readonly<{
  navigation: {
    title: string
    rootPath: string
  }[]
  children: React.ReactNode
}>) {
  const router = useRouter()
  const pathname = usePathname()
  const currentIndex = navigation.findIndex((item) => {
    return pathname.startsWith(item.rootPath)
  })

  if (!navigation.length || currentIndex < 0) {
    return <></>
  }

  return (
    <>
      <Box
        sx={{
          flex: 1,
          //backgroundColor: '#efefef',
          display: 'flex',
          flexDirection: 'row',
        }}
      >
        <Box
          sx={{
            borderRight: 1,
            borderColor: 'divider',
          }}
        >
          <Tabs
            orientation="vertical"
            TabIndicatorProps={{
              sx: {
                left: 0,
              },
            }}
            value={currentIndex}
            variant="scrollable"
            scrollButtons="auto"
            onChange={(e, value) => {
              const newIndex = value as number
              const newSegment = navigation[newIndex]?.rootPath
              if (newSegment) {
                router.push(newSegment)
              }
            }}
          >
            {navigation.map((item, index) => (
              <Tab
                key={index}
                label={item.title}
                value={index}
                sx={{
                  alignItems: 'flex-start',
                  minWidth: 150,
                  '&.Mui-selected': {
                    backgroundColor: '#efefef',
                  },
                }}
              />
            ))}
          </Tabs>
        </Box>
        <Box
          sx={{
            flex: 1,
            overflow: 'hidden',
            overflowY: 'auto',
            marginLeft: '20px',
          }}
        >
          {children}
        </Box>
      </Box>
    </>
  )
}
