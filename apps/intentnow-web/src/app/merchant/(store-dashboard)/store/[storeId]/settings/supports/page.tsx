'use client'

import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Grid2 as Grid,
  Typography,
} from '@mui/material'

export default function StoreSettingsSupportsPage() {
  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box>
        <Card
          sx={{
            marginBottom: '20px',
          }}
        >
          <CardHeader title="Support Channels" />
          <CardContent>
            <Grid container spacing={2}>
              <Grid size={4}>
                <Typography>Email</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </Typography>
              </Grid>

              <Grid size={4}>
                <Typography>Slack</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>Coming Soon</Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>
    </Box>
  )
}
