'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid2 as Grid,
  TextField,
  Typography,
} from '@mui/material'
import { Delete as DeleteIcon } from '@mui/icons-material'
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridValidRowModel,
} from '@mui/x-data-grid'
import { PublicUserDto, StoreAddUserDto } from '@packages/shared-entities'
import { List, SaveButton, useDataGrid } from '@refinedev/mui'
import { useMemo } from 'react'
import { ConfirmDialog2 } from '@/components/confirm-dialog'
import { useDialogs } from '@toolpad/core/useDialogs'
import { useAuth } from '@/modules/auth/auth'
import { HttpError, useDelete } from '@refinedev/core'
import {
  useModalForm,
  UseModalFormReturnType,
} from '@refinedev/react-hook-form'

export function StoreUserAccessSettingsCard({ storeId }: { storeId: string }) {
  const { user } = useAuth()
  const dialogs = useDialogs()

  const { mutateAsync: deleteUser } = useDelete()
  const resource = `stores/${storeId}/users`

  const createUserAccessModalFormProps = useModalForm<
    StoreAddUserDto,
    HttpError,
    StoreAddUserDto
  >({
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
    modalProps: {},
    refineCoreProps: { resource: resource, action: 'create' },
    syncWithLocation: false,
    warnWhenUnsavedChanges: true,
    defaultValues: {
      email: '',
    },
  })
  const {
    modal: { show: showCreateModal },
  } = createUserAccessModalFormProps

  const { dataGridProps } = useDataGrid<PublicUserDto>({
    resource: resource,
    sorters: {},
    syncWithLocation: false,
    queryOptions: refineConfig.defaultUseListQueryOptions,
  })
  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'email',
        headerName: 'Email',
        sortable: false,
        filterable: false,
        minWidth: 300,
      },
      {
        field: 'displayName',
        headerName: 'Name',
        flex: 1,
        sortable: false,
        filterable: false,
        minWidth: 200,
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Action',
        width: 150,
        sortable: false,
        filterable: false,
        getActions: ({ row }) => [
          <GridActionsCellItem
            key={2}
            icon={<DeleteIcon />}
            sx={{ padding: '2px 6px' }}
            label="Revoke"
            disabled={
              !user?.userInfo?.email || row.email === user.userInfo.email
            }
            onClick={async () => {
              const { answer } = await dialogs.open(ConfirmDialog2, {
                title: 'Revoke User Access',
                description: `You are about to revoke access for user "${row.email}". Do you want to continue?`,
              })
              if (answer) {
                await deleteUser({
                  resource: resource,
                  id: row._id,
                })
              }
            }}
          />,
        ],
      },
    ],
    [deleteUser, dialogs, resource, user]
  )

  return (
    <Box>
      <List
        title="Users with Access"
        headerButtons={() => (
          <Box display="flex" gap={2} alignItems="center">
            <Button
              variant="contained"
              onClick={() => {
                showCreateModal()
              }}
            >
              Add User
            </Button>
          </Box>
        )}
      >
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
      <AddUserAccessModal {...createUserAccessModalFormProps} />
    </Box>
  )
}

export function AddUserAccessModal({
  saveButtonProps,
  modal: { visible, close },
  register,
  formState: { errors },
}: UseModalFormReturnType<StoreAddUserDto, HttpError, StoreAddUserDto>) {
  return (
    <Dialog
      open={visible}
      //onClose={close}
      maxWidth={false}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '90vw',
          maxHeight: '90vw',
        },
      }}
    >
      <DialogTitle>Add User</DialogTitle>
      <DialogContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px',
          }}
        >
          <Box
            component="form"
            autoComplete="off"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '600px',
              gap: '10px',
            }}
          >
            <Grid
              container
              spacing={2}
              sx={{
                justifyItems: 'center',
                alignItems: 'center',
              }}
            >
              <Grid
                size={4}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography>User email</Typography>
              </Grid>
              <Grid
                size={8}
                sx={{
                  alignItems: 'center',
                }}
              >
                <TextField
                  id="name"
                  autoFocus
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Please enter a valid email',
                    },
                  })}
                  error={!!errors.email}
                  helperText={errors.email?.message}
                  margin="normal"
                  fullWidth
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={close}>
          Cancel
        </Button>
        <SaveButton {...saveButtonProps} variant="outlined">
          Add
        </SaveButton>
      </DialogActions>
    </Dialog>
  )
}
