'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Box,
  darken,
  Dialog,
  DialogContent,
  DialogTitle,
  lighten,
  styled,
  Theme,
  Grid2 as Grid,
  Typography,
  TextField,
  Button,
  DialogActions,
} from '@mui/material'
import {
  ContentCopy as CopyIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material'
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridValidRowModel,
} from '@mui/x-data-grid'
import {
  ActiveStoreLaunchConfigDto,
  StoreLaunchConfigCreateDto,
  StoreLaunchConfigDto,
} from '@packages/shared-entities'
import { List, SaveButton, useDataGrid } from '@refinedev/mui'
import { useCallback, useMemo } from 'react'
import { HttpError, useOne } from '@refinedev/core'
import {
  useModalForm,
  UseModalFormReturnType,
} from '@refinedev/react-hook-form'
import { J<PERSON><PERSON>ree, KeyPath } from 'react-json-tree'
import { useFieldArray } from 'react-hook-form'
import { useAuth } from '@/modules/auth/auth'

const getBackgroundColor = (
  color: string,
  theme: Theme,
  coefficient: number
) => ({
  backgroundColor: darken(color, coefficient),
  ...theme.applyStyles('light', {
    backgroundColor: lighten(color, coefficient),
  }),
})

const StyledDataGrid = styled(DataGrid)(({ theme }) => ({
  '& .row-theme--Acive': {
    ...getBackgroundColor('#B2F10C', theme, 0.7),
    '&:hover': {
      ...getBackgroundColor('#B2F10C', theme, 0.6),
    },
    '&.Mui-selected': {
      ...getBackgroundColor(theme.palette.success.main, theme, 0.5),
      '&:hover': {
        ...getBackgroundColor(theme.palette.success.main, theme, 0.4),
      },
    },
  },
}))

export function StoreLaunchConfigSettingsCard({
  storeId,
}: {
  storeId: string
}) {
  const { user } = useAuth()
  const resource = `stores/${storeId}/launch-configs`

  const {
    data: { data: activeLaunchConfig } = {},
    refetch: activeLaunchConfigRefetch,
  } = useOne<ActiveStoreLaunchConfigDto>({
    resource: `stores/${storeId}`,
    id: 'active-launch-config',
    queryOptions: {
      enabled: true,
      refetchOnWindowFocus: 'always',
      refetchOnReconnect: 'always',
      refetchOnMount: 'always',
      retry: 3,
    },
  })
  const activeLaunchConfigId =
    activeLaunchConfig?.activeLaunchConfig?.launchConfig?._id

  const { dataGridProps, setCurrent } = useDataGrid<StoreLaunchConfigDto>({
    resource: resource,
    sorters: {
      initial: [{ field: 'createdAt', order: 'desc' }],
    },
    pagination: {
      pageSize: 5,
    },
    syncWithLocation: false,
    queryOptions: refineConfig.defaultUseListQueryOptions,
  })
  const latestLaunchConfig: StoreLaunchConfigDto | undefined =
    dataGridProps.rows[0]

  const createUserAccessModalFormProps = useModalForm<
    StoreLaunchConfigCreateDto,
    HttpError,
    StoreLaunchConfigCreateDto
  >({
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
    modalProps: {},
    refineCoreProps: {
      resource: resource,
      action: 'create',
      onMutationSuccess: async () => {
        await activeLaunchConfigRefetch()
        setCurrent(1)
      },
    },
    syncWithLocation: false,
    warnWhenUnsavedChanges: true,
    resetOptions: {
      keepDirty: false,
      keepDefaultValues: false,
      keepErrors: false,
      keepTouched: false,
      keepIsValid: false,
      keepIsSubmitted: false,
      keepSubmitCount: false,
    },
  })
  const {
    modal: { show: showCreateModal, visible: createModalVisible },
    reset: resetCreateModal,
  } = createUserAccessModalFormProps

  const defaultNewConfigName = useCallback(() => {
    const now = new Date()
    const configName = `${now.getFullYear()}-${now.getMonth() >= 9 ? now.getMonth() + 1 : `0${now.getMonth() + 1}`}-${now.getDate() >= 10 ? now.getDate() : `0${now.getDate()}`}`

    return configName
  }, [])

  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'name',
        headerName: 'Name',
        width: 100,
        sortable: false,
        filterable: false,
      },
      {
        field: 'createdAt',
        headerName: 'Created',
        width: 180,
        sortable: false,
        filterable: false,
        valueFormatter: (values: string) => {
          return new Date(values).toLocaleString()
        },
      },
      {
        field: 'configVariants',
        headerName: 'Variants',
        flex: 1,
        sortable: false,
        filterable: false,
        renderCell: (params) => {
          return (
            <Box
              sx={{
                flex: 1,
                height: '100%',
                overflow: 'auto',
                minHeight: '50px',
              }}
            >
              <JSONTree
                theme={{
                  scheme: 'monokai',
                  author: 'wimer hazenberg (http://www.monokai.nl)',
                  base00: '#272822',
                  base01: '#383830',
                  base02: '#49483e',
                  base03: '#75715e',
                  base04: '#a59f85',
                  base05: '#f8f8f2',
                  base06: '#f5f4f1',
                  base07: '#f9f8f5',
                  base08: '#f92672',
                  base09: '#fd971f',
                  base0A: '#f4bf75',
                  base0B: '#a6e22e',
                  base0C: '#a1efe4',
                  base0D: '#66d9ef',
                  base0E: '#ae81ff',
                  base0F: '#cc6633',
                }}
                invertTheme={true}
                data={{
                  ...params.row.configVariants,
                }}
                shouldExpandNodeInitially={(
                  keyPath: KeyPath,
                  data: unknown,
                  level: number
                ) => level > 0}
              />
            </Box>
          )
        },
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Action',
        width: 100,
        sortable: false,
        filterable: false,
        getActions: ({ row }) => [
          <GridActionsCellItem
            key={2}
            icon={<CopyIcon />}
            sx={{ padding: '2px 6px' }}
            label="Duplicate"
            onClick={async () => {
              if (!createModalVisible) {
                resetCreateModal({
                  name: defaultNewConfigName(),
                  configVariants: row?.configVariants ?? [],
                })

                showCreateModal()
              }
            }}
          />,
        ],
      },
    ],
    [
      createModalVisible,
      defaultNewConfigName,
      resetCreateModal,
      showCreateModal,
    ]
  )

  if (!user?.roles?.admin) {
    return <></>
  }

  return (
    <Box>
      <List
        title="[Internal] Launch Configs"
        createButtonProps={{
          onClick: () => {
            if (!createModalVisible) {
              resetCreateModal({
                name: defaultNewConfigName(),
                configVariants: latestLaunchConfig?.configVariants ?? [],
              })

              showCreateModal()
            }
          },
          variant: 'contained',
        }}
      >
        <StyledDataGrid
          getRowHeight={() => 'auto'}
          getRowClassName={(params) =>
            activeLaunchConfigId === params.row._id ? `row-theme--Acive` : ''
          }
          {...dataGridProps}
          columns={columns}
        />
      </List>
      <CreateStoreLaunchConfigModal {...createUserAccessModalFormProps} />
    </Box>
  )
}

export function CreateStoreLaunchConfigModal({
  saveButtonProps,
  modal: { visible, close },
  register,
  control,
  watch,
  formState: { errors, isLoading, isValidating, isReady },
}: UseModalFormReturnType<
  StoreLaunchConfigCreateDto,
  HttpError,
  StoreLaunchConfigCreateDto
>) {
  const {
    fields: variantFields,
    append: variantAppend,
    remove: variantRemove,
  } = useFieldArray({
    name: 'configVariants',
    control,
    rules: {
      required: 'Minimum 1 variant is required',
      minLength: {
        value: 1,
        message: 'Minimum 1 variant is required',
      },
      validate: (variants) => {
        for (const variant of variants) {
          if (variant.modelConfigV1) {
            if (variant.modelConfigV1.floor >= variant.modelConfigV1.ceiling) {
              return 'Floor must be less than ceiling'
            }
            if (variant.modelConfigV1.start >= variant.modelConfigV1.end) {
              return 'Start must be less than end'
            }
          }
        }
        return true
      },
    },
  })

  if (!visible || !isReady || isLoading || isValidating) {
    return <></>
  }

  console.debug('watch', watch())
  console.debug('errors', errors)

  return (
    <Dialog
      open={visible}
      onClose={close}
      maxWidth={false}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '90vw',
          maxHeight: '90vh',
          backgroundColor: 'white',
        },
      }}
    >
      <DialogTitle>Create Launch Config</DialogTitle>
      <DialogContent>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px',
          }}
        >
          <Box
            component="form"
            autoComplete="off"
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '700px',
              gap: '10px',
            }}
          >
            <Grid
              container
              spacing={2}
              sx={{
                justifyItems: 'center',
                alignItems: 'center',
              }}
            >
              <Grid
                size={2}
                sx={{
                  alignItems: 'center',
                }}
              >
                <Typography>Config Name</Typography>
              </Grid>
              <Grid
                size={10}
                sx={{
                  alignItems: 'center',
                }}
              >
                <TextField
                  {...register('name', {
                    required: 'Config name is required',
                  })}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  margin="normal"
                  fullWidth
                />
              </Grid>

              <Grid
                size={2}
                sx={{
                  alignItems: 'top',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'top' }}>
                  <Typography
                    sx={{
                      alignContent: 'center',
                    }}
                  >
                    Config Variants
                  </Typography>
                  <Button
                    size="small"
                    onClick={() => {
                      variantAppend({
                        modelConfigV1: {
                          model: '',
                          floor: 0,
                          ceiling: 0,
                          start: 1,
                          end: 1,
                        },
                      })
                    }}
                  >
                    <AddIcon />
                  </Button>
                </Box>
              </Grid>
              <Grid
                size={10}
                sx={{
                  alignItems: 'center',
                }}
              >
                {Boolean(errors.configVariants?.root) && (
                  <Typography color="error">
                    {errors.configVariants?.root?.message}
                  </Typography>
                )}
                {variantFields.map((variant, index) => {
                  return (
                    <Box
                      key={`variant-${index}`}
                      sx={{
                        border: 0.5,
                        borderRadius: 1,
                        margin: '5px',
                        padding: '5px',
                        paddingTop: '10px',
                        display: 'flex',
                        flexDirection: 'column',
                        gap: 2,
                        //alignItems: 'center',
                      }}
                    >
                      <Grid container spacing={2}>
                        <Grid size={12}>
                          <TextField
                            label="Model"
                            type="string"
                            fullWidth
                            {...register(
                              `configVariants.${index}.modelConfigV1.model`,
                              {
                                required: 'Required',
                              }
                            )}
                            error={
                              !!errors.configVariants?.[index]?.modelConfigV1
                                ?.model
                            }
                            helperText={
                              errors.configVariants?.[index]?.modelConfigV1
                                ?.model?.message
                            }
                          />
                        </Grid>
                        <Grid size={3}>
                          <TextField
                            label="Floor"
                            type="number"
                            sx={{
                              // border: 'none',
                              // boxShadow: 'none',
                              width: '100px',
                            }}
                            {...register(
                              `configVariants.${index}.modelConfigV1.floor`,
                              {
                                required: 'Required',
                                min: {
                                  value: 0,
                                  message: '>=0',
                                },
                                max: {
                                  value: 1,
                                  message: '<=1',
                                },
                                valueAsNumber: true,
                              }
                            )}
                            error={
                              !!errors.configVariants?.[index]?.modelConfigV1
                                ?.floor
                            }
                            helperText={
                              errors.configVariants?.[index]?.modelConfigV1
                                ?.floor?.message
                            }
                            inputProps={{
                              step: 0.01,
                            }}
                          />
                        </Grid>

                        <Grid size={3}>
                          <TextField
                            label="Ceiling"
                            type="number"
                            sx={{
                              // border: 'none',
                              // boxShadow: 'none',
                              width: '100px',
                            }}
                            {...register(
                              `configVariants.${index}.modelConfigV1.ceiling`,
                              {
                                required: 'Required',
                                min: {
                                  value: 0,
                                  message: '>=0',
                                },
                                max: {
                                  value: 1,
                                  message: '<=1',
                                },
                                valueAsNumber: true,
                              }
                            )}
                            error={
                              !!errors.configVariants?.[index]?.modelConfigV1
                                ?.ceiling
                            }
                            helperText={
                              errors.configVariants?.[index]?.modelConfigV1
                                ?.ceiling?.message
                            }
                            inputProps={{
                              step: 0.01,
                            }}
                          />
                        </Grid>

                        <Grid size={3}>
                          <TextField
                            label="Start"
                            type="number"
                            sx={{
                              // border: 'none',
                              // boxShadow: 'none',
                              width: '100px',
                            }}
                            {...register(
                              `configVariants.${index}.modelConfigV1.start`,
                              {
                                required: 'Required',
                                min: {
                                  value: 1,
                                  message: '>0',
                                },
                                valueAsNumber: true,
                              }
                            )}
                            error={
                              !!errors.configVariants?.[index]?.modelConfigV1
                                ?.start
                            }
                            helperText={
                              errors.configVariants?.[index]?.modelConfigV1
                                ?.start?.message
                            }
                            inputProps={{
                              step: 1,
                            }}
                          />
                        </Grid>

                        <Grid size={3}>
                          <TextField
                            label="End"
                            type="number"
                            sx={{
                              // border: 'none',
                              // boxShadow: 'none',
                              width: '100px',
                            }}
                            {...register(
                              `configVariants.${index}.modelConfigV1.end`,
                              {
                                required: 'Required',
                                min: {
                                  value: 1,
                                  message: '>0',
                                },
                                valueAsNumber: true,
                              }
                            )}
                            error={
                              !!errors.configVariants?.[index]?.modelConfigV1
                                ?.end
                            }
                            helperText={
                              errors.configVariants?.[index]?.modelConfigV1?.end
                                ?.message
                            }
                            inputProps={{
                              step: 1,
                            }}
                          />
                        </Grid>
                      </Grid>

                      <Button
                        variant="outlined"
                        size="small"
                        sx={{ width: '150px' }}
                        onClick={() => {
                          variantRemove(index)
                        }}
                      >
                        <DeleteIcon />
                        Delete Variant
                      </Button>
                    </Box>
                  )
                })}
              </Grid>
            </Grid>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={close}>
          Cancel
        </Button>
        <SaveButton {...saveButtonProps} variant="outlined">
          Create
        </SaveButton>
      </DialogActions>
    </Dialog>
  )
}
