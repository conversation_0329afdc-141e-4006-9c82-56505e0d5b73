'use client'

import { useParams } from 'next/navigation'
import { StoreSettingsSubLayout } from '../store-settings-sub-layout'

export default function StoreSettingsIntegrationsLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { storeId }: { storeId: string } = useParams()

  return (
    <>
      <StoreSettingsSubLayout
        navigation={[
          {
            title: 'Shopify Integration',
            rootPath: `/merchant/store/${storeId}/settings/integrations/shopify`,
          },
        ]}
      >
        {children}
      </StoreSettingsSubLayout>
    </>
  )
}
