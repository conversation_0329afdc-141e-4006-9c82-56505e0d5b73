'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Card,
  CardContent,
  CardHeader,
  Grid2 as Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material'
import {
  Edit as EditIcon,
  Check as CheckIcon,
  Clear as ClearIcon,
} from '@mui/icons-material'
import { StoreDto } from '@packages/shared-entities'
import { useAuth } from '@/modules/auth/auth'
import { useState } from 'react'
import { useForm } from '@refinedev/react-hook-form'

export function StoreShopifyConfigSettingsCard({
  storeId,
  onChange,
}: {
  storeId: string
  onChange: () => void
}) {
  const { user } = useAuth()
  const [editing, setEditing] = useState(false)

  const {
    register,
    reset,
    getValues,
    formState: { errors, isDirty, isValid, isReady },
    refineCore: {
      query,
      mutation: { mutateAsync: updateStore },
    },
  } = useForm<StoreDto>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    refineCoreProps: {
      resource: 'stores',
      id: storeId,
      action: 'edit',
      queryOptions: refineConfig.defaultUseOneQueryOptions,
    },
    resetOptions: {
      keepDirty: false,
      keepDefaultValues: false,
      keepErrors: false,
      keepTouched: false,
      keepIsValid: false,
      keepIsSubmitted: false,
      keepSubmitCount: false,
    },
  })
  const store = query?.data?.data

  if (!store) {
    return <></>
  }

  console.debug('errors', errors)

  return (
    <Card
      sx={{
        marginBottom: '20px',
      }}
    >
      <CardHeader
        title="[Internal] Integration Config"
        action={
          user?.roles?.admin && (
            <>
              {editing && (
                <>
                  <IconButton
                    disabled={!isDirty || !isValid}
                    onClick={async () => {
                      if (isValid) {
                        await updateStore({
                          resource: 'stores',
                          id: store._id,
                          values: {
                            shopifyConfig: getValues(
                              'shopifyConfig.myshopifyDomain'
                            )
                              ? {
                                  myshopifyDomain:
                                    getValues(
                                      'shopifyConfig.myshopifyDomain'
                                    ) ?? undefined,
                                  appHandle:
                                    getValues('shopifyConfig.appHandle') ??
                                    undefined,
                                }
                              : null,
                          },
                        })

                        onChange()
                      }
                      setEditing(false)
                    }}
                  >
                    <CheckIcon />
                  </IconButton>
                  <IconButton
                    onClick={() => {
                      reset(query?.data?.data)
                      setEditing(false)
                    }}
                  >
                    <ClearIcon />
                  </IconButton>
                </>
              )}
              {!editing && (
                <IconButton
                  disabled={!isReady}
                  onClick={() => {
                    if (isReady && query?.data?.data) {
                      reset(query?.data?.data)
                      setEditing(true)
                    }
                  }}
                >
                  <EditIcon />
                </IconButton>
              )}
            </>
          )
        }
      />
      <CardContent>
        <Grid container spacing={2}>
          <Grid size={4}>
            <Typography>Myshopify Domain</Typography>
          </Grid>
          <Grid size={8}>
            {editing && (
              <>
                <TextField
                  type="string"
                  {...register('shopifyConfig.myshopifyDomain', {
                    required: false,
                    validate: (value) => {
                      if (value && !getValues('shopifyConfig.appHandle')) {
                        return `Myshopify domain can't be set when app handle is empty`
                      } else if (
                        !value &&
                        getValues('shopifyConfig.appHandle')
                      ) {
                        return `Myshopify domain can't be empty when app handle is set`
                      }
                      return true
                    },
                  })}
                  error={!!(errors.shopifyConfig as any)?.myshopifyDomain}
                  helperText={String(
                    (errors.shopifyConfig as any)?.myshopifyDomain?.message ??
                      ''
                  )}
                  fullWidth
                />
              </>
            )}
            {!editing && (
              <Typography>{store.shopifyConfig?.myshopifyDomain}</Typography>
            )}
          </Grid>
          <Grid size={4}>
            <Typography>App Handle</Typography>
          </Grid>
          <Grid size={8}>
            {editing && (
              <>
                <TextField
                  type="string"
                  {...register('shopifyConfig.appHandle', {
                    required: false,
                    validate: (value) => {
                      if (
                        value &&
                        !getValues('shopifyConfig.myshopifyDomain')
                      ) {
                        return `App handle can't be set when myshopify domain is empty`
                      } else if (
                        !value &&
                        getValues('shopifyConfig.myshopifyDomain')
                      ) {
                        return `App handle can't be empty when myshopify domain is set`
                      }
                      return true
                    },
                  })}
                  error={!!(errors.shopifyConfig as any)?.appHandle}
                  helperText={String(
                    (errors.shopifyConfig as any)?.appHandle?.message ?? ''
                  )}
                  fullWidth
                />
              </>
            )}
            {!editing && (
              <Typography>{store.shopifyConfig?.appHandle}</Typography>
            )}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}
