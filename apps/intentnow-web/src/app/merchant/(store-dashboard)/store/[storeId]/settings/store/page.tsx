'use client'

import { refineConfig } from '@/modules/config/refine'
import { Box } from '@mui/material'
import { StoreDto } from '@packages/shared-entities'
import { useParams } from 'next/navigation'
import { useAuth } from '@/modules/auth/auth'
import { StoreUserAccessSettingsCard } from '@/app/merchant/(store-dashboard)/store/[storeId]/settings/store/store-user-access-settings'
import { StoreConfigSettingsCard } from './store-config-settings'
import { useOne } from '@refinedev/core'
import { StoreLaunchConfigSettingsCard } from './store-launchconfig-settings'
import { StoreInfoSettingsCard } from './store-info-settings'

export default function StoreSettingsStoreAccessPage() {
  const { storeId }: { storeId: string } = useParams()
  const { user } = useAuth()

  const { data: { data: store } = {} } = useOne<StoreDto>({
    resource: 'stores',
    id: storeId,
    queryOptions: refineConfig.defaultUseOneQueryOptions,
  })

  if (!store) {
    return <></>
  }

  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box>
        <StoreInfoSettingsCard storeId={storeId} />
      </Box>

      <Box
        sx={{
          marginBottom: '20px',
        }}
      >
        <StoreUserAccessSettingsCard storeId={storeId} />
      </Box>

      {user?.roles?.admin && store.effectiveConfig && (
        <>
          <Box
            sx={{
              marginBottom: '20px',
            }}
          >
            <StoreConfigSettingsCard storeId={storeId} />
          </Box>
          <Box
            sx={{
              marginBottom: '20px',
            }}
          >
            <StoreLaunchConfigSettingsCard storeId={storeId} />
          </Box>
        </>
      )}
    </Box>
  )
}
