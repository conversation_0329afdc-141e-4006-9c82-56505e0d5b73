'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Card,
  CardContent,
  CardHeader,
  Grid2 as Grid,
  IconButton,
  Typography,
} from '@mui/material'
import { OpenInNew as OpenInNewIcon } from '@mui/icons-material'
import { StoreBillingDto } from '@packages/shared-entities'
import { useOne } from '@refinedev/core'

export function StoreBillingSettingsCard({ storeId }: { storeId: string }) {
  const { data: { data: storeBilling } = {} } = useOne<StoreBillingDto>({
    resource: `stores/${storeId}`,
    id: 'billing',
    queryOptions: refineConfig.defaultUseOneQueryOptions,
  })

  if (!storeBilling) {
    return <></>
  }

  return (
    <Card
      sx={{
        marginBottom: '20px',
      }}
    >
      <CardHeader
        title="Subscription"
        action={
          <>
            {storeBilling.stripe?.billingPortalUrl && (
              <IconButton
                onClick={() => {
                  if (storeBilling?.stripe?.billingPortalUrl)
                    window.open(storeBilling.stripe.billingPortalUrl, '_blank')
                }}
              >
                <OpenInNewIcon />
              </IconButton>
            )}
          </>
        }
      />
      <CardContent>
        <Grid container spacing={2}>
          <Grid size={4}>
            <Typography>Status</Typography>
          </Grid>
          <Grid size={8}>
            <Typography>
              {storeBilling.subscription?.status?.toUpperCase() ??
                'No Subscription'}
            </Typography>
          </Grid>
          {storeBilling.subscription && (
            <>
              <Grid size={4}>
                <Typography>Subscription</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>{storeBilling.subscription.productName}</Typography>
              </Grid>

              <Grid size={4}>
                <Typography>Renew Date</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  {storeBilling.subscription.renewsAt
                    ? new Date(
                        storeBilling.subscription.renewsAt
                      ).toLocaleDateString()
                    : 'N/A'}
                </Typography>
              </Grid>

              <Grid size={4}>
                <Typography>Price</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  {`${storeBilling.subscription.price.amount} ${storeBilling.subscription.price.currency} / ${
                    storeBilling.subscription.price.interval
                  }`}
                </Typography>
              </Grid>

              <Grid size={4}>
                <Typography>Payment</Typography>
              </Grid>
              <Grid size={8}>
                <Typography>
                  {storeBilling.subscription.payment
                    ? `${storeBilling.subscription.payment.card.brand.toUpperCase()} ●●●●${storeBilling.subscription.payment.card.last4}`
                    : undefined}
                </Typography>
              </Grid>
            </>
          )}
        </Grid>
      </CardContent>
    </Card>
  )
}
