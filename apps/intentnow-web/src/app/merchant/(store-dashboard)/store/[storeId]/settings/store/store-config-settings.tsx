'use client'

import { refineConfig } from '@/modules/config/refine'
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid2 as Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material'
import {
  Edit as EditIcon,
  Check as CheckIcon,
  Clear as ClearIcon,
} from '@mui/icons-material'
import { StoreDto } from '@packages/shared-entities'
import { useState } from 'react'
import { useForm } from '@refinedev/react-hook-form'
import { OptionalSwitch } from '@/components/optional-switch'
import { Controller } from 'react-hook-form'
import { useAuth } from '@/modules/auth/auth'

export function StoreConfigSettingsCard({ storeId }: { storeId: string }) {
  const { user } = useAuth()
  const [editingConfig, setEditingConfig] = useState(false)

  const {
    register,
    reset,
    control,
    getValues,
    formState: { errors, isDirty, isValid },
    refineCore: {
      query,
      mutation: { mutateAsync: updateStore },
    },
  } = useForm<StoreDto>({
    mode: 'onChange',
    refineCoreProps: {
      resource: 'stores',
      id: storeId,
      action: 'edit',
      queryOptions: refineConfig.defaultUseOneQueryOptions,
    },
  })
  const store = query?.data?.data

  if (!user?.roles?.admin || !store) {
    return <></>
  }

  console.debug('errors', errors)

  return (
    <Card>
      <CardHeader
        title="[Internal] Store Config"
        action={
          editingConfig ? (
            <>
              <IconButton
                disabled={!isDirty || !isValid}
                onClick={async () => {
                  if (!isValid) {
                    return
                  }
                  await updateStore({
                    resource: 'stores',
                    id: store._id,
                    values: {
                      config: {
                        receiveEvents:
                          getValues('config.receiveEvents') ?? undefined,
                        sendAmplitude:
                          getValues('config.sendAmplitude') ?? undefined,
                        showOffers: getValues('config.showOffers') ?? undefined,
                        predictOffers:
                          getValues('config.predictOffers') ?? undefined,
                        eventShadowRatio:
                          getValues('config.eventShadowRatio') ?? undefined,
                        isLegacy: getValues('config.isLegacy') ?? undefined,
                        modelOverrides:
                          getValues('config.modelOverrides') ?? undefined,
                        stripeSubscriptionId:
                          getValues('config.stripeSubscriptionId') ?? undefined,
                      },
                    },
                  })
                  setEditingConfig(false)
                }}
              >
                <CheckIcon />
              </IconButton>
              <IconButton
                onClick={() => {
                  reset(query?.data?.data, {
                    keepDirty: false,
                    keepDefaultValues: false,
                    keepErrors: false,
                    keepTouched: false,
                    keepIsValid: false,
                    keepIsSubmitted: false,
                    keepSubmitCount: false,
                  })
                  setEditingConfig(false)
                }}
              >
                <ClearIcon />
              </IconButton>
            </>
          ) : (
            <IconButton
              onClick={() => {
                setEditingConfig(true)
              }}
            >
              <EditIcon />
            </IconButton>
          )
        }
      />
      <CardContent>
        <Grid container spacing={2}>
          <Grid size={4}>
            <Typography>Receive Events</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.receiveEvents', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return store.effectiveConfig ? (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig.receiveEvents}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  ) : (
                    <> </>
                  )
                }}
              />
            </Box>
          </Grid>
          <Grid size={4}>
            <Typography>Send Amplitude</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.sendAmplitude', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig!.sendAmplitude}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  )
                }}
              />
            </Box>
          </Grid>
          <Grid size={4}>
            <Typography>Show Offers</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.showOffers', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return store.effectiveConfig ? (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig.showOffers}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  ) : (
                    <> </>
                  )
                }}
              />
            </Box>
          </Grid>
          <Grid size={4}>
            <Typography>Predict Offers</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.predictOffers', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return store.effectiveConfig ? (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig.predictOffers}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  ) : (
                    <> </>
                  )
                }}
              />
            </Box>
          </Grid>

          <Grid size={4}>
            <Typography>Legacy Store</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.isLegacy', {
                  required: false,
                })}
                control={control}
                render={({ field: { onChange, value, ...restField } }) => {
                  return store.effectiveConfig ? (
                    <OptionalSwitch
                      value={value}
                      effectiveValue={store.effectiveConfig.isLegacy}
                      editing={editingConfig}
                      onChange={(value) => {
                        onChange(value)
                      }}
                      {...restField}
                    />
                  ) : (
                    <> </>
                  )
                }}
              />
            </Box>
          </Grid>

          <Grid size={4}>
            <Typography>Even Shadow Ratio</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              {editingConfig && (
                <>
                  <TextField
                    type="number"
                    {...register('config.eventShadowRatio', {
                      required: false,
                      valueAsNumber: true,
                      min: {
                        value: 0,
                        message: '>=0',
                      },
                      max: {
                        value: 1,
                        message: '<=1',
                      },
                    })}
                    error={!!(errors.config as any)?.eventShadowRatio}
                    helperText={String(
                      (errors.config as any)?.eventShadowRatio?.message ?? ''
                    )}
                    inputProps={{
                      step: 0.01,
                      min: 0,
                      max: 1,
                    }}
                    sx={{
                      width: '100px',
                    }}
                  />
                </>
              )}
              {!editingConfig && (
                <Typography>
                  {store.effectiveConfig?.eventShadowRatio}
                </Typography>
              )}
            </Box>
          </Grid>

          <Grid size={4}>
            <Typography>Model Overrides</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <Controller
                {...register('config.modelOverrides', {
                  required: false,
                })}
                control={control}
                render={({ field: { value, onChange } }) => {
                  if (editingConfig) {
                    return (
                      <TextField
                        fullWidth
                        defaultValue={value ? JSON.stringify(value) : ''}
                        onChange={(event) => {
                          try {
                            const newValue = event.target.value
                              ? JSON.parse(event.target.value)
                              : undefined
                            onChange(newValue)
                          } catch (e) {
                            //ignore
                          }
                        }}
                      />
                    )
                  } else {
                    return (
                      <Typography>
                        {value ? JSON.stringify(value) : ''}
                      </Typography>
                    )
                  }
                }}
              />
            </Box>
          </Grid>

          <Grid size={4}>
            <Typography>Stripe Subscription ID</Typography>
          </Grid>
          <Grid size={8}>
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              {editingConfig && (
                <>
                  <TextField
                    type="string"
                    {...register('config.stripeSubscriptionId', {
                      required: false,
                    })}
                    error={!!(errors.config as any)?.stripeSubscriptionId}
                    helperText={String(
                      (errors.config as any)?.stripeSubscriptionId?.message ??
                        ''
                    )}
                    fullWidth
                  />
                </>
              )}
              {!editingConfig && (
                <Typography>
                  {store.effectiveConfig?.stripeSubscriptionId}
                </Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}
