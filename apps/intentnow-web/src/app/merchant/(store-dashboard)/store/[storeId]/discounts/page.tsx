'use client'

import { refineConfig } from '@/modules/config/refine'
import { Box } from '@mui/material'
import { OpenInNew as OpenInNewIcon } from '@mui/icons-material'
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridValidRowModel,
} from '@mui/x-data-grid'
import { List, useDataGrid } from '@refinedev/mui'
import { useParams } from 'next/navigation'
import { useMemo } from 'react'
import {
  StoreOfferDto,
  StoreCampaignDto,
  StoreGeneratedDiscountDto,
} from '@packages/shared-entities'
import { useMany } from '@refinedev/core'
import _ from 'lodash'

export default function StoreDiscountsPage() {
  const { storeId }: { storeId: string } = useParams()
  const resource = `stores/${storeId}/generated-discounts`

  const { dataGridProps } = useDataGrid<StoreGeneratedDiscountDto>({
    resource,
    sorters: {
      initial: [
        {
          field: 'createdAt',
          order: 'desc',
        },
      ],
    },
    syncWithLocation: false,
    queryOptions: refineConfig.defaultUseListQueryOptions,
  })

  const campaignIds = useMemo(() => {
    return _.uniq(
      dataGridProps.rows
        .map((row) => row.source?.campaignId as string)
        .filter(Boolean)
    )
  }, [dataGridProps.rows])
  const offerIds = useMemo(() => {
    return _.uniq(
      dataGridProps.rows
        .map((row) => row.source?.offerId as string)
        .filter(Boolean)
    )
  }, [dataGridProps.rows])

  const { data: { data: campaigns } = {}, isLoading: campaignsLoading } =
    useMany<StoreCampaignDto>({
      resource: `stores/${storeId}/campaigns`,
      ids: campaignIds,
      queryOptions: {
        enabled: campaignIds.length > 0,
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        refetchOnMount: false,
        retry: 0,
      },
    })

  const { data: { data: offers } = {}, isLoading: offersLoading } =
    useMany<StoreOfferDto>({
      resource: `stores/${storeId}/offers`,
      ids: offerIds,
      queryOptions: {
        enabled: offerIds.length > 0,
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        refetchOnMount: false,
        retry: 0,
      },
    })

  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'discountContent.code',
        headerName: 'Discoutn Code',
        sortable: false,
        filterable: false,
        width: 150,
        valueGetter: (value, row) => {
          return row.discountContent?.code
        },
      },
      // {
      //   field: 'createdAt',
      //   headerName: 'Created',
      //   width: 180,
      //   sortable: false,
      //   filterable: false,
      //   valueFormatter: (values: string) => {
      //     return new Date(values).toLocaleString()
      //   },
      // },
      {
        field: 'startsAt',
        headerName: 'Starts',
        width: 180,
        sortable: false,
        filterable: false,
        valueGetter: (value, row) => {
          return new Date(row.discountContent?.startsAt).toLocaleString()
        },
      },
      {
        field: 'endsAt',
        headerName: 'Ends',
        width: 180,
        sortable: false,
        filterable: false,
        valueGetter: (value, row) => {
          return new Date(row.discountContent?.endsAt).toLocaleString()
        },
      },
      {
        field: 'source.campaignId',
        headerName: 'Campaign',
        width: 200,
        sortable: false,
        filterable: false,
        flex: 1,
        valueGetter: (value, row) => {
          const campaignId = row.source?.campaignId
          const campaign = campaigns?.find((x) => x._id === campaignId)
          return campaign
            ? campaign.name
            : campaignsLoading
              ? 'Loading'
              : campaignId
        },
      },
      {
        field: 'source.offerId',
        headerName: 'Offer Popup',
        width: 200,
        sortable: false,
        filterable: false,
        flex: 1,
        valueGetter: (value, row) => {
          const offerId = row.source?.offerId
          const offer = offers?.find((x) => x._id === offerId)
          return offer ? offer.name : offersLoading ? 'Loading' : offerId
        },
      },
      {
        field: 'status',
        headerName: 'Status',
        width: 100,
        sortable: false,
        filterable: false,
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Action',
        width: 80,
        sortable: false,
        filterable: false,
        getActions: ({ row }) => [
          <GridActionsCellItem
            key={2}
            icon={<OpenInNewIcon />}
            sx={{ padding: '2px 6px' }}
            label="Open in Shopify Admin"
            onClick={() => {
              const generatedDiscount = row as StoreGeneratedDiscountDto
              const parts =
                generatedDiscount.discountData.discountRef.split('/')
              if (parts?.length) {
                const discountId = parts[parts.length - 1]
                const shopifyAdminLink = `https://${generatedDiscount.discountData.storeRef}/admin/discounts/${discountId}`
                window.open(shopifyAdminLink, '_blank')
              }
            }}
          />,
        ],
      },
    ],
    [campaigns, campaignsLoading, offers, offersLoading]
  )

  return (
    <Box>
      <List>
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </Box>
  )
}
