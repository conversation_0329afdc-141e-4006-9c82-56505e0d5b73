import { clerkMiddleware } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import { clerkConfig } from '@/modules/config/clerk'

const tenantKeys = {
  default: {
    publishableKey: clerkConfig.publishableKey,
    secretKey: clerkConfig.secretKey,
  },
}

export default clerkMiddleware(
  async () => {
    return NextResponse.next()
  },
  () => {
    // Resolve tenant based on the request
    return tenantKeys.default
  }
)

export const config = {
  matcher: ['/admin2/:path*', '/merchant/:path*'],
}
