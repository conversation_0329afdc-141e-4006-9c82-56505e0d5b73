import { formatIs, rankWith, uiTypeIs } from '@jsonforms/core'
import { materialRenderers } from '@jsonforms/material-renderers'
import { ColorPickerJsonFormsRenderer } from './color-picker'
import { MyGroupRenderer } from './group-layout'

export const customRenderers = [
  ...materialRenderers,
  {
    tester: rankWith(
      3, //increase rank as needed
      formatIs('color')
    ),
    renderer: ColorPickerJsonFormsRenderer,
  },
  {
    tester: rankWith(3, uiTypeIs('Group')),
    renderer: MyGroupRenderer,
  },
]
