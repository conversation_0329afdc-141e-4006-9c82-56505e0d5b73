import {
  MaterialLayoutRenderer,
  MaterialLayoutRendererProps,
} from '@jsonforms/material-renderers'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Typography,
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import React from 'react'
import { withJsonFormsLayoutProps } from '@jsonforms/react'

const GroupRenderer = (props: any) => {
  const { uischema, schema, path, visible, renderers, enabled } = props

  const layoutProps: MaterialLayoutRendererProps = {
    elements: uischema.elements,
    schema: schema,
    path: path,
    direction: 'column',
    visible: visible,
    uischema: uischema,
    renderers: renderers,
    enabled,
  }
  return (
    <Box
      sx={{
        display: {
          xs: 'none',
          sm: visible ? 'block' : 'none',
        },
      }}
    >
      <Accordion defaultExpanded={true}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>{uischema.label}</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <MaterialLayoutRenderer {...layoutProps} />
        </AccordionDetails>
      </Accordion>
    </Box>
  )
}

export const MyGroupRenderer = withJsonFormsLayoutProps(GroupRenderer)
