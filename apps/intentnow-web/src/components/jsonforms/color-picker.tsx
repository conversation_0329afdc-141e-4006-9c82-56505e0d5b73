import { withJsonFormsControlProps } from '@jsonforms/react'
import { MuiColorInput } from 'mui-color-input'

const ColorPickerControl = (props: {
  handleChange: (path: string, value: any) => void
  data: string | undefined
  path: string
  label: string | undefined
}) => {
  const handleColorChange = (color: string) => {
    props.handleChange(props.path, color)
  }

  return (
    <MuiColorInput
      sx={{
        paddingTop: '6px',
        paddingBottom: '6px',
      }}
      fullWidth={true}
      format="hex"
      label={props.label}
      value={props.data ?? ''}
      onChange={handleColorChange}
    />
  )
}

export const ColorPickerJsonFormsRenderer =
  withJsonFormsControlProps(ColorPickerControl)
