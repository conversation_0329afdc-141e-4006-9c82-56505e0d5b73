import {
  Button,
  <PERSON><PERSON>,
  <PERSON>alogA<PERSON>,
  <PERSON>alogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material'
import { DialogProps } from '@toolpad/core/useDialogs'

export function ConfirmDialog({
  open,
  title,
  description,
  confirm,
  defaultAnswer,
}: {
  open: boolean
  title: string
  description: string
  confirm: (answer: boolean) => void
  defaultAnswer?: boolean
}) {
  function handleConfirm() {
    confirm(true)
  }

  function handleCancel() {
    confirm(false)
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={handleCancel}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
      >
        <DialogTitle id="confirm-dialog-title">{title}</DialogTitle>
        <DialogContent>
          <DialogContentText id="confirm-dialog-description">
            {description}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancel} autoFocus={Boolean(!defaultAnswer)}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} autoFocus={Boolean(defaultAnswer)}>
            Continue
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

//A confirm dialog built for Toolpad useDialogs()
export function ConfirmDialog2({
  payload,
  open,
  onClose,
}: DialogProps<
  {
    title: string
    description: string
    defaultAnswer?: boolean
  },
  { answer: boolean }
>) {
  function handleConfirm() {
    onClose({
      answer: true,
    })
  }

  function handleCancel() {
    onClose({
      answer: false,
    })
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={handleCancel}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
      >
        <DialogTitle id="confirm-dialog-title">{payload.title}</DialogTitle>
        <DialogContent>
          <DialogContentText id="confirm-dialog-description">
            {payload.description}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            variant="outlined"
            onClick={handleCancel}
            autoFocus={Boolean(!payload.defaultAnswer)}
          >
            Cancel
          </Button>
          <Button
            variant="outlined"
            onClick={handleConfirm}
            autoFocus={Boolean(payload.defaultAnswer)}
          >
            Continue
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}
