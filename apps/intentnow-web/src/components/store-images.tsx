import { useStoreImagesAdmin } from '@/modules/intentnow/settings'
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
} from '@mui/material'
import Image from 'next/image'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import DeleteIcon from '@mui/icons-material/Delete'
import { useState } from 'react'
import { useAuthedApiFetch } from '@/modules/auth/fetch'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { ShopifyStoreImage } from '@packages/shared-entities'

export function StoreImageUploadForm({
  shop,
  onUpload,
}: {
  shop?: string
  onUpload?: () => Promise<void>
}) {
  const { authedApiPostFetch } = useAuthedApiFetch()
  const [uploadFile, setUploadFile] = useState<any>()

  if (!shop) {
    return <></>
  }

  return (
    <Paper
      sx={{
        padding: '10px',
        width: '400px',
      }}
    >
      <form
        onSubmit={async (event) => {
          if (uploadFile) {
            event.preventDefault()
            event.currentTarget.reset()

            const formData = new FormData()
            formData.append('shop', shop)
            formData.append('imageFile', uploadFile)

            await authedApiPostFetch(
              [`/api/admin/intentnow/store-images/upload`, formData],
              undefined,
              false
            )

            await onUpload?.()
            setUploadFile(undefined)
          }
        }}
      >
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
        >
          <TextField
            type="file"
            variant="outlined"
            inputProps={{ accept: 'image/*' }}
            sx={{
              width: '100%',
              alignItems: 'center',
            }}
            onChange={(event) => {
              const file = (event.target as any)?.files?.[0]
              setUploadFile(file)
            }}
          />
          <Button
            variant="contained"
            color="primary"
            type="submit"
            disabled={!uploadFile}
            sx={{
              width: '150px',
            }}
            startIcon={<CloudUploadIcon />}
          >
            Upload
          </Button>
        </Box>
      </form>
    </Paper>
  )
}

export function StoreImageList({ shop }: { shop?: string }) {
  const { storeImages, isLoading, mutate, deleteStoreImages } =
    useStoreImagesAdmin(shop)
  const [imageToDelete, setImageToDelete] = useState<ShopifyStoreImage>()

  return (
    <Box>
      <TableContainer component={Paper} sx={{ width: '100%' }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }} width={250}>
                Preview
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }}>
                Name
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }}>
                Uploaded At
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }}>
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody sx={{ fontSize: 16 }}>
            {!storeImages && (
              <>
                <TableRow>
                  <TableCell>
                    {isLoading ? `Loading ...` : `No images found.`}
                  </TableCell>
                </TableRow>
              </>
            )}
            {storeImages?.map((image, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Box height="200px" width="200px" position="relative">
                    <Image
                      src={image.imageUrl}
                      alt=""
                      layout={'fill'}
                      objectFit={'contain'}
                    />
                  </Box>
                </TableCell>
                <TableCell>{image.name}</TableCell>
                <TableCell>
                  {new Date(image.createdAt).toLocaleString()}
                </TableCell>
                <TableCell>
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={() => {
                      setImageToDelete(image)
                    }}
                  >
                    <DeleteIcon />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <ConfirmDialog
        title="Delete Image"
        description={`You are about to delete the image "${imageToDelete?.name}". Deleted image can't be recovered. Do you want to continue?`}
        open={Boolean(imageToDelete)}
        defaultAnswer={false}
        confirm={async (answer) => {
          try {
            if (answer && shop && imageToDelete) {
              await deleteStoreImages(imageToDelete._id)
            }
          } finally {
            setImageToDelete(undefined)
            await mutate()
          }
        }}
      />
    </Box>
  )
}
