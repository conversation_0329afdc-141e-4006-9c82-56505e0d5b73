import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CircularProgress,
  Container,
  Grid2 as Grid,
  Input,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ield,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material'
import {
  defaultDiscountConfig,
  DiscountConfig,
} from '@packages/shared-entities'
import { useMemo } from 'react'
import PercentIcon from '@mui/icons-material/Percent'
import MoneyOffIcon from '@mui/icons-material/MoneyOff'
import { DiscountCustomerGetsValueInput } from '@packages/shared-entities/dist/shopify-api/admin.types'

export function DiscountEditor({
  isLoading,
  discountConfig,
  setDiscountConfig,
}: {
  isLoading: boolean
  discountConfig: DiscountConfig | undefined
  setDiscountConfig: (discountConfig: DiscountConfig) => void
}) {
  const {
    discountType,
    percent,
    amount,
    minimumOrder,
    codeLength,
    codeAlphabet,
    codePrefix,
    discountDurationInHrs,
    cacheValidDurationInHrs,
  } = useMemo(() => {
    const discountType: 'percent' | 'amount' = discountConfig?.discountInput
      .customerGets?.value?.percentage
      ? 'percent'
      : 'amount'

    const percent = Math.floor(
      (discountConfig?.discountInput.customerGets?.value?.percentage ?? 0.2) *
        100
    )
    const amount =
      discountConfig?.discountInput.customerGets?.value?.discountAmount
        ?.amount ?? 0
    const minimumOrder =
      discountConfig?.discountInput.minimumRequirement?.subtotal
        ?.greaterThanOrEqualToSubtotal ?? 0
    const codeLength = discountConfig?.codeGen.length ?? 0
    const codeAlphabet = discountConfig?.codeGen.alphabet ?? ''
    const codePrefix = discountConfig?.codeGen.prefix ?? ''
    const discountDurationInHrs = Math.floor(
      (discountConfig?.discountDurationInMinutes ?? 540) / 60
    )
    const cacheValidDurationInHrs = Math.floor(
      (discountConfig?.cacheValidDurationInMinutes ?? 360) / 60
    )

    return {
      discountType,
      percent,
      amount,
      minimumOrder,
      codeLength,
      codeAlphabet,
      codePrefix,
      discountDurationInHrs,
      cacheValidDurationInHrs,
    }
  }, [discountConfig])

  function generateDiscountConfig(
    type: 'percent' | 'amount',
    percent: number,
    amount: number,
    minimumOrder: number, //TODO
    codeLength: number,
    codeAlphabet: string,
    codePrefix: string,
    discountDurationInHrs: number,
    cacheValidDurationInHrs: number
  ) {
    let discountTitle: string | undefined
    let discountValue: DiscountCustomerGetsValueInput | undefined

    if (type === 'percent') {
      discountTitle = `${percent}% Off`
      discountValue = {
        percentage: percent / 100,
      }
    } else {
      discountTitle = `$${amount} Off`
      discountValue = {
        discountAmount: {
          amount: amount,
        },
      }
    }

    const dc = discountConfig ?? defaultDiscountConfig
    const newConfig: DiscountConfig = {
      discountDurationInMinutes: discountDurationInHrs * 60,
      cacheValidDurationInMinutes: cacheValidDurationInHrs * 60,
      codeGen: {
        length: codeLength,
        alphabet: codeAlphabet,
        prefix: codePrefix || undefined,
      },
      discountInput: {
        ...dc.discountInput,
        customerGets: {
          ...dc.discountInput.customerGets,
          value: discountValue,
        },
        title: discountTitle,
        ...(minimumOrder
          ? {
              minimumRequirement: {
                subtotal: {
                  greaterThanOrEqualToSubtotal: minimumOrder,
                },
              },
            }
          : {}),
      },
    }

    setDiscountConfig(newConfig)
  }

  if (isLoading) {
    return (
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  if (!discountConfig) {
    return (
      <Box
        id="discount-editor"
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Button
          variant="contained"
          onClick={() => {
            if (!discountConfig) {
              setDiscountConfig(defaultDiscountConfig)
            }
          }}
        >
          Create Discount Config
        </Button>
      </Box>
    )
  }

  return (
    <Box
      id="discount-editor"
      sx={{
        justifyContent: 'center',
        justifyItems: 'center',
      }}
    >
      <Container
        sx={{
          width: '600px',
          maxWidth: '600px',
          margin: '20px',
        }}
      >
        <Card
          sx={{
            paddingBottom: '10px',
          }}
        >
          <CardHeader title="Discount Code" />
          <CardContent>
            <Box
              sx={{
                marginBottom: '20px',
              }}
            >
              <Input
                value={codeLength}
                size="medium"
                onChange={(e) => {
                  const value = Math.floor(
                    e.target.value ? Number(e.target.value) : 0
                  )
                  generateDiscountConfig(
                    discountType,
                    percent,
                    amount,
                    minimumOrder,
                    value,
                    codeAlphabet,
                    codePrefix,
                    discountDurationInHrs,
                    cacheValidDurationInHrs
                  )
                }}
                inputProps={{
                  step: 1,
                  min: 4,
                  max: 20,
                  type: 'number',
                }}
                sx={{
                  marginLeft: '10px',
                }}
              />
              Characters
            </Box>
            <Box sx={{}}>
              <TextField
                value={codeAlphabet}
                label="Alphabet"
                type="text"
                onChange={(e) => {
                  generateDiscountConfig(
                    discountType,
                    percent,
                    amount,
                    minimumOrder,
                    codeLength,
                    e.target.value,
                    codePrefix,
                    discountDurationInHrs,
                    cacheValidDurationInHrs
                  )
                }}
                sx={{
                  width: '350px',
                }}
              />
            </Box>
            <Box sx={{}}>
              <TextField
                value={codePrefix}
                label="Prefix"
                type="text"
                onChange={(e) => {
                  const value = e.target.value.slice(0, codeLength)
                  generateDiscountConfig(
                    discountType,
                    percent,
                    amount,
                    minimumOrder,
                    codeLength,
                    codeAlphabet,
                    value,
                    discountDurationInHrs,
                    cacheValidDurationInHrs
                  )
                }}
                sx={{
                  width: '200px',
                }}
              />
            </Box>
          </CardContent>
        </Card>
        <Card
          sx={{
            marginTop: '10px',
          }}
        >
          <CardHeader title="Discount Type" />
          <CardContent>
            <ToggleButtonGroup
              value={discountType}
              exclusive
              onChange={(_, type) => {
                type &&
                  generateDiscountConfig(
                    type,
                    percent,
                    amount,
                    minimumOrder,
                    codeLength,
                    codeAlphabet,
                    codePrefix,
                    discountDurationInHrs,
                    cacheValidDurationInHrs
                  )
              }}
            >
              <ToggleButton value="percent">
                <PercentIcon />
                Percent Off
              </ToggleButton>
              <ToggleButton value="amount">
                <MoneyOffIcon />
                Amount Off
              </ToggleButton>
            </ToggleButtonGroup>
            {discountType === 'percent' && (
              <Card
                sx={{
                  marginTop: '20px',
                }}
              >
                <CardContent>
                  <Grid
                    container
                    spacing={2}
                    sx={{
                      justifyItems: 'center',
                      marginTop: '20px',
                    }}
                  >
                    <Grid size={4}>
                      <Slider
                        value={percent}
                        getAriaValueText={(value) => {
                          return `${value}% Off`
                        }}
                        valueLabelDisplay="auto"
                        shiftStep={5}
                        step={1}
                        min={1}
                        max={100}
                        onChange={(e, num) => {
                          const value = Math.floor(num ? Number(num) : 0)
                          generateDiscountConfig(
                            discountType,
                            value,
                            amount,
                            minimumOrder,
                            codeLength,
                            codeAlphabet,
                            codePrefix,
                            discountDurationInHrs,
                            cacheValidDurationInHrs
                          )
                        }}
                      />
                    </Grid>
                    <Grid size={2}>
                      <Input
                        value={percent}
                        size="medium"
                        onChange={(e) => {
                          const value = Math.floor(
                            e.target.value ? Number(e.target.value) : 0
                          )
                          generateDiscountConfig(
                            discountType,
                            value,
                            amount,
                            minimumOrder,
                            codeLength,
                            codeAlphabet,
                            codePrefix,
                            discountDurationInHrs,
                            cacheValidDurationInHrs
                          )
                        }}
                        inputProps={{
                          step: 1,
                          min: 1,
                          max: 100,
                          type: 'number',
                        }}
                      />
                      %
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}
            {discountType === 'amount' && (
              <Card
                sx={{
                  marginTop: '20px',
                }}
              >
                <CardContent>
                  <TextField
                    value={amount}
                    label="Amount Off"
                    type="number"
                    variant="outlined"
                    onChange={(e) => {
                      const value =
                        Math.floor(
                          (e.target.value ? Number(e.target.value) : 0) * 100
                        ) / 100
                      generateDiscountConfig(
                        discountType,
                        percent,
                        value,
                        minimumOrder,
                        codeLength,
                        codeAlphabet,
                        codePrefix,
                        discountDurationInHrs,
                        cacheValidDurationInHrs
                      )
                    }}
                    sx={{
                      margin: '5px',
                    }}
                  />
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>
        <Card
          sx={{
            marginTop: '10px',
          }}
        >
          <CardHeader title="Limits" />
          <CardContent>
            <TextField
              value={minimumOrder}
              label="Minimum Order"
              type="number"
              variant="outlined"
              onChange={(e) => {
                const value =
                  Math.floor(
                    (e.target.value ? Number(e.target.value) : 0) * 100
                  ) / 100
                generateDiscountConfig(
                  discountType,
                  percent,
                  amount,
                  value,
                  codeLength,
                  codeAlphabet,
                  codePrefix,
                  discountDurationInHrs,
                  cacheValidDurationInHrs
                )
              }}
              sx={{
                margin: '5px',
              }}
            />
          </CardContent>
        </Card>
        <Card
          sx={{
            marginTop: '10px',
          }}
        >
          <CardHeader title="Durations" />
          <CardContent>
            <TextField
              value={discountDurationInHrs}
              label="Valid-to-Redeem (hours)"
              type="number"
              variant="outlined"
              onChange={(e) => {
                const hrs = Math.floor(
                  e.target.value ? Number(e.target.value) : 0
                )
                generateDiscountConfig(
                  discountType,
                  percent,
                  amount,
                  minimumOrder,
                  codeLength,
                  codeAlphabet,
                  codePrefix,
                  hrs,
                  cacheValidDurationInHrs
                )
              }}
              sx={{
                margin: '5px',
              }}
            />
            <TextField
              value={cacheValidDurationInHrs}
              label="Continue-to-Show (hours)"
              type="number"
              variant="outlined"
              onChange={(e) => {
                const hrs = Math.floor(
                  e.target.value ? Number(e.target.value) : 0
                )
                generateDiscountConfig(
                  discountType,
                  percent,
                  amount,
                  minimumOrder,
                  codeLength,
                  codeAlphabet,
                  codePrefix,
                  discountDurationInHrs,
                  hrs
                )
              }}
              sx={{
                margin: '5px',
              }}
            />
          </CardContent>
        </Card>
      </Container>
    </Box>
  )
}
