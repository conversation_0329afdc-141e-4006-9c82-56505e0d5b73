import {
  DiscountConfig,
  DiscountContent,
  legacyWidgetStores,
  ShopifyStoreWidget,
  Widget2DialogConfig,
  Widget2TeaserConfig,
} from '@packages/shared-entities'
import {
  Box,
  Button,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import WidgetsIcon from '@mui/icons-material/Widgets'
import DiscountIcon from '@mui/icons-material/Discount'
import SaveIcon from '@mui/icons-material/Save'
import CloseIcon from '@mui/icons-material/Close'
import PreviewIcon from '@mui/icons-material/Preview'
import CloseFullscreenIcon from '@mui/icons-material/CloseFullscreen'
import StorefrontIcon from '@mui/icons-material/Storefront'
import PlayArrowIcon from '@mui/icons-material/PlayArrow'
import LinkIcon from '@mui/icons-material/Link'
import LinkOffIcon from '@mui/icons-material/LinkOff'
import { useStoreConfigByShop } from '@/modules/intentnow/settings'
import { DiscountEditor } from './discount-editor'
import { Widget2DialogTeaserEditor } from './widget2-dialog-teaser-editor'
import {
  generatePreviewDiscount,
  getPreviewWidgetUrl,
} from '@/modules/intentnow/widgets'

export function StoreWidget2Editor({
  shop,
  widgetId,
  outsideHeight,
}: {
  shop: string
  widgetId: string | undefined
  outsideHeight?: number
}) {
  const isLegacyStore = legacyWidgetStores.includes(shop)
  const {
    storeConfig,
    isLoading,
    isValidating,
    error,
    mutate,
    updateStoreConfig,
    updateStoreWidget,
  } = useStoreConfigByShop(shop)
  const [widgetData, setWidgetData] = useState<ShopifyStoreWidget>()
  const [discount, setDiscount] = useState<DiscountContent>()

  const [dialogConfig, setDialogConfig] = useState<
    Widget2DialogConfig | undefined
  >()
  const [teaserConfig, setTeaserConfig] = useState<
    Widget2TeaserConfig | undefined
  >()
  const [discountConfig, setDiscountConfig] = useState<DiscountConfig>()

  const [edit, setEdit] = useState<'dialog' | 'teaser' | 'discount'>('dialog')
  const [dialogChanged, setDialogChanged] = useState(false)
  const [teaserChanged, setTeaserChanged] = useState(false)
  const [discountChanged, setDiscountChanged] = useState(false)
  const [widgetDataChanged, setWidgetDataChanged] = useState(false)
  const [ready, setReady] = useState(false)

  const changed = useMemo(
    () =>
      dialogChanged || teaserChanged || discountChanged || widgetDataChanged,
    [dialogChanged, teaserChanged, discountChanged, widgetDataChanged]
  )

  const isSelected = useMemo(() => {
    return (
      storeConfig &&
      widgetData &&
      storeConfig?.promoConfig?.selectedWidgetId === widgetData?._id
    )
  }, [storeConfig, widgetData])

  useEffect(() => {
    if (!ready) {
      const widgetData = storeConfig?._widgets?.find((w) => w._id === widgetId)

      if (!isLoading && !isValidating && !error && widgetData) {
        setWidgetData(widgetData)
        setDialogConfig(widgetData.widget2?.dialog)
        setTeaserConfig(widgetData.widget2?.teaser)
        setDiscountConfig(widgetData.discount)

        if (widgetData.discount) {
          generatePreviewDiscount(widgetData.discount)
        } else {
          setEdit('discount')
        }

        setReady(true)
      }
    }
  }, [
    ready,
    widgetId,
    storeConfig,
    isLoading,
    isValidating,
    error,
    setWidgetData,
  ])

  useEffect(() => {
    if (discountConfig) {
      setDiscount(generatePreviewDiscount(discountConfig))
    } else {
      setDiscount(undefined)
    }
  }, [discountConfig])

  if (isLegacyStore) {
    return <></>
  }

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100vh',
        }}
      >
        <Box
          id="top-bar"
          sx={{
            boxShadow: 1,
          }}
        >
          <Box>
            <Box sx={{ display: 'inline-flex' }}>
              <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                <StorefrontIcon />
                <Typography
                  sx={{
                    fontWeight: 'bolder',
                  }}
                >
                  {storeConfig?.name
                    ? `${storeConfig?.name} [${storeConfig.shop}]`
                    : shop}
                </Typography>
              </Box>
              {widgetData && (
                <>
                  <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                    <WidgetsIcon />
                    <Typography
                      sx={{
                        fontWeight: 'bolder',
                      }}
                    >
                      {widgetData._id}
                    </Typography>
                  </Box>
                </>
              )}
              {widgetData && isSelected && (
                <>
                  <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                    <LinkIcon />
                    <Typography sx={{}}>In Use</Typography>
                  </Box>
                </>
              )}
              {widgetData && !isSelected && (
                <>
                  <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                    <LinkOffIcon />
                    <Typography sx={{}}>Unselected</Typography>
                  </Box>
                </>
              )}
            </Box>
          </Box>
          <Box sx={{ display: 'flex' }}>
            <Box sx={{ display: 'inline-flex', padding: '8px' }}>
              <ToggleButtonGroup
                disabled={!ready}
                value={edit}
                exclusive
                onChange={(_, newEdit) => {
                  newEdit && setEdit(newEdit)
                }}
                aria-label="text alignment"
              >
                <ToggleButton value="dialog" aria-label="left aligned">
                  <WidgetsIcon />
                  Dialog
                </ToggleButton>
                <ToggleButton value="teaser" aria-label="left aligned">
                  <CloseFullscreenIcon />
                  Teaser
                </ToggleButton>
                <ToggleButton value="discount" aria-label="centered">
                  <DiscountIcon />
                  Discount
                </ToggleButton>
              </ToggleButtonGroup>
              {widgetData && (
                <Box
                  sx={{
                    marginLeft: '20px',
                  }}
                >
                  <TextField
                    sx={{
                      width: '200px',
                    }}
                    value={widgetData?.name}
                    label="Widget Name"
                    onChange={(e) => {
                      setWidgetData({
                        ...widgetData,
                        name: e.target.value,
                      })
                      setWidgetDataChanged(true)
                    }}
                  />
                </Box>
              )}
            </Box>
            <Box
              sx={{
                flex: 1,
                display: 'inline-flex',
                flexDirection: 'row-reverse',
              }}
            >
              <Button
                variant="text"
                color="inherit"
                disabled={!changed || !ready}
                onClick={async () => {
                  if (changed && widgetData) {
                    await updateStoreWidget(widgetData._id, {
                      ...(widgetDataChanged
                        ? {
                            name: widgetData.name,
                          }
                        : {}),

                      ...(discountChanged
                        ? {
                            discount: discountConfig,
                          }
                        : {}),

                      ...((dialogChanged || teaserChanged) &&
                      dialogConfig &&
                      teaserConfig
                        ? {
                            widget2: {
                              dialog: dialogConfig,
                              teaser: teaserConfig,
                            },
                          }
                        : {}),
                    })
                    setDialogChanged(false)
                    setTeaserChanged(false)
                    setDiscountChanged(false)
                    setWidgetDataChanged(false)
                  }
                }}
              >
                <SaveIcon />
                Save
              </Button>
              <Button
                variant="text"
                color="inherit"
                disabled={!changed || !ready}
                onClick={async () => {
                  setDialogConfig(undefined)
                  setTeaserConfig(undefined)
                  setDiscountConfig(undefined)
                  setWidgetData(undefined)
                  setDiscount(undefined)

                  setDialogChanged(false)
                  setTeaserChanged(false)
                  setDiscountChanged(false)
                  setWidgetDataChanged(false)

                  setReady(false)

                  mutate()
                }}
              >
                <CloseIcon />
                Discard Changes
              </Button>

              <Button
                variant="text"
                color="inherit"
                disabled={!ready || changed}
                onClick={() => {
                  window.open(
                    getPreviewWidgetUrl(
                      storeConfig?.shop,
                      storeConfig?.website,
                      widgetData
                    ),
                    '_blank'
                  )
                }}
              >
                <PreviewIcon />
                Live Preview
              </Button>

              <Button
                variant="text"
                color="inherit"
                disabled={!ready || changed || isSelected}
                onClick={async () => {
                  if (widgetData) {
                    await updateStoreConfig({
                      promoConfig: {
                        selectedWidgetId: widgetData?._id,
                      },
                    })
                    mutate()
                  }
                }}
              >
                <PlayArrowIcon />
                Go Live
              </Button>
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
          }}
        >
          {['dialog', 'teaser'].includes(edit) && (
            <Widget2DialogTeaserEditor
              isLoading={isLoading || !ready}
              edit={edit}
              dialogConfig={dialogConfig}
              setDialogConfig={setDialogConfig}
              teaserConfig={teaserConfig}
              setTeaserConfig={setTeaserConfig}
              setDialogChanged={setDialogChanged}
              setTeaserChanged={setTeaserChanged}
              discount={discount}
              outsideHeight={110 + (outsideHeight ?? 0)}
            />
          )}

          {edit === 'discount' && (
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <DiscountEditor
                isLoading={isLoading || !ready}
                discountConfig={discountConfig}
                setDiscountConfig={(dc) => {
                  setDiscountChanged(true)
                  setDiscountConfig(dc)
                }}
              />
            </Box>
          )}
        </Box>
      </Box>
    </>
  )
}
