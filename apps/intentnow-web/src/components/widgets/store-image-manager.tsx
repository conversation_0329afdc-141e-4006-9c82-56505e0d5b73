'use client'

import { refineConfig } from '@/modules/config/refine'
import { SearchOutlined } from '@mui/icons-material'
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material'
import {
  Delete as DeleteIcon,
  Clear as ClearIcon,
  Check as CheckIcon,
} from '@mui/icons-material'
import { StoreImageDto } from '@packages/shared-entities'
import { useMemo, useState } from 'react'
import { debounce } from 'lodash'
import { List, useDataGrid } from '@refinedev/mui'
import {
  DataGrid,
  GridActionsCellItem,
  GridColDef,
  GridValidRowModel,
} from '@mui/x-data-grid'
import Image from 'next/image'
import { ConfirmDialog2 } from '../confirm-dialog'
import { useDialogs } from '@toolpad/core/useDialogs'
import { useDelete, useInvalidate } from '@refinedev/core'
import { FilePond, registerPlugin } from 'react-filepond'
import { useAuthedApiFetch } from '@/modules/auth/fetch'
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type'
import FilePondPluginImagePreview from 'filepond-plugin-image-preview'
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size'

import 'filepond/dist/filepond.min.css'
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css'
import { ProcessServerConfigFunction } from 'filepond'

registerPlugin(
  FilePondPluginFileValidateType,
  FilePondPluginImagePreview,
  FilePondPluginFileValidateSize
)

export function StoreImagesManager({
  open,
  onClose,
  selectOne,
  storeId,
}: {
  open: boolean
  onClose: (selectedImage?: StoreImageDto) => void
  selectOne?: boolean
  storeId: string
}) {
  const { authedApiPostFetch } = useAuthedApiFetch()
  const invalidate = useInvalidate()
  const dialogs = useDialogs()
  const resource = `stores/${storeId}/images`
  const { mutateAsync: deleteItem } = useDelete<StoreImageDto>()

  const [tab, setTab] = useState<string>('library')
  const [search, setSearch] = useState('')
  const [importUrl, setImportUrl] = useState('')

  const { dataGridProps, setFilters } = useDataGrid<StoreImageDto>({
    resource,
    sorters: { initial: [{ field: 'createdAt', order: 'desc' }] },
    filters: {
      initial: [],
    },
    syncWithLocation: false,

    pagination: {
      pageSize: 5,
    },
    queryOptions: {
      ...refineConfig.defaultUseListQueryOptions,
      enabled: open,
    },
  })

  const columns = useMemo<GridColDef<GridValidRowModel>[]>(
    () => [
      {
        field: 'imageUrl',
        headerName: 'Image',
        sortable: false,
        filterable: false,
        minWidth: 140,
        renderCell: (params) => {
          return (
            <Box
              width="120px"
              height="78px"
              position="relative"
              sx={{
                border: 0.5,
                borderRadius: 0.5,
              }}
            >
              <Image
                src={params.row.imageUrl}
                fill
                alt=""
                objectFit={'contain'}
              />
            </Box>
          )
        },
      },
      {
        field: 'name',
        headerName: 'Name',
        minWidth: 200,
        flex: 1,
        sortable: true,
        filterable: false,
        renderCell: (params) => {
          return (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 1,
                height: '100%',
                justifyContent: 'center',
              }}
            >
              <Typography
                sx={{
                  color: 'text.primary',
                  fontWeight: 'bold',
                }}
              >
                {params.row.name}
              </Typography>
              <Typography
                sx={{
                  fontSize: '0.75rem',
                  color: 'text.secondary',
                }}
              >
                {params.row.fileInfo.format} • {params.row.fileInfo.width}x
                {params.row.fileInfo.height}
              </Typography>
            </Box>
          )
        },
      },
      {
        field: 'fileInfo.size',
        headerName: 'File Size',
        sortable: true,
        filterable: false,
        minWidth: 150,
        renderCell: (params) => {
          return `${(params?.row.fileInfo.size / 1000).toFixed(2)} KB`
        },
      },
      {
        field: 'createdAt',
        headerName: 'Uploaded',
        sortable: true,
        filterable: false,
        minWidth: 200,
        renderCell: (params) => {
          if (params?.row) {
            return new Date(params?.row.createdAt).toLocaleString()
          }
        },
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Action',
        width: 150,
        sortable: false,
        filterable: false,
        getActions: ({ row }) => [
          ...(selectOne
            ? [
                <GridActionsCellItem
                  key={'action-select'}
                  icon={<CheckIcon />}
                  sx={{ padding: '2px 6px' }}
                  label="Select"
                  onClick={() => {
                    onClose(row as any)
                  }}
                />,
              ]
            : []),
          <GridActionsCellItem
            key={'action-delete'}
            icon={<DeleteIcon />}
            sx={{ padding: '2px 6px' }}
            label="Delete"
            showInMenu={selectOne}
            onClick={async () => {
              const { answer } = await dialogs.open(ConfirmDialog2, {
                title: 'Delete Image',
                description: `You are about to delete the image "${row.name}". Deleted image can't be recovered. Do you want to continue?`,
              })
              if (answer) {
                //Delete the image
                await deleteItem({
                  resource,
                  id: row._id,
                })
              }
            }}
          />,
        ],
      },
    ],
    [deleteItem, dialogs, onClose, resource, selectOne]
  )

  const debouncedFn = useMemo(
    () =>
      debounce((newSearch: string) => {
        if (newSearch) {
          setFilters([
            {
              field: 'name',
              operator: 'contains',
              value: newSearch,
            },
          ])
        } else {
          setFilters([])
        }
      }, 1000),
    [setFilters]
  )

  const handleUpload: ProcessServerConfigFunction = (
    fieldName,
    file,
    metadata,
    load,
    error,
    progress,
    abort
  ) => {
    const formData = new FormData()
    formData.append('imageFile', file)

    const controller = new AbortController()

    authedApiPostFetch(
      [`/api/intentnow/stores/${storeId}/images`, formData],
      {
        method: 'POST',
        signal: controller.signal,
      },
      false //not JSON
    )
      .then(() => {
        load('Success')
        invalidate({
          resource,
          invalidates: ['list'],
        })
      })
      .catch((err) => {
        if (err.name !== 'AbortError') {
          error('Upload failed: ' + err.message)
        }
      })

    return {
      abort: () => {
        controller.abort()
        abort()
      },
    }
  }

  const isLoading = dataGridProps.loading

  return (
    <Dialog
      open={open}
      onClose={() => onClose()}
      maxWidth={false}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '95vw',
          maxHeight: '95vh',
          width: '95vw',
          height: '95vh',
          margin: 0,
          padding: 0,
          backgroundColor: '#fff',
        },
      }}
    >
      <DialogTitle>Store Images</DialogTitle>
      <DialogContent>
        {isLoading && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
          >
            <CircularProgress />
          </Box>
        )}
        {!isLoading && (
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
              }}
            >
              <Tabs
                variant="scrollable"
                scrollButtons="auto"
                value={tab}
                onChange={(_, value) => {
                  setTab(value)
                }}
              >
                <Tab label="Image Library" value="library" />
                <Tab label="Upload Image" value="upload" />
                {/* <Tab label="Import Url" value="import" /> */}
              </Tabs>
            </Box>
            <Container
              maxWidth="lg"
              sx={{
                justifyContent: 'center',
                alignItems: 'center',
                overflowY: 'auto',
              }}
            >
              {tab === 'library' && (
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    margin: '10px',
                  }}
                >
                  <Box>
                    <TextField
                      placeholder="Search..."
                      autoFocus={true}
                      value={search}
                      onChange={(e) => {
                        const newSearch = e.target.value
                        setSearch(newSearch)
                        debouncedFn(newSearch)
                      }}
                      InputProps={{
                        startAdornment: <SearchOutlined />,
                        endAdornment: (
                          <IconButton
                            onClick={() => {
                              setSearch('')
                              debouncedFn('')
                            }}
                            size="small"
                            sx={{ visibility: search ? 'visible' : 'hidden' }}
                          >
                            <ClearIcon />
                          </IconButton>
                        ),
                      }}
                      size="medium"
                      sx={{
                        width: '300px',
                      }}
                    />
                  </Box>
                  <Box
                    sx={{
                      flex: 1,
                    }}
                  >
                    <List>
                      <DataGrid
                        {...dataGridProps}
                        columns={columns}
                        sx={{
                          '.MuiDataGrid-row:hover': {
                            cursor: 'pointer',
                          },
                        }}
                        rowHeight={80}
                        onRowClick={(params) => {
                          if (selectOne) {
                            onClose(params.row as any)
                          }
                        }}
                      />
                    </List>
                  </Box>
                </Box>
              )}
              {tab === 'upload' && (
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '20px',
                    margin: '20px',
                  }}
                >
                  <Box
                    sx={{
                      flex: 1,
                      display: 'flex',
                      justifyContent: 'center',

                      '& .filepond--root': {
                        width: '600px !important',
                        height: '300px !important',
                      },
                      '& .filepond--panel-root': {
                        border: `1px dashed`,
                        justifyContent: 'center',
                      },
                      '& .filepond--drop-label': {
                        height: '100% !important',
                      },
                      '& .filepond--image-preview': {
                        backgroundColor: ' #f0f0f0 !important',
                      },
                    }}
                  >
                    <FilePond
                      name="imageFile"
                      allowMultiple={false}
                      allowRevert={false}
                      credits={false}
                      instantUpload={false}
                      maxFileSize="5MB"
                      allowImagePreview={true}
                      allowFileSizeValidation={true}
                      allowFileTypeValidation={true}
                      acceptedFileTypes={[
                        'image/png',
                        'image/jpeg',
                        'images/gif',
                        'image/webp',
                      ]}
                      labelIdle='<p><b>Drag and drop or select image</b></p><p>Accepts .jpg, .jpeg, .png, .gif, and .webp file types. </p><p>Maximum file size 5MB.</p><br/><p><span class="filepond--label-action">Select Image</span></p>'
                      server={{
                        process: handleUpload,
                      }}
                    />
                  </Box>
                </Box>
              )}
              {tab === 'import' && (
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    justifyContent: 'center',
                    margin: '20px',
                  }}
                >
                  <Box
                    sx={{
                      maxWidth: '600px',
                      width: '600px',
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    <Typography variant="h6">Image Url</Typography>
                    <TextField
                      value={importUrl}
                      fullWidth
                      size="medium"
                      onChange={(e) => {
                        setImportUrl(e.target.value)
                      }}
                      InputProps={{
                        startAdornment: <SearchOutlined />,
                        endAdornment: Boolean(importUrl) ? (
                          <Button size="small">Import</Button>
                        ) : undefined,
                      }}
                    />
                    {/* <Box
                      sx={{
                        flex: 1,
                        display: 'flex',
                        justifyContent: 'center',

                        '& .filepond--root': {
                          width: '600px !important',
                          height: '300px !important',
                        },
                        '& .filepond--panel-root': {
                          border: `1px dashed`,
                          justifyContent: 'center',
                        },
                        '& .filepond--drop-label': {
                          height: '100% !important',
                        },
                        '& .filepond--image-preview': {
                          backgroundColor: ' #f0f0f0 !important',
                        },
                        '& .filepond--label-action': {
                          textDecoration: 'none !important',
                        },
                      }}
                    >
                      <FilePond
                        name="imageFile"
                        allowMultiple={false}
                        allowRevert={false}
                        credits={false}
                        instantUpload={false}
                        maxFileSize="5MB"
                        allowImagePreview={true}
                        allowFileSizeValidation={true}
                        allowFileTypeValidation={true}
                        acceptedFileTypes={[
                          'image/png',
                          'image/jpeg',
                          'images/gif',
                          'image/webp',
                        ]}
                        server={{
                          process: handleUpload,
                        }}
                      />
                    </Box> */}
                  </Box>
                </Box>
              )}
            </Container>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={() => onClose()}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  )
}
