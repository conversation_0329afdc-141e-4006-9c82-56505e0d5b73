'use client'

import { Puck, usePuck } from '@measured/puck'
import '@measured/puck/puck.css'
import createEmotionCache from '@measured/puck-plugin-emotion-cache'
import {
  PreviewMode,
  PromoWidgetProvider,
  usePromoWidgetContext,
} from '@packages/intentnow-tag'
import {
  defaultWidget3Config,
  DiscountContent,
  GoogleFontConfig,
  ShopifyStoreImage,
  Widget3DialogConfig,
  Widget3TeaserConfig,
} from '@packages/shared-entities'
import { useEffect, useMemo, useState } from 'react'
import {
  applyDynamicCustomFieldsToPuckConfig,
  applyDynamicCustomFieldsToPuckConfigNewStore,
  getWidget3DialogPuckConfigWithCustomFields,
  getWidget3TeaserPuckConfigWithCustomFields,
} from '../puck/custom-fields'
import {
  Box,
  Button,
  CircularProgress,
  Paper,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material'
import ComputerIcon from '@mui/icons-material/Computer'
import PhoneIphoneIcon from '@mui/icons-material/PhoneIphone'
import _ from 'lodash'
import Link from 'next/link'
import { getSelectedIteamPath } from '../puck/puck-edit-helper'
import { summarizeFontsFromWidget3Config } from '@packages/intentnow-tag'

const emotionCache = createEmotionCache('widget3-dialog-teaser-editor')
const dialogPuckConfig = getWidget3DialogPuckConfigWithCustomFields()
const teaserPuckConfig = getWidget3TeaserPuckConfigWithCustomFields()

export function Widget3DialogTeaserEditor({
  isLoading,
  edit,
  discount,
  initialDialogData,
  initialTeaserData,
  dialogData,
  setDialogData,
  setDialogChanged,
  teaserData,
  setTeaserData,
  setTeaserChanged,
  storeImages,
  outsideHeight,
  fullEditor,
  newStoreId,
}: {
  isLoading: boolean
  edit: 'dialog' | 'teaser' | 'discount'
  initialDialogData: Widget3DialogConfig | undefined
  initialTeaserData: Widget3TeaserConfig | undefined
  dialogData: Widget3DialogConfig | undefined
  setDialogData: (discountConfig: Widget3DialogConfig) => void
  teaserData: Widget3TeaserConfig | undefined
  setTeaserData: (teaserConfig: Widget3TeaserConfig) => void
  setDialogChanged: (changed: boolean) => void
  setTeaserChanged: (changed: boolean) => void
  discount: DiscountContent | undefined
  storeImages: ShopifyStoreImage[] | undefined
  outsideHeight?: number
  fullEditor?: boolean
  newStoreId?: string
}) {
  const { customDialogPuckConfig, customTeaserPuckConfig } = useMemo(() => {
    return newStoreId
      ? applyDynamicCustomFieldsToPuckConfigNewStore(
          dialogPuckConfig,
          teaserPuckConfig,
          newStoreId
        )
      : applyDynamicCustomFieldsToPuckConfig(
          dialogPuckConfig,
          teaserPuckConfig,
          storeImages
        )
  }, [newStoreId, storeImages])

  const [goolgeFonts, setGoogleFonts] = useState<string[]>()
  useEffect(() => {
    const { googleFonts } = summarizeFontsFromWidget3Config({
      dialog: dialogData ?? {},
      teaser: teaserData ?? {},
    })
    setGoogleFonts(
      googleFonts.map((f) => (f.fontConfig as GoogleFontConfig).cssUrl)
    )
  }, [dialogData, teaserData])

  if (isLoading) {
    return (
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  if (!discount) {
    return (
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography>Missing Discount Config</Typography>
      </Box>
    )
  }

  if (!dialogData || !teaserData) {
    return (
      <Box
        id="discount-editor"
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Button
          variant="contained"
          onClick={() => {
            if (!dialogData) {
              setDialogData(defaultWidget3Config.dialog)
            }
            if (!teaserData) {
              setTeaserData(defaultWidget3Config.teaser)
            }
          }}
        >
          Create Dialog and Teaser
        </Button>
      </Box>
    )
  }

  return (
    <>
      {Boolean(goolgeFonts?.length) && (
        <>
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link
            rel="preconnect"
            href="https://fonts.gstatic.com"
            crossOrigin="anonymous"
          />
          {goolgeFonts?.map((fontHref, index) => (
            <link href={fontHref} rel="stylesheet" key={index} />
          ))}
        </>
      )}
      <PromoWidgetProvider>
        <Box
          height={`calc(100vh - ${outsideHeight ?? 0}px)`}
          width="100%"
          sx={{
            paddingTop: '5px',
            flex: 1,
            display: 'block',
          }}
        >
          {edit === 'dialog' && (
            <>
              <Puck
                iframe={{
                  enabled: false,
                }}
                config={customDialogPuckConfig}
                data={dialogData}
                onChange={(data) => {
                  if (!data) {
                    return
                  }

                  if (data !== dialogData && !_.isEqual(data, dialogData)) {
                    //console.debug('old dialogdata', JSON.stringify(dialogData))
                    //console.debug('new dialog data', JSON.stringify(data))
                    setDialogData(data)
                  }
                  if (_.isEqual(data, initialDialogData)) {
                    setDialogChanged(false)
                  } else {
                    setDialogChanged(true)
                  }
                }}
                plugins={[emotionCache]}
              >
                <Widget3PuckEditorInner
                  puckRootName="Dialog"
                  discount={discount}
                  outsideHeight={outsideHeight}
                  fullEditor={fullEditor}
                />
              </Puck>
            </>
          )}
          {edit === 'teaser' && (
            <>
              <Puck
                iframe={{
                  enabled: false,
                }}
                config={customTeaserPuckConfig}
                data={teaserData}
                onChange={(data) => {
                  if (!data) {
                    return
                  }

                  if (data !== teaserData && !_.isEqual(data, teaserData)) {
                    //console.debug('old teaser data', JSON.stringify(teaserData))
                    //console.debug('new teaser data', JSON.stringify(data))
                    setTeaserData(data)
                  }
                  if (_.isEqual(data, initialTeaserData)) {
                    setTeaserChanged(false)
                  } else {
                    setTeaserChanged(true)
                  }
                }}
                plugins={[emotionCache]}
              >
                <Widget3PuckEditorInner
                  puckRootName="Teaser"
                  discount={discount}
                  outsideHeight={outsideHeight}
                  fullEditor={fullEditor}
                />
              </Puck>
            </>
          )}
        </Box>
      </PromoWidgetProvider>
    </>
  )
}

//This component needs to be wrapped within a <Puck> element.
function Widget3PuckEditorInner({
  puckRootName,
  discount,
  outsideHeight,
  fullEditor,
}: {
  puckRootName: string
  discount: DiscountContent | undefined
  outsideHeight?: number
  fullEditor?: boolean
}) {
  const [view, setView] = useState<'desktop' | 'mobile'>('desktop')
  const { setContent, setSettings } = usePromoWidgetContext()
  const puck = usePuck()

  useEffect(() => {
    if (discount) {
      setContent({
        discount,
      })
    } else {
      setContent(undefined)
    }
  }, [discount, setContent])

  useEffect(() => {
    setSettings({
      previewMode:
        view === 'mobile'
          ? PreviewMode.previewDialogMobile
          : PreviewMode.previewDialog,
    })
  }, [view, setSettings])

  const { selectedPath } = useMemo(() => {
    const selectedPath = getSelectedIteamPath(
      puck.config,
      puck.appState.data,
      puck.appState.ui.itemSelector,
      puckRootName
    )

    return {
      selectedPath,
    }
  }, [puck, puckRootName])

  return (
    <>
      <Box
        height={`calc(100vh - ${outsideHeight ?? 0}px)`}
        sx={{
          display: 'flex',
        }}
      >
        <Box sx={{ width: '300px', minWidth: '300px' }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              overflow: 'hidden',
              overflowY: 'auto',
            }}
          >
            <Paper
              sx={{
                marginTop: '5px',
                padding: '2px',
              }}
            >
              <Typography
                sx={{
                  fontSize: '16px',
                  fontWeight: 'bold',
                  padding: '5px',
                }}
              >
                {selectedPath?.map((p, idx) => {
                  if (idx < selectedPath.length - 1) {
                    return (
                      <span key={idx}>
                        <Link
                          href="#"
                          onClick={() => {
                            puck.dispatch({
                              type: 'setUi',
                              ui: { itemSelector: p.selector },
                            })
                          }}
                        >{`${p.name}`}</Link>
                        {` > `}
                      </span>
                    )
                  } else {
                    return <span key={idx}>{p.name}</span>
                  }
                })}
              </Typography>
              <Puck.Fields />
            </Paper>
          </Box>
        </Box>
        <Box
          height={`calc(100vh - ${outsideHeight ?? 0}px)`}
          width={`calc(100vw - 480px)`}
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'row-reverse',
          }}
        >
          {fullEditor && (
            <Box
              sx={{
                width: '180px',
                overflow: 'hidden',
                overflowY: 'auto',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  height: '100%',
                  overflow: 'hidden',
                  overflowY: 'auto',
                }}
              >
                <Paper
                  sx={{
                    marginTop: '5px',
                    padding: '2px',
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: '16px',
                      fontWeight: 'bold',
                      padding: '5px',
                    }}
                  >
                    Components
                  </Typography>
                  <Puck.Components />
                </Paper>
                <Paper
                  sx={{
                    marginTop: '5px',
                    padding: '2px',
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: '16px',
                      fontWeight: 'bold',
                      padding: '5px',
                    }}
                  >
                    Outline
                  </Typography>
                  <Puck.Outline />
                </Paper>
              </Box>
            </Box>
          )}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              width: '100%',
              padding: '2px',

              overflow: 'hidden',
              overflowY: 'auto',
            }}
          >
            <Box
              sx={{
                flex: 1,
                border: '2px dotted black',
                backgroundColor: 'lightgray',
                position: 'relative',
                overflow: 'hidden',
                minWidth: view === 'mobile' ? '390px' : '800px',
                maxWidth: view === 'mobile' ? '390px' : '2000px',
                minHeight: view === 'mobile' ? '844px' : '600px',
                width: '100%',
                height:
                  view === 'mobile'
                    ? '844px'
                    : `calc(100vh - ${(outsideHeight ?? 0) + 10}px)`,

                // ...(view === 'mobile'
                //   ? {
                //       transform: 'scale(0.75)',
                //       transformOrigin: 'top left',
                //     }
                //   : {}),
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  right: '0px',
                  zIndex: 1,
                }}
              >
                <ToggleButtonGroup
                  value={view}
                  exclusive
                  onChange={(_, newView) => {
                    setView(newView)
                  }}
                  aria-label="text alignment"
                >
                  <ToggleButton value="desktop" aria-label="deskop view">
                    <ComputerIcon />
                  </ToggleButton>
                  <ToggleButton value="mobile" aria-label="mobile view">
                    <PhoneIphoneIcon />
                  </ToggleButton>
                </ToggleButtonGroup>
              </Box>
              <Puck.Preview />
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  )
}
