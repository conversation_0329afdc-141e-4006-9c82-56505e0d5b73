import {
  DiscountConfig,
  DiscountContent,
  legacyWidgetStores,
  Widget2DialogConfig,
  Widget2TeaserConfig,
} from '@packages/shared-entities'
import {
  Box,
  Button,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material'
import { useEffect, useMemo, useState } from 'react'
import WidgetsIcon from '@mui/icons-material/Widgets'
import DiscountIcon from '@mui/icons-material/Discount'
import SaveIcon from '@mui/icons-material/Save'
import CloseIcon from '@mui/icons-material/Close'
import PreviewIcon from '@mui/icons-material/Preview'
import CloseFullscreenIcon from '@mui/icons-material/CloseFullscreen'
import StorefrontIcon from '@mui/icons-material/Storefront'
import PublicIcon from '@mui/icons-material/Public'
import { useStoreConfigByShop } from '@/modules/intentnow/settings'
import { DiscountEditor } from './discount-editor'
import { Widget2DialogTeaserEditor } from './widget2-dialog-teaser-editor'
import {
  generatePreviewDiscount,
  getPreviewWidgetUrl,
} from '@/modules/intentnow/widgets'

export function Widget2Editor({
  shop,
  outsideHeight,
}: {
  shop: string
  outsideHeight?: number
}) {
  const isLegacyStore = legacyWidgetStores.includes(shop)
  const {
    storeConfig,
    isLoading,
    isValidating,
    error,
    mutate,
    updateStoreConfig,
  } = useStoreConfigByShop(shop, true)
  const [discount, setDiscount] = useState<DiscountContent>()

  const [dialogConfig, setDialogConfig] = useState<
    Widget2DialogConfig | undefined
  >()
  const [teaserConfig, setTeaserConfig] = useState<
    Widget2TeaserConfig | undefined
  >()
  const [discountConfig, setDiscountConfig] = useState<DiscountConfig>()

  const [edit, setEdit] = useState<'dialog' | 'teaser' | 'discount'>(
    isLegacyStore ? 'discount' : 'dialog'
  )
  const [dialogChanged, setDialogChanged] = useState(false)
  const [teaserChanged, setTeaserChanged] = useState(false)
  const [discountChanged, setDiscountChanged] = useState(false)
  const [ready, setReady] = useState(false)

  const changed = useMemo(
    () => dialogChanged || teaserChanged || discountChanged,
    [dialogChanged, teaserChanged, discountChanged]
  )

  useEffect(() => {
    if (!isLoading && !isValidating && !error) {
      setDialogConfig(storeConfig?.promoConfig?.widget2?.dialog)
      setTeaserConfig(storeConfig?.promoConfig?.widget2?.teaser)

      setDiscountConfig(storeConfig?.promoConfig?.discount)
      if (storeConfig?.promoConfig?.discount) {
        setDiscount(generatePreviewDiscount(storeConfig.promoConfig.discount))
      } else {
        setEdit('discount')
      }

      setReady(true)
    } else if (isLoading || isValidating) {
      setReady(false)
    }
  }, [storeConfig, isLoading, isValidating, error])

  useEffect(() => {
    if (discountConfig) {
      setDiscount(generatePreviewDiscount(discountConfig))
    } else {
      setDiscount(undefined)
    }
  }, [discountConfig])

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100vh',
        }}
      >
        <Box
          id="top-bar"
          sx={{
            boxShadow: 1,
          }}
        >
          <Box>
            <Box sx={{ display: 'inline-flex' }}>
              <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                <StorefrontIcon />
                <Typography
                  sx={{
                    fontWeight: 'bolder',
                  }}
                >
                  {storeConfig?.name
                    ? `${storeConfig?.name} [${storeConfig.shop}]`
                    : shop}
                </Typography>
              </Box>
              {storeConfig?.website && (
                <Box sx={{ display: 'inline-flex', paddingLeft: '10px' }}>
                  <PublicIcon />
                  <Typography
                    sx={{
                      fontWeight: 'bolder',
                    }}
                  >
                    {storeConfig?.website}
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
          <Box sx={{ display: 'flex' }}>
            <Box sx={{ display: 'inline-flex', padding: '8px' }}>
              <ToggleButtonGroup
                disabled={!ready}
                value={edit}
                exclusive
                onChange={(_, newEdit) => {
                  newEdit && setEdit(newEdit)
                }}
                aria-label="text alignment"
              >
                {!isLegacyStore && (
                  <>
                    <ToggleButton value="dialog" aria-label="left aligned">
                      <WidgetsIcon />
                      Dialog
                    </ToggleButton>
                    <ToggleButton value="teaser" aria-label="left aligned">
                      <CloseFullscreenIcon />
                      Teaser
                    </ToggleButton>
                  </>
                )}
                <ToggleButton value="discount" aria-label="centered">
                  <DiscountIcon />
                  Discount
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>
            <Box
              sx={{
                flex: 1,
                display: 'inline-flex',
                flexDirection: 'row-reverse',
              }}
            >
              <Button
                variant="text"
                color="inherit"
                disabled={!changed || !ready}
                onClick={async () => {
                  if (changed) {
                    await updateStoreConfig({
                      promoConfig: {
                        ...(discountChanged
                          ? {
                              discount: discountConfig,
                            }
                          : {}),
                        ...((dialogChanged || teaserChanged) &&
                        dialogConfig &&
                        teaserConfig
                          ? {
                              widget2: {
                                dialog: dialogConfig,
                                teaser: teaserConfig,
                              },
                            }
                          : {}),
                      },
                    })
                    setDialogChanged(false)
                    setTeaserChanged(false)
                    setDiscountChanged(false)
                  }
                }}
              >
                <SaveIcon />
                Save
              </Button>
              <Button
                variant="text"
                color="inherit"
                disabled={!changed || !ready}
                onClick={async () => {
                  setDialogConfig(undefined)
                  setTeaserConfig(undefined)
                  setDiscountConfig(undefined)
                  setDialogChanged(false)
                  setTeaserChanged(false)
                  setDiscountChanged(false)
                  mutate()
                }}
              >
                <CloseIcon />
                Discard Changes
              </Button>
              <Button
                variant="text"
                color="inherit"
                disabled={!ready || changed}
                onClick={() => {
                  window.open(
                    getPreviewWidgetUrl(
                      storeConfig?.shop,
                      storeConfig?.website,
                      undefined
                    ),
                    '_blank'
                  )
                }}
              >
                <PreviewIcon />
                Live Preview
              </Button>
            </Box>
          </Box>
        </Box>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
          }}
        >
          {!isLegacyStore && ['dialog', 'teaser'].includes(edit) && (
            <Widget2DialogTeaserEditor
              isLoading={isLoading || !ready}
              edit={edit}
              dialogConfig={dialogConfig}
              setDialogConfig={setDialogConfig}
              teaserConfig={teaserConfig}
              setTeaserConfig={setTeaserConfig}
              setDialogChanged={setDialogChanged}
              setTeaserChanged={setTeaserChanged}
              discount={discount}
              outsideHeight={92 + (outsideHeight ?? 0)}
            />
          )}

          {edit === 'discount' && (
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
              }}
            >
              <DiscountEditor
                isLoading={isLoading || !ready}
                discountConfig={discountConfig}
                setDiscountConfig={(dc) => {
                  setDiscountChanged(true)
                  setDiscountConfig(dc)
                }}
              />
            </Box>
          )}
        </Box>
      </Box>
    </>
  )
}
