import { JsonSchema } from '@jsonforms/core'
import { JsonForms } from '@jsonforms/react'
import {
  Box,
  Button,
  CircularProgress,
  Container,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material'
import {
  defaultWidget2Config,
  DiscountContent,
  Widget2DialogConfig,
  widgetDialogJsonSchema,
  widget2DialogUiSchema,
  Widget2TeaserConfig,
  widgetTeaserJsonSchema,
  widget2TeaserUiSchema,
} from '@packages/shared-entities'
import ComputerIcon from '@mui/icons-material/Computer'
import PhoneIphoneIcon from '@mui/icons-material/PhoneIphone'
import { customRenderers } from '../jsonforms/renderers'
import { materialCells } from '@jsonforms/material-renderers'
import { useState } from 'react'
import { PreviewMode, PromoWidget } from '@packages/intentnow-tag'

export function Widget2DialogTeaserEditor({
  isLoading,
  edit,
  dialogConfig,
  setDialogConfig,
  teaserConfig,
  setTeaserConfig,
  setDialogChanged,
  setTeaserChanged,
  discount,
  outsideHeight,
}: {
  isLoading: boolean
  edit: 'dialog' | 'teaser' | 'discount'
  dialogConfig: Widget2DialogConfig | undefined
  setDialogConfig: (discountConfig: Widget2DialogConfig) => void
  teaserConfig: Widget2TeaserConfig | undefined
  setTeaserConfig: (teaserConfig: Widget2TeaserConfig) => void
  setDialogChanged: (changed: boolean) => void
  setTeaserChanged: (changed: boolean) => void
  discount: DiscountContent | undefined
  outsideHeight?: number
}) {
  const [view, setView] = useState<'desktop' | 'mobile'>('desktop')

  if (!['dialog', 'teaser'].includes(edit)) {
    return <></>
  }

  if (isLoading) {
    return (
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Box>
    )
  }

  if (!discount) {
    return (
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography>Missing Discount Config</Typography>
      </Box>
    )
  }

  if (!dialogConfig || !teaserConfig) {
    return (
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Button
          variant="contained"
          onClick={() => {
            if (!dialogConfig || !teaserConfig) {
              setDialogConfig(defaultWidget2Config.dialog)
              setTeaserConfig(defaultWidget2Config.teaser)
              setDialogChanged(true)
              setTeaserChanged(true)
            }
          }}
        >
          Create Widget with Default Settings
        </Button>
      </Box>
    )
  }

  return (
    <>
      {edit === 'dialog' && (
        <Box
          id="dialog-editor"
          height={`calc(100vh - ${outsideHeight ?? 0}px)`}
          sx={{
            marginTop: '5px',
            flex: 1,
            display: 'flex',
          }}
        >
          <Box
            id="dialog-editor_left-panel"
            width="500px"
            sx={{
              overflow: 'hidden',
              overflowY: 'auto',
            }}
          >
            <JsonForms
              schema={widgetDialogJsonSchema as unknown as JsonSchema}
              uischema={widget2DialogUiSchema}
              data={dialogConfig}
              renderers={customRenderers}
              cells={materialCells}
              onChange={({ data, errors }) => {
                if (!errors?.length && data !== dialogConfig) {
                  setDialogConfig(data)
                  setDialogChanged(true)
                }
              }}
            />
          </Box>
          <Box
            sx={{
              justifyItems: 'center',
              width: '100%',
            }}
          >
            <Box
              id="dialog-editor_preview-panel"
              sx={{
                flex: 1,
                border: '2px dotted black',
                backgroundColor: 'lightgray',
                width: '100%',
                minWidth: view === 'mobile' ? '390px' : '700px',
                maxWidth: view === 'mobile' ? '390px' : '2000px',
                height: view === 'mobile' ? '844px' : '100%',
                ...(view === 'mobile' ? { maxHeight: '844px' } : {}),
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              <PromoWidget
                settings={{
                  previewMode:
                    view === 'mobile'
                      ? PreviewMode.previewDialogMobile
                      : PreviewMode.previewDialog,
                }}
                content={{
                  discount: discount!,
                  widget2: {
                    dialog: dialogConfig,
                    teaser: teaserConfig,
                  },
                }}
              />
              <Box
                sx={{
                  position: 'absolute',
                  right: '0px',
                }}
              >
                <ToggleButtonGroup
                  value={view}
                  exclusive
                  onChange={(_, newView) => {
                    setView(newView)
                  }}
                  aria-label="text alignment"
                >
                  <ToggleButton value="desktop" aria-label="left aligned">
                    <ComputerIcon />
                  </ToggleButton>
                  <ToggleButton value="mobile" aria-label="centered">
                    <PhoneIphoneIcon />
                  </ToggleButton>
                </ToggleButtonGroup>
              </Box>
            </Box>
          </Box>
        </Box>
      )}
      {edit === 'teaser' && (
        <Box
          id="teaser-editor"
          height={`calc(100vh - ${outsideHeight ?? 0}px)`}
          sx={{
            marginTop: '5px',
            flex: 1,
            display: 'flex',
          }}
        >
          <Container
            id="teaser-editor_left-panel"
            sx={{
              overflow: 'hidden',
              overflowY: 'auto',
              width: '500px',
            }}
          >
            <JsonForms
              schema={widgetTeaserJsonSchema as unknown as JsonSchema}
              uischema={widget2TeaserUiSchema}
              data={teaserConfig}
              renderers={customRenderers}
              cells={materialCells}
              onChange={({ data, errors }) => {
                if (!errors?.length && data !== teaserConfig) {
                  setTeaserConfig(data)
                  setTeaserChanged(true)
                }
              }}
            />
          </Container>
          <Box
            sx={{
              justifyItems: 'center',
              width: '100%',
            }}
          >
            <Box
              id="teaser-editor_preview-panel"
              sx={{
                flex: 1,
                border: '2px dotted black',
                backgroundColor: 'lightgray',
                maring: '2px',
                width: '100%',
                height: '100%',
                minWidth: view === 'mobile' ? '390px' : '700px',
                maxWidth: view === 'mobile' ? '390px' : '2000px',
                position: 'relative',
                overflow: 'hidden',
                overflowY: 'auto',
              }}
            >
              <PromoWidget
                settings={{
                  previewMode:
                    view === 'mobile'
                      ? PreviewMode.previewTeaserMobile
                      : PreviewMode.previewTeaser,
                }}
                content={{
                  discount: discount!,
                  widget2: {
                    dialog: dialogConfig,
                    teaser: teaserConfig,
                  },
                }}
              />
              <Box
                sx={{
                  position: 'absolute',
                  right: '0px',
                }}
              >
                <ToggleButtonGroup
                  value={view}
                  exclusive
                  onChange={(_, newView) => {
                    setView(newView)
                  }}
                  aria-label="text alignment"
                >
                  <ToggleButton value="desktop" aria-label="left aligned">
                    <ComputerIcon />
                  </ToggleButton>
                  <ToggleButton value="mobile" aria-label="centered">
                    <PhoneIphoneIcon />
                  </ToggleButton>
                </ToggleButtonGroup>
              </Box>
            </Box>
          </Box>
        </Box>
      )}
    </>
  )
}
