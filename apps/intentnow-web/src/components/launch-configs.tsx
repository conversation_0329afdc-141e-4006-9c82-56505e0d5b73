import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import {
  useActiveStoreConfig,
  useLaunchConfigs,
  useStoreConfigByShop,
} from '@/modules/intentnow/settings'
import { JSONTree } from 'react-json-tree'
import { useMemo, useState } from 'react'
import { JsonFormsDialog } from './jsonforms/jsonforms-dialog'
import { getLaunchConfigJsonSchema } from '@/modules/intentnow/forms'
import { StoreLaunchConfig } from '@packages/shared-entities'

export function LaunchConfigs({ shop }: { shop?: string }) {
  const { storeConfig } = useStoreConfigByShop(shop)
  const {
    launchConfigs,
    isLoading,
    mutate: mutateLaunchConfigs,
    createLaunchConfig,
  } = useLaunchConfigs(shop)
  const { activeStoreConfig, mutate: mutateActiveStoreConfig } =
    useActiveStoreConfig(shop)
  const [newLaunchConfig, setNewLaunchConfig] = useState<any>()

  const { launchConfigJsonSchema, launchConfigUiSchema } = useMemo(() => {
    return getLaunchConfigJsonSchema(storeConfig?._widgets ?? [])
  }, [storeConfig?._widgets])

  const now = new Date()
  const newConfigName = `${now.getFullYear()}-${now.getMonth() >= 9 ? now.getMonth() + 1 : `0${now.getMonth() + 1}`}-${now.getDate() >= 10 ? now.getDate() : `0${now.getDate()}`}`

  return (
    <Box>
      <Box padding="10px">
        <Button
          variant="contained"
          onClick={() => {
            if (!newLaunchConfig) {
              setNewLaunchConfig(
                activeStoreConfig?.currentLaunchConfig
                  ? {
                      name: newConfigName,
                      configVariants:
                        activeStoreConfig.currentLaunchConfig.configVariants,
                    }
                  : {
                      name: newConfigName,
                      configVariants: storeConfig?.promoConfig?.model
                        ? [
                            {
                              modelConfig: {
                                parameters:
                                  storeConfig.promoConfig.model.parameters,
                              },
                            },
                          ]
                        : [],
                    }
              )
            }
          }}
        >
          Create Launch Config
        </Button>
      </Box>
      <Box>
        <TableContainer component={Paper} sx={{ width: '100%' }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }} width={20}>
                  Status
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }}>
                  Name
                </TableCell>
                <TableCell
                  sx={{ fontWeight: 'bold', fontSize: 16 }}
                  width={160}
                >
                  Created At
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }}>
                  Config Variants
                </TableCell>
                <TableCell sx={{ width: '10px' }} />
              </TableRow>
            </TableHead>
            <TableBody sx={{ fontSize: 16 }}>
              {!launchConfigs && (
                <>
                  <TableRow>
                    <TableCell>
                      {isLoading ? `Loading ...` : `No launch config found.`}
                    </TableCell>
                  </TableRow>
                </>
              )}
              {launchConfigs?.map((config, index) => {
                const isActive =
                  activeStoreConfig?.currentLaunchConfig?._id === config._id

                return (
                  <TableRow key={index}>
                    <TableCell>{isActive ? 'Active' : ''}</TableCell>
                    <TableCell>{config.name}</TableCell>
                    <TableCell>
                      {new Date(config.createdAt).toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <JSONTree
                        data={{
                          ...config.configVariants,
                        }}
                        shouldExpandNodeInitially={() => isActive}
                      />
                    </TableCell>
                    <TableCell sx={{ padding: '0px' }}>
                      <Button
                        startIcon={<ContentCopyIcon />}
                        variant="text"
                        size="small"
                        sx={{
                          minWidth: 'unset',
                          width: '10px',
                        }}
                        onClick={() => {
                          if (!newLaunchConfig) {
                            setNewLaunchConfig({
                              name: newConfigName,
                              configVariants: config.configVariants,
                            })
                          }
                        }}
                      />
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
      <JsonFormsDialog
        open={Boolean(newLaunchConfig)}
        title="Create Launch Config"
        description=""
        dataSchema={launchConfigJsonSchema}
        uiSchema={launchConfigUiSchema}
        formData={newLaunchConfig}
        onValidate={(data: Partial<StoreLaunchConfig>) => {
          //additional validation
          if (data.configVariants) {
            let selectWidgetId = false
            let selectNoneWidgetId = false
            for (let i = 0; i < data.configVariants.length; i++) {
              if (data.configVariants[i].widgetId) {
                selectWidgetId = true
              } else {
                selectNoneWidgetId = true
              }

              const variant = data.configVariants[i]
              const floor = variant.modelConfig.parameters.floor
              const ceiling = variant.modelConfig.parameters.ceiling
              const start = variant.modelConfig.parameters.start
              const end = variant.modelConfig.parameters.end

              if (floor < 0 || floor > 1) {
                return [
                  {
                    instancePath: '/configVariants',
                    keyword: '',
                    schemaPath: '',
                    message: `Error in variant[${i}]: Floor must be between 0 and 1`,
                    params: {},
                  },
                ]
              }
              if (ceiling < 0 || ceiling > 1) {
                return [
                  {
                    instancePath: '/configVariants',
                    keyword: '',
                    schemaPath: '',
                    message: `Error in variant[${i}]: Ceiling must be between 0 and 1`,
                    params: {},
                  },
                ]
              }
              if (start < 0) {
                return [
                  {
                    instancePath: '/configVariants',
                    keyword: '',
                    schemaPath: '',
                    message: `Error in variant[${i}]: Start must be greater than 0`,
                    params: {},
                  },
                ]
              }
              if (end < 0) {
                return [
                  {
                    instancePath: '/configVariants',
                    keyword: '',
                    schemaPath: '',
                    message: `Error in variant[${i}]: End must be greater than 0`,
                    params: {},
                  },
                ]
              }
              if (floor >= ceiling) {
                return [
                  {
                    instancePath: '/configVariants',
                    keyword: '',
                    schemaPath: '',
                    message: `Error in variant[${i}]: Floor must be less than Ceiling`,
                    params: {},
                  },
                ]
              }
              if (start >= end) {
                return [
                  {
                    instancePath: '/configVariants',
                    keyword: '',
                    schemaPath: '',
                    message: `Error in variant[${i}]: Start must be less than End`,
                    params: {},
                  },
                ]
              }
            }

            if (selectWidgetId && selectNoneWidgetId) {
              return [
                {
                  instancePath: '/configVariants',
                  keyword: '',
                  schemaPath: '',
                  message: `Error: all variants must select a widget or none of them select a widget`,
                  params: {},
                },
              ]
            }

            return []
          }
        }}
        onSave={async (data: Partial<StoreLaunchConfig>) => {
          setNewLaunchConfig(undefined)
          if (data && shop) {
            await createLaunchConfig(data as any)
            await mutateLaunchConfigs()
            await mutateActiveStoreConfig()
          }
        }}
        onCancel={() => {
          setNewLaunchConfig(undefined)
        }}
      />
    </Box>
  )
}
