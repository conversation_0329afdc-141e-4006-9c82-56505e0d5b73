'use client'
// import { useTranslations } from 'next-intl'
import {
  Box,
  Button,
  CircularProgress,
  Container,
  <PERSON>ack,
  TextField,
} from '@mui/material'
import { JSONTree } from 'react-json-tree'
import { useState } from 'react'
import { useLocalStorage } from 'usehooks-ts'

export function ShopifyGraphQl({
  shop,
  queryFunc,
}: {
  shop: string
  queryFunc: (shop: string, query: string, variables: any) => Promise<any>
}) {
  const [loading, setLoading] = useState(false)

  const localStoragePrefix = `intentnow_shopify-graphql_${shop}`

  const [query, setQuery] = useLocalStorage<string>(
    `${localStoragePrefix}/query`,
    ''
  )
  const [variables, setVariables] = useLocalStorage<string>(
    `${localStoragePrefix}/variables`,
    ''
  )
  const [results, setResults] = useState<any>()
  const [status, setStatus] = useState('')

  async function runQuery() {
    if (shop && query) {
      try {
        setLoading(true)
        setStatus('running')
        const variablesObj = variables ? JSON.parse(variables) : undefined
        const response = await queryFunc(shop, query, variablesObj)
        setResults(response)
        setStatus('done')
      } catch (error: any) {
        setResults({
          error,
        })
        setStatus('error')
      } finally {
        setLoading(false)
      }
    } else {
      if (!shop) {
        setStatus('missing myshopify domain')
      } else if (!query) {
        setStatus('missing query')
      }
    }
  }

  return (
    <main>
      <Container>
        <Stack spacing={2}>
          <br />
          <div>
            <Box display="flex" alignItems="center">
              <Button
                //disabled={!shopifyState || !query || status === 'running'}
                variant="contained"
                onClick={runQuery}
              >
                Run Query
              </Button>
              {loading && <CircularProgress />}
              {!loading && `   ${status}`}
            </Box>
          </div>
          <div>
            Query:
            <TextField
              multiline
              rows={10}
              fullWidth={true}
              variant="outlined"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
            />
          </div>
          <div>
            Variables:
            <TextField
              multiline
              rows={5}
              fullWidth={true}
              variant="outlined"
              value={variables}
              onChange={(e) => setVariables(e.target.value)}
            />
          </div>
          <div>
            Results:
            <JSONTree
              data={results ?? {}}
              shouldExpandNodeInitially={() => true}
            />
          </div>
        </Stack>
      </Container>
    </main>
  )
}
