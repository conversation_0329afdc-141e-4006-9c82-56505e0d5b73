import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import { useMemo } from 'react'

export function StoreAnalytics({
  analyticsConfig,
}: {
  analyticsConfig?: {
    chartIds?: string[]
  }
}) {
  const chartRows = useMemo(() => {
    if (analyticsConfig?.chartIds) {
      const chartIds = analyticsConfig.chartIds
      const chartRows: { chartIds: string[] }[] = []
      for (let i = 0; i < chartIds.length; i += 2) {
        chartRows.push({ chartIds: chartIds.slice(i, i + 2) })
      }

      return chartRows
    } else {
      return []
    }
  }, [analyticsConfig])

  return (
    <Box>
      <TableContainer component={Paper} sx={{ width: '100%' }}>
        <Table>
          <TableBody sx={{ fontSize: 16 }}>
            {!chartRows.length && (
              <>
                <TableRow>
                  <TableCell>{`Analytics haven't been defined.`}</TableCell>
                </TableRow>
              </>
            )}
            {!!chartRows.length &&
              chartRows.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {row.chartIds.map((chartId: string, index) => (
                    <TableCell key={index}>
                      <iframe
                        src={`https://app.amplitude.com/analytics/share/embed/${chartId}`}
                        frameBorder="0"
                        width="100%"
                        height="540"
                      ></iframe>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  )
}
