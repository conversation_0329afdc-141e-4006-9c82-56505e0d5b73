import { Config, Data, usePuck } from '@measured/puck'

export function getSelectedIteamPath(
  config: Config,
  data: Data,
  selector: ReturnType<typeof usePuck>['appState']['ui']['itemSelector'] | null,
  rootName: string
) {
  const path: {
    name: string
    selector: {
      zone: string
      index: number
    } | null
  }[] = []

  let zone = selector?.zone ?? 'default-zone'
  let itemIndex = selector?.index ?? -1

  while (true) {
    const { itemName, parentZone, selector } = lookupPuckNode(
      config,
      data,
      zone,
      itemIndex,
      rootName
    )

    path.push({
      name: itemName,
      selector,
    })

    if (parentZone) {
      zone = parentZone
      itemIndex = -1
    } else {
      break
    }
  }

  return path.reverse()
}

function lookupPuckNode(
  config: Config,
  data: Data,
  zone: string | undefined | null,
  itemIndex: number, // < 0 if we are looking up for a zone
  rootName: string
): {
  itemName: string
  parentZone?: string
  selector: {
    zone: string
    index: number
  } | null
} {
  if (zone) {
    if (zone === 'default-zone') {
      if (itemIndex >= 0) {
        const item = data.content[itemIndex]
        if (item) {
          return {
            itemName:
              config.components[item.type]?.label || item.type || 'Unknown',
            selector: {
              zone: 'default-zone',
              index: itemIndex,
            },
            parentZone: 'default-zone',
          }
        }
      } else {
        return {
          itemName: rootName,
          selector: null,
        }
      }
    } else {
      let parentZone: string | undefined
      let itemName: string | undefined
      let selector: {
        zone: string
        index: number
      } | null = null

      const zoneData = data.zones?.[zone]
      if (zoneData) {
        const item = zoneData[itemIndex]
        if (item) {
          parentZone = zone
          itemName = config.components[item.type]?.label || item.type
          selector = {
            zone,
            index: itemIndex,
          }
        }
      }

      if (!itemName) {
        const zoneComponentId = zone.split(':')[0]
        const zoneCompRootIndex = data.content.findIndex(
          (c) => c.props['id'] === zoneComponentId
        )

        if (zoneCompRootIndex >= 0) {
          //This zone belongs to a root component
          const zoneComponent = data.content[zoneCompRootIndex]
          if (zoneComponent) {
            itemName =
              config.components[zoneComponent.type]?.label || zoneComponent.type
            parentZone = 'default-zone'
            selector = {
              zone: 'default-zone',
              index: zoneCompRootIndex,
            }
          }
        } else {
          //This zone component is within another zone
          for (const zid of Object.keys(data.zones ?? {})) {
            const zn = data.zones?.[zid]
            if (zn) {
              const zoneComponentIndex = zn.findIndex(
                (c) => c.props['id'] === zoneComponentId
              )

              if (zoneComponentIndex >= 0) {
                const zoneComponent = zn[zoneComponentIndex]
                itemName =
                  config.components[zoneComponent.type]?.label ||
                  zoneComponent.type
                parentZone = zid
                selector = {
                  zone: zid,
                  index: zoneComponentIndex,
                }
              }
            }
          }
        }
      }

      if (itemName) {
        return {
          itemName,
          parentZone,
          selector,
        }
      }
    }
  }

  //Fallback if nothing works
  return {
    itemName: rootName,
    selector: null,
  }
}
