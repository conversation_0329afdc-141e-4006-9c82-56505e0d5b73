import { <PERSON><PERSON><PERSON>, <PERSON>, FieldLabel, ObjectField } from '@measured/puck'
import { Box, Button, Grid2 as Grid, Typography } from '@mui/material'
import {
  widget3DialogPuckConfig,
  widget3TeaserPuckConfig,
} from '@packages/intentnow-tag'
import { ShopifyStoreImage } from '@packages/shared-entities'
import _ from 'lodash'
import { MuiColorInput } from 'mui-color-input'
import Image from 'next/image'
import { StoreImagesManager } from '../widgets/store-image-manager'
import { useState } from 'react'

//Apply custom field editing components here so we don't have to introduce extra UI dependencies into the intentnow-tag package
export function getWidget3DialogPuckConfigWithCustomFields() {
  const newConfig = _.cloneDeep(widget3DialogPuckConfig)

  applyColorsCustomFields(
    newConfig.root!.fields!.minimizeButton! as ObjectField,
    ['color']
  )
  applyColorsCustomFields(
    (newConfig.root!.fields!.styles! as ObjectField).objectFields
      .colors as ObjectField,
    ['color', 'backgroundColor']
  )
  applyColorsCustomFields(
    (newConfig.components.Text.fields!.styles! as ObjectField).objectFields
      .colors as ObjectField,
    ['color', 'backgroundColor']
  )
  applyColorsCustomFields(
    (newConfig.components.Button.fields!.styles! as ObjectField).objectFields
      .colors as ObjectField,
    ['color', 'backgroundColor', 'hoverBackgroundColor']
  )

  return newConfig
}

export function applyDynamicCustomFieldsToPuckConfig(
  dialogConfig: typeof widget3DialogPuckConfig,
  teaserConfig: typeof widget3TeaserPuckConfig,
  storeImages?: ShopifyStoreImage[]
) {
  const customDialogPuckConfig = _.cloneDeep(dialogConfig)
  const customTeaserPuckConfig = _.cloneDeep(teaserConfig)

  if (!storeImages) {
    storeImages = []
  }

  const imageOptions = [
    {
      label: 'None',
      value: '',
    },
    ...storeImages.map((image) => ({
      label: image.name,
      value: image.imageUrl,
    })),
  ]

  customDialogPuckConfig.components.Image.fields!.imageUrl = {
    label: 'Image',
    type: 'select',
    options: imageOptions,
  }
  ;(
    customDialogPuckConfig.components.DialogContainer.fields!
      .image as ObjectField
  ).objectFields.imageUrl = {
    label: 'Image',
    type: 'select',
    options: imageOptions,
  }

  customTeaserPuckConfig.components.Image.fields!.imageUrl = {
    label: 'Image',
    type: 'select',
    options: imageOptions,
  }

  return {
    customDialogPuckConfig,
    customTeaserPuckConfig,
  }
}

export function applyDynamicCustomFieldsToPuckConfigNewStore(
  dialogConfig: typeof widget3DialogPuckConfig,
  teaserConfig: typeof widget3TeaserPuckConfig,
  storeId: string
) {
  const customDialogPuckConfig = _.cloneDeep(dialogConfig)
  const customTeaserPuckConfig = _.cloneDeep(teaserConfig)

  ;(customDialogPuckConfig.components.Image.fields! as any).imageUrl =
    getStoreImageCustomField(storeId)
  ;(
    customDialogPuckConfig.components.DialogContainer.fields!
      .image as ObjectField
  ).objectFields.imageUrl = getStoreImageCustomField(storeId)
  ;(customTeaserPuckConfig.components.Image.fields! as any).imageUrl =
    getStoreImageCustomField(storeId)

  return {
    customDialogPuckConfig,
    customTeaserPuckConfig,
  }
}

export function getWidget3TeaserPuckConfigWithCustomFields() {
  const newConfig = {
    ...widget3TeaserPuckConfig,
  }

  applyColorsCustomFields(
    (newConfig.components.Text.fields!.styles! as ObjectField).objectFields
      .colors as ObjectField,
    ['color', 'backgroundColor']
  )

  return newConfig
}

function applyColorsCustomFields(
  colorsField: ObjectField<any> | undefined,
  fields: string[]
) {
  if (!colorsField) {
    return
  }

  for (const field of fields) {
    if (colorsField.objectFields[field]) {
      colorsField.objectFields[field] = {
        ...ColorCustomField,
        label: colorsField.objectFields[field].label,
      }
    }
  }
}

export const ColorCustomField: Field<string | undefined> = {
  type: 'custom',
  label: 'Color',
  render: (props: {
    field: CustomField<string | undefined>
    name: string
    id: string
    value: string | undefined
    onChange: (value: string | undefined) => void
    readOnly?: boolean
  }) => {
    return (
      <div>
        <FieldLabel label={props.field?.label ?? props.name}>
          <MuiColorInput
            sx={{
              paddingTop: '6px',
              paddingBottom: '6px',
            }}
            fullWidth={true}
            format="hex"
            value={props.value ?? ''}
            disabled={props.readOnly}
            onChange={props.onChange}
          />
        </FieldLabel>
      </div>
    )
  },
}

function getStoreImageCustomField(storeId: string) {
  const storeImageCustomField: Field<string | undefined> = {
    type: 'custom',
    label: 'Image',
    render: (props: {
      field: CustomField<string | undefined>
      name: string
      id: string
      value: string | undefined
      onChange: (value: string | undefined) => void
      readOnly?: boolean
    }) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const [openImagesManager, setOpenImagesManager] = useState(false)

      return (
        <div>
          <FieldLabel label={props.field?.label ?? props.name}>
            <Box
              sx={{
                width: '100%',
                height: '100px',
                border: 0.5,
                borderRadius: 1,
              }}
            >
              <Grid container spacing={0}>
                <Grid size={6}>
                  <Box
                    sx={{
                      width: '100%',
                      height: '100px',
                      position: 'relative',
                      borderRight: 0.5,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    {!props.value && <Typography>None</Typography>}
                    {Boolean(props.value) && (
                      <Image
                        src={props.value!}
                        alt=""
                        fill
                        objectFit={'contain'}
                      />
                    )}
                  </Box>
                </Grid>
                <Grid
                  size={6}
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setOpenImagesManager(true)
                    }}
                  >
                    Select
                  </Button>
                </Grid>
              </Grid>
            </Box>
            <StoreImagesManager
              open={openImagesManager}
              onClose={(selectedImage) => {
                setOpenImagesManager(false)
                if (selectedImage) {
                  props.onChange(selectedImage.imageUrl)
                }
              }}
              storeId={storeId}
              selectOne
            />
          </FieldLabel>
        </div>
      )
    },
  }
  return storeImageCustomField
}
