import { Box } from '@mui/material'

export function EmbeddedDialog({ children }: { children: React.ReactNode }) {
  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: '600px',
        minHeight: '400px',
        alignItems: 'left',
        justifyContent: 'top',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '10px',
        boxShadow: 2,
        padding: '20px',
        paddingTop: '40px',
      }}
    >
      {children}
    </Box>
  )
}
