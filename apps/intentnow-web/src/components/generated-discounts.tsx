import { appConfig } from '@/modules/config/app'
import {
  useGeneratedDiscounts,
  useGeneratedDiscountsForShop,
} from '@/modules/intentnow/discounts'
import { useShopifyApp } from '@/modules/shopify/app'
import { DecoratedPageInfo } from '@/modules/utils/paged-data-helper'
import {
  Box,
  Button,
  Link,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import { ShopifyStoreGeneratedDiscount } from '@packages/shared-entities'

export function GeneratedDiscountsAdmin({
  shop,
  appHandle,
}: {
  shop?: string
  appHandle?: string
}) {
  const { data, isLoading, pageInfo } = useGeneratedDiscountsForShop(
    appHandle,
    shop
  )

  return (
    <GeneratedDiscounts
      admin={true}
      shop={shop!}
      data={data}
      isLoading={isLoading}
      pageInfo={pageInfo}
    ></GeneratedDiscounts>
  )
}

export function GeneratedDiscountsEmbedded() {
  const shopifyApp = useShopifyApp()
  const { data, isLoading, pageInfo } = useGeneratedDiscounts()

  if (!shopifyApp) {
    return <></>
  }

  return (
    <GeneratedDiscounts
      admin={false}
      shop={shopifyApp.shop}
      data={data}
      isLoading={isLoading}
      pageInfo={pageInfo}
    ></GeneratedDiscounts>
  )
}

export function GeneratedDiscounts({
  admin,
  shop,
  data,
  isLoading,
  pageInfo,
}: {
  admin?: boolean
  shop: string
  data?: ShopifyStoreGeneratedDiscount[]
  isLoading: boolean
  pageInfo?: DecoratedPageInfo
}) {
  const firestoreDbPath =
    appConfig.appEnv === 'production' ? '-default-' : 'staging'

  return (
    <Box>
      <Button
        disabled={!pageInfo?.prevPage}
        onClick={() => {
          pageInfo?.firstPage?.()
        }}
      >
        First
      </Button>
      <Button
        disabled={!pageInfo?.prevPage}
        onClick={() => {
          pageInfo?.prevPage?.()
        }}
      >
        Prev
      </Button>
      <Button
        disabled={!pageInfo?.nextPage}
        onClick={() => {
          pageInfo?.nextPage?.()
        }}
      >
        Next
      </Button>
      <Button
        disabled={!pageInfo?.nextPage}
        onClick={() => {
          pageInfo?.lastPage?.()
        }}
      >
        Last
      </Button>
      {pageInfo?.totalCount !== undefined && (
        <>{`(${pageInfo.totalCount} Totals)`}</>
      )}
      <br />
      <TableContainer component={Paper} sx={{ width: '100%' }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }}>
                Code
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }}>
                Starts At
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }}>
                Ends At
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }} width={80}>
                Status
              </TableCell>
              <TableCell sx={{ fontWeight: 'bold', fontSize: 16 }}>
                Details
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody sx={{ fontSize: 16 }}>
            {!data && (
              <>
                <TableRow>
                  <TableCell>
                    {isLoading
                      ? `Loading ...`
                      : `Failed to read generated discounts.`}
                  </TableCell>
                </TableRow>
              </>
            )}
            {!!data &&
              data.map((discount, index) => (
                <TableRow key={index}>
                  <TableCell>{discount.discount.code}</TableCell>
                  <TableCell>
                    {new Date(discount.discount.startsAt).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    {new Date(discount.discount.endsAt).toLocaleString()}
                  </TableCell>
                  {discount.discountStatus?.status === 'REDEEMED' ? (
                    <TableCell>
                      <Box padding={1} style={{ backgroundColor: 'green' }}>
                        {discount.discountStatus?.status}
                      </Box>
                    </TableCell>
                  ) : (
                    <TableCell>
                      <Box padding={1}>{discount.discountStatus?.status}</Box>
                    </TableCell>
                  )}
                  <TableCell>
                    {admin && discount.discountId2 && (
                      <div>{`discountId: ${discount.discountId2}`}</div>
                    )}
                    {admin && (
                      <>
                        <div>{`clientId: ${discount.clientId}`}</div>
                        <div>
                          <Link
                            target="_blank"
                            href={`https://app.amplitude.com/analytics/intentnow/users?search=${discount.clientId}&searchType=search`}
                          >
                            User on Amplitude
                          </Link>
                        </div>
                        <div>
                          <Link
                            target="_blank"
                            href={`https://console.cloud.google.com/firestore/databases/${firestoreDbPath}/data/panel/ShopifyStoreClients/${discount.clientId}`}
                          >
                            Client Record (Firestore)
                          </Link>
                        </div>
                      </>
                    )}
                    {!admin && discount.discountId2 && (
                      <Button
                        onClick={() => {
                          const parts = discount.discountId2?.split('/')
                          if (parts?.length) {
                            const discountId = parts[parts.length - 1]
                            window.open(
                              `https://${shop}/admin/discounts/${discountId}`,
                              '_blank'
                            )
                          }
                        }}
                      >
                        details
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Button
        disabled={!pageInfo?.prevPage}
        onClick={() => {
          pageInfo?.firstPage?.()
          window.scrollTo(0, 0)
        }}
      >
        First
      </Button>
      <Button
        disabled={!pageInfo?.prevPage}
        onClick={() => {
          pageInfo?.prevPage?.()
          window.scrollTo(0, 0)
        }}
      >
        Prev
      </Button>
      <Button
        disabled={!pageInfo?.nextPage}
        onClick={() => {
          pageInfo?.nextPage?.()
          window.scrollTo(0, 0)
        }}
      >
        Next
      </Button>
      <Button
        disabled={!pageInfo?.nextPage}
        onClick={() => {
          pageInfo?.lastPage?.()
          window.scrollTo(0, 0)
        }}
      >
        Last
      </Button>
      {pageInfo?.totalCount !== undefined && (
        <>{`(${pageInfo.totalCount} Totals)`}</>
      )}
      <br />
    </Box>
  )
}
