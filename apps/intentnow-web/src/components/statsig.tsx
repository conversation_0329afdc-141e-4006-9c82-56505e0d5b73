'use client'
import { useShopifyApp } from '@/modules/shopify/app'
import { LogLevel, StatsigProvider } from '@statsig/react-bindings'
import { useEffect, useState } from 'react'

const sdkKey = process.env.NEXT_PUBLIC_STATSIG_CLIENT_KEY!
const tier = process.env.NEXT_PUBLIC_APP_ENV ?? 'development'

export function ShopifyStatsigProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const shopifyApp = useShopifyApp()
  const [shop, setShop] = useState<string>()

  useEffect(() => {
    if (shopifyApp?.shop) {
      setShop(shopifyApp.shop)
    }
  }, [shopifyApp])

  if (!shop) {
    return <></>
  }

  return (
    <StatsigProvider
      sdkKey={sdkKey}
      user={{ userID: shop }}
      options={{
        logLevel: LogLevel.Info,
        environment: {
          tier,
        },
      }}
    >
      {children}
    </StatsigProvider>
  )
}
