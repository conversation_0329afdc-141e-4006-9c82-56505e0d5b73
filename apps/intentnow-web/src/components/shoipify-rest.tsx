'use client'
// import { useTranslations } from 'next-intl'
import {
  Box,
  Button,
  CircularProgress,
  Container,
  MenuItem,
  Select,
  Stack,
  TextField,
} from '@mui/material'
import { JSONTree } from 'react-json-tree'
import { useState } from 'react'
import { useLocalStorage } from 'usehooks-ts'

export function ShopifyGraphRest({
  shop,
  queryFunc,
}: {
  shop: string
  queryFunc: (
    shop: string,
    op: string,
    path: string,
    query?: any,
    data?: any
  ) => Promise<any>
}) {
  const [loading, setLoading] = useState(false)
  const localStoragePrefix = `intentnow_shopify-rest_${shop}`
  const opItems = ['get', 'post', 'put', 'delete']

  const [op, setOp] = useLocalStorage<string>(`${localStoragePrefix}/op`, 'get')
  const [query, setQuery] = useLocalStorage<string>(
    `${localStoragePrefix}/query`,
    ''
  )
  const [path, setPath] = useLocalStorage<string>(
    `${localStoragePrefix}/path`,
    ''
  )
  const [data, setData] = useLocalStorage<string>(
    `${localStoragePrefix}/variables`,
    ''
  )
  const [results, setResults] = useState<any>()
  const [status, setStatus] = useState('')

  async function runQuery() {
    if (shop && path) {
      try {
        setLoading(true)
        setStatus('running')
        const queryObj = query ? JSON.parse(query) : undefined
        const dataObj = data ? JSON.parse(data) : undefined
        const response = await queryFunc(shop, op, path, queryObj, dataObj)
        setResults(response)
        setStatus('done')
      } catch (error: any) {
        setResults({
          error,
        })
        setStatus('error')
      } finally {
        setLoading(false)
      }
    } else {
      if (!shop) {
        setStatus('missing myshopify domain')
      } else if (!query) {
        setStatus('missing query')
      }
    }
  }

  return (
    <main>
      <Container>
        <Stack spacing={2}>
          <br />
          <div>
            <Box display="flex" alignItems="center">
              <Button
                //disabled={!shopifyState || !query || status === 'running'}
                variant="contained"
                onClick={runQuery}
              >
                Run Query
              </Button>
              {loading && <CircularProgress />}
              {!loading && `   ${status}`}
            </Box>
          </div>
          <div>
            Op:
            <Select
              value={op}
              onChange={(e) => {
                setOp(e.target.value)
              }}
            >
              {opItems.map((item, index) => (
                <MenuItem key={index} value={item}>
                  {item}
                </MenuItem>
              ))}
            </Select>
          </div>
          <div>
            Path:
            <TextField
              fullWidth={true}
              variant="outlined"
              value={path}
              onChange={(e) => setPath(e.target.value)}
            />
          </div>
          <div>
            Query:
            <TextField
              multiline
              rows={3}
              fullWidth={true}
              variant="outlined"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
            />
          </div>
          <div>
            Data:
            <TextField
              multiline
              rows={3}
              fullWidth={true}
              variant="outlined"
              value={data}
              onChange={(e) => setData(e.target.value)}
            />
          </div>
          <div>
            Results:
            <JSONTree
              data={results ?? {}}
              shouldExpandNodeInitially={() => true}
            />
          </div>
        </Stack>
      </Container>
    </main>
  )
}
