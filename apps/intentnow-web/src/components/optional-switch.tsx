import { MenuItem, Select, Switch } from '@mui/material'
import _ from 'lodash'

export function OptionalSwitch(props: {
  value?: boolean | undefined
  effectiveValue: boolean
  editing: boolean
  onChange: (value: boolean | undefined) => void
}) {
  return (
    <>
      {props.editing && (
        <>
          <Select
            value={
              _.isNil(props.value) ? 'default' : String(<PERSON><PERSON>an(props.value))
            }
            onChange={(e) => {
              if (e.target.value === 'true') {
                props.onChange(true)
              } else if (e.target.value === 'false') {
                props.onChange(false)
              } else {
                props.onChange(undefined)
              }
            }}
          >
            <MenuItem value={'default'}>Default</MenuItem>
            <MenuItem value={'true'}>On</MenuItem>
            <MenuItem value={'false'}>Off</MenuItem>
          </Select>
        </>
      )}
      {!props.editing && (
        <>
          <Switch checked={props.effectiveValue} />
        </>
      )}
    </>
  )
}
