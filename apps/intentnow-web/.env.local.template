# Basic configs
NEXT_PUBLIC_APP_NAME=intentnow-web-dev
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_CLIENT_API_URL=http://localhost:4000
NEXT_PUBLIC_WEB_URL=http://localhost:3000

# Shopify Admin app configs (support multiple Shopify Apps on the same server)
NEXT_PUBLIC_SHOPIFY_APP_FE_CONFIGS=[{"API_KEY": "", "APP_HANDLE": ""}]

# Statsig API key
NEXT_PUBLIC_STATSIG_CLIENT_KEY=

# Firebase configs (to support <PERSON><PERSON> Auth on the standalone website)
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=

# The intentnow tag version
NEXT_PUBLIC_INTENTNOW_TAG_VERSION=

# Clerk Keys
CLERK_ENCRYPTION_KEY=
CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$CLERK_SECRET_KEY
