{"extends": "../../tsconfig.json", "compilerOptions": {"module": "esnext", "moduleResolution": "bundler", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noEmit": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "types": ["@emotion/react/types/css-prop"], "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "target": "ES2017"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next"]}