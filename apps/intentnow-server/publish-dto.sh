echo "Publishing Intentnow DTO types to shared-entities package..."

FROM_PATH="./dist/dto"
TO_PATH="../../packages/shared-entities/dto"

for file in $FROM_PATH/*.dto.d.ts; do
  echo "Copying $file to $TO_PATH/$(basename "${file%.d.ts}.ts")"
  cp "$file" "$TO_PATH/$(basename "${file%.d.ts}.ts")"
done

SHOPIFY_FROM_PATH="./src/types/shopify-api"
SHOPIFY_TO_PATH="../../packages/shared-entities/shopify-api"

echo "Publishing Shopify API DTO types to shared-entities package..."
cp $SHOPIFY_FROM_PATH/admin.generated.d.ts $SHOPIFY_TO_PATH/
cp $SHOPIFY_FROM_PATH/admin.types.ts $SHOPIFY_TO_PATH/