# IntentNow Server Environment Variables
# Basic Info
LOGGER_LEVEL=info
APP_NAME=intentnow-server-dev
APP_ENV=development

# Only needed when deployed to GCP
GCP_PROJECT=

# To delay the server start time so that we can debug the initialization codes
APP_START_WAIT_TIME_MS=

# Redis is currently optional. Only used for the Shopify session storage depending on the SHOPIFY_SESSION_STORAGE value
REDIS_URL=

# Robot token for admin access on some internal APIs. Can set it to any random vaue you like.
AUTH_ROBOT_TOKENS=

# On the Admin Console, only Firease users with one of these emails are allowed to access the Admin APIs
AUTH_USER_EMAILS=

# Shopify App Configurations
SHOPIFY_APP_SERVER_HOST=localhost:4001
SHOPIFY_APP_ACCESS_SCOPES=read_customer_events,write_pixels,read_price_rules,write_discounts,read_themes,read_products

# memory, redis, or firestore
SHOPIFY_SESSION_STORAGE=firestore

# Shopify Admin app configs and credentials (support multiple Shopify Apps running on the same server)
SHOPIFY_APP_CONFIGS=[{"APP_HANDLE": "", "API_KEY": "", "SECRET_KEY": "", "PROMO_EMBED_ID": "", "APP_HANDLE_2": ""}]

# IntentNow Web Pixel Configurations (needed by the Shopify App Admin to create web pixel for merchant)
INTENTNOW_EVENT_API=
INTENTNOW_EVENT_API_TOKEN=

# Analtyics event version (an always-incremented number) for filtering purpose
INTENTNOW_ANALYTICS_EVENT_VERSION=

# Statsig API key to access statsig experiments and dynamic configs
STATSIG_SECRET_KEY=

# MongoDB URI (with credentials)
MONGODB_URI=

# IP Api configs (for extracting geo-location info)
IP_API_BASE_URL=
IP_API_KEY=

# Amplitude API key
AMPLITUDE_API_KEY=
AMPLITUDE_SECRET_KEY=

# Redis
REDIS_URL=

# Firebase configs (to support Firebase Auth on the standalone website)
# These are not needed when deployed on GCP
FIREBASE_PROJECT_ID=intentnow-backend
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY={ "private_key": "" }

# Firestore database name (if missing it will connect to the production DB "(default)")
# staging or (default)
FIRESTORE_DATABASE

# Clerk Keys
CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
CLERK_JWT_PUBLIC_KEY=
CLERK_WEBHOOK_SIGNING_SECRET=

# Event shadowing configs
INTENTNOW_EVENT_SHADOW_API=

# Stripe configs
STRIPE_SECRET_KEY=
