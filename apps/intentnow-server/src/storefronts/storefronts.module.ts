import { Module } from '@nestjs/common'
import { StorefrontsService } from './storefronts.service'
import { StorefrontsController } from './storefronts.controller'
import { IntentnowModule } from 'src/intentnow/intentnow.module'
import { AuthModule } from 'src/auth/auth.module'
import { StoresModule } from 'src/stores/stores.module'
import { UsersModule } from 'src/users/users.module'
import { ShopifyModule } from 'src/shopify/shopify.module'
import { ClsModule } from 'nestjs-cls'
import { StorefrontEventsController } from './storefront-events.controller'

@Module({
  imports: [
    IntentnowModule,
    AuthModule,
    StoresModule,
    UsersModule,
    ShopifyModule,
    ClsModule,
  ],
  providers: [StorefrontsService],
  controllers: [StorefrontsController, StorefrontEventsController],
  exports: [StorefrontsService],
})
export class StorefrontsModule {}
