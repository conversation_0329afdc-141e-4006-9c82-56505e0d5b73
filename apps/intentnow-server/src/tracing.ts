import { NodeSDK } from '@opentelemetry/sdk-node'
import { PinoInstrumentation } from '@opentelemetry/instrumentation-pino'
import * as process from 'process'
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base'
import { HttpInstrumentation } from '@opentelemetry/instrumentation-http'
import { ExpressInstrumentation } from '@opentelemetry/instrumentation-express'
import { NestInstrumentation } from '@opentelemetry/instrumentation-nestjs-core'
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-grpc'
import { PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics'
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-grpc'
import { PrometheusExporter } from '@opentelemetry/exporter-prometheus'
import { UndiciInstrumentation } from '@opentelemetry/instrumentation-undici'
import { MongooseInstrumentation } from '@opentelemetry/instrumentation-mongoose'
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node'

const traceExporter = new OTLPTraceExporter()
const spanProcessor = new BatchSpanProcessor(traceExporter)

const metricReader =
  process.env.APP_ENV === 'development'
    ? new PrometheusExporter({
        port: 8081,
      }) // For local dev can be accessed on: http://localhost:8081/metrics
    : new PeriodicExportingMetricReader({
        exporter: new OTLPMetricExporter(),
        exportIntervalMillis: 60000,
      })

export const otelSDK = new NodeSDK({
  metricReader,
  spanProcessors: [spanProcessor],
  instrumentations: [
    getNodeAutoInstrumentations(),
    new PinoInstrumentation(),
    new HttpInstrumentation(),
    new ExpressInstrumentation(),
    new NestInstrumentation(),
    new UndiciInstrumentation(),
    new MongooseInstrumentation(),
  ],
})

// gracefully shut down the SDK on process exit
process.on('SIGTERM', () => {
  otelSDK
    .shutdown()
    .then(
      () => console.log('OpenTelemetry SDK shut down successfully'),
      (err) => console.error('Error shutting down OpenTelemetry SDK', err)
    )
    .finally(() => process.exit(0))
})
