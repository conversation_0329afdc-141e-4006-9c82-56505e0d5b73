import { Test, TestingModule } from '@nestjs/testing'
import { IntentnowService } from './intentnow.service'
import { ConfigService } from '@nestjs/config'
import { MetricService } from 'nestjs-otel'

describe('IntentnowService', () => {
  let service: IntentnowService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [IntentnowService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {}
        } else if (token === MetricService) {
          return {
            getCounter: () => {},
            getHistogram: () => {},
          }
        }
        return {}
      })
      .compile()

    service = module.get<IntentnowService>(IntentnowService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })
})
