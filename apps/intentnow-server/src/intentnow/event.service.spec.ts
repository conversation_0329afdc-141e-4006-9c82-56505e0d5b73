import { Test, TestingModule } from '@nestjs/testing'
import { EventService } from './event.service'
import { ConfigService } from '@nestjs/config'

describe('EventService', () => {
  let service: EventService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [EventService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            ipApi: {
              baseUrl: 'http://ip-api.com',
            },
          }
        }

        return {}
      })
      .compile()

    service = module.get<EventService>(EventService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })

  it('hash ip', () => {
    expect(service.hashIp('***************')).toBe(
      '4dabcab210766e35f03e77120e6986d6e6d4752b2a9ff22980b9253d026080d8'
    )
    expect(service.hashIp('**************')).toBe(
      'e79f0ed2681dc0e14d1e8a4f40a7e2f0238b998d0fd66ae11c0a7cc5f925e4fe'
    )
  })

  it('hashedIp composite key', () => {
    expect(
      service.getCompositeKeyForHashedIp(
        'c59e9595-210b-4ee0-8c21-41be5fb1f5e3',
        'e79f0ed2681dc0e14d1e8a4f40a7e2f0238b998d0fd66ae11c0a7cc5f925e4fe'
      )
    ).toBe('deb396a6c4584c516f03f212a2c827c019894a32e0fa342044eea8961f057a89')
    expect(
      service.getCompositeKeyForHashedIp(
        'e6b126f7-1081-4b51-a572-17914fe20fc0',
        '8f940c909a7b3fc11334744666c2937748d58413c333bb3e1754a868019f59be'
      )
    ).toBe('a42a4663139a6616a775921554c0fbd848037d3be302e5713ac85cd4baa6f854')
  })
})
