import { Test, TestingModule } from '@nestjs/testing'
import { ConfigService } from '@nestjs/config'
import { MetricService } from 'nestjs-otel'
import { StoreConfigService } from './store-config.service'

describe('StoreConfigService', () => {
  let service: StoreConfigService

  // Mock data
  const mockShop = 'test-shop.myshopify.com'
  const mockStoreId = 'store-123'
  const mockPromoConfig = {
    model: {
      parameters: {
        temperature: 0.7,
        maxTokens: 100,
      },
    },
    dialog: {
      title: 'Test Promo',
      description: 'Test description',
    },
    discount: {
      codeGen: {
        alphabet: 'ABCD',
        length: 4,
      },
      discountDurationInMinutes: 60,
      discountInput: {
        customerGets: 0.2,
        items: 'all',
        title: 'bassic discount',
      },
    },
  }

  const mockAnalyticsConfig = {
    enabled: true,
    trackingId: 'UA-12345',
  }

  const mockPromoConfigVariants = [
    {
      name: 'variant1',
      model: {
        parameters: {
          temperature: 0.9,
        },
      },
      dialog: {
        title: 'Variant Promo',
      },
      discount: {
        discountDurationInMinutes: 120,
        discountInput: {
          customerGets: 0.15,
        },
      },
    },
  ]

  const mockShopifyStore = {
    _id: mockStoreId,
    shop: mockShop,
    promoConfig: mockPromoConfig,
    analyticsConfig: mockAnalyticsConfig,
    promoConfigVariants: mockPromoConfigVariants,
  }

  // Mock Firestore objects
  const mockDocumentData = jest.fn().mockReturnValue(mockShopifyStore)
  const mockDocumentSnapshot = {
    data: mockDocumentData,
    id: mockStoreId,
    exists: true,
  }

  const mockQuerySnapshot = {
    empty: false,
    docs: [mockDocumentSnapshot],
  }

  const mockQuery = {
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn().mockResolvedValue(mockQuerySnapshot),
  }

  const mockWidgetsQuerySnapshot = {
    empty: true,
    docs: [],
  }

  const mockWidgetsQuery = {
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    get: jest.fn().mockResolvedValue(mockWidgetsQuerySnapshot),
  }

  const mockLaunchConfigsQuerySnapshot = {
    empty: true,
    docs: [],
  }

  const mockLaunchConfigsQuery = {
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    get: jest.fn().mockResolvedValue(mockLaunchConfigsQuerySnapshot),
  }

  const mockCollection = jest.fn((collection: string) => {
    if (collection === 'ShopifyStores/store-123/ShopifyStoreWidgets') {
      return mockWidgetsQuery
    }

    if (collection === 'ShopifyStores/store-123/ShopifyStoreLaunchConfigs') {
      return mockLaunchConfigsQuery
    }

    return mockQuery
  })

  const mockFirestore = {
    collection: mockCollection,
  }

  const mockLogger = {
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  }

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [StoreConfigService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            app: {
              name: 'test-app',
            },
            intentnow: {
              modelApi: {},
              storePromo: {
                storeConfigCacheTTLInSeconds: 60,
              },
            },
          }
        } else if (token === MetricService) {
          return {
            getCounter: () => {},
          }
        } else if (token === 'FIRESTORE_CLIENT') {
          return mockFirestore
        }
        return {}
      })
      .compile()

    service = module.get<StoreConfigService>(StoreConfigService)
    service.logger = mockLogger as any
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })

  it('should fetch and cache config from Firestore', async () => {
    let result = await service.getStoreConfigForGetDiscount(mockShop)

    expect(result).toEqual({
      shopifyStoreId: mockStoreId,
      promoConfig: mockPromoConfig,
      analyticsConfig: mockAnalyticsConfig,
      promoConfigVariants: ['variant1'],
      _widgets: [],
    })

    expect(service['shopifyStorePromoConfigCache'][mockShop]).toBeDefined()
    expect(
      service['shopifyStorePromoConfigCache'][mockShop]!.shopifyStore._id
    ).toBe(mockStoreId)

    //This time it should get it from cache
    result = await service.getStoreConfigForGetDiscount(mockShop)

    expect(result).toEqual({
      shopifyStoreId: mockStoreId,
      promoConfig: mockPromoConfig,
      analyticsConfig: mockAnalyticsConfig,
      promoConfigVariants: ['variant1'],
      _widgets: [],
    })

    expect(mockCollection).toHaveBeenCalledWith('ShopifyStores')
    expect(mockCollection).toHaveBeenCalledTimes(3) // Get store config and the widgets and the launch configs
    expect(mockQuery.where).toHaveBeenCalledWith('shop', '==', mockShop)
    expect(mockQuery.limit).toHaveBeenCalledWith(1)
    expect(mockQuery.get).toHaveBeenCalled()
  })

  it('should return cached config', async () => {
    // First call to populate the cache
    await service.getStoreConfigForGetDiscount(mockShop)

    // Reset mocks to verify they're not called again
    jest.clearAllMocks()

    // Second call should use cache
    const result = await service.getStoreConfigForGetDiscount(mockShop)

    expect(mockCollection).not.toHaveBeenCalled()
    expect(mockQuery.get).not.toHaveBeenCalled()

    expect(result).toEqual({
      shopifyStoreId: mockStoreId,
      promoConfig: mockPromoConfig,
      analyticsConfig: mockAnalyticsConfig,
      promoConfigVariants: ['variant1'],
      _widgets: [],
    })

    expect(mockLogger.log).toHaveBeenCalledWith(
      { shop: mockShop },
      'getStoreConfig: returning cached config'
    )
  })

  it('should merge config variant when specified', async () => {
    const variantName = 'variant1'
    const result = await service.getStoreConfigForGetDiscount(
      mockShop,
      variantName
    )

    // Expected merged config
    const expectedMergedConfig = {
      model: {
        parameters: {
          temperature: 0.9, // Overridden by variant
          maxTokens: 100, // From base config
        },
      },
      dialog: {
        title: 'Variant Promo', // Overridden by variant
        description: 'Test description', // From base config
      },
      discount: {
        codeGen: {
          alphabet: 'ABCD',
          length: 4,
        },
        discountDurationInMinutes: 120,
        //discountInput should be overridden entirely
        discountInput: {
          customerGets: 0.15,
        },
      },
    }

    expect(result.promoConfig).toEqual(expectedMergedConfig)
    expect(mockLogger.log).toHaveBeenCalledWith(
      { shop: mockShop, configVariantName: variantName },
      'getStoreConfig: configVariant found, calculating merged config'
    )
  })

  it('should log error when requested variant does not exist', async () => {
    const nonExistentVariant = 'non-existent-variant'
    const result = await service.getStoreConfigForGetDiscount(
      mockShop,
      nonExistentVariant
    )

    // Should return the base config without merging
    expect(result.promoConfig).toEqual(mockPromoConfig)
    expect(mockLogger.error).toHaveBeenCalledWith(
      { shop: mockShop, configVariantName: nonExistentVariant },
      'getStoreConfig: configVariant not found'
    )
  })

  it('should return empty object when no valid config exists', async () => {
    // Mock empty query result
    mockQuerySnapshot.empty = true

    const result = await service.getStoreConfigForGetDiscount(mockShop)

    expect(result).toEqual({})
    expect(mockLogger.warn).toHaveBeenCalledWith(
      { shop: mockShop },
      'getStoreConfig: no valid config'
    )

    // Reset for other tests
    mockQuerySnapshot.empty = false
  })

  it('should handle store with missing promoConfig', async () => {
    // Mock store without promoConfig
    const storeWithoutPromoConfig = {
      ...mockShopifyStore,
      promoConfig: undefined,
    }
    mockDocumentData.mockReturnValueOnce(storeWithoutPromoConfig)

    const result = await service.getStoreConfigForGetDiscount(mockShop)

    expect(result).toEqual({})
    expect(mockLogger.warn).toHaveBeenCalledWith(
      { shop: mockShop },
      'getStoreConfig: invalid config, no promoConfig'
    )
    expect(service['shopifyStorePromoConfigCache'][mockShop]).toBeUndefined()

    // Reset mock for other tests
    mockDocumentData.mockReturnValue(mockShopifyStore)
  })
})
