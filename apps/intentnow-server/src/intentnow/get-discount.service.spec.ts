import { Test, TestingModule } from '@nestjs/testing'
import { ConfigService } from '@nestjs/config'
import { MetricService } from 'nestjs-otel'
import { GetDiscountService } from './get-discount.service'

describe('StorePromoService', () => {
  let service: GetDiscountService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GetDiscountService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            intentnow: {
              modelApi: {},
              storePromo: {
                storeConfigCacheTTLInSeconds: 60,
              },
            },
          }
        } else if (token === MetricService) {
          return {
            getCounter: () => {},
          }
        }

        return {}
      })
      .compile()

    service = module.get<GetDiscountService>(GetDiscountService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })
})
