import { MiddlewareConsumer, Module } from '@nestjs/common'
import { IntentnowService } from './intentnow.service'
import { IntentnowShopifyEmbeddedController } from './intentnow-shopify-embedded.controller'
import { ShopifyModule } from 'src/shopify/shopify.module'
import { ShopifyService } from 'src/shopify/shopify.service'
import { AuthModule } from 'src/auth/auth.module'
import { GetDiscountService } from './get-discount.service'
import { MongooseModule } from '@nestjs/mongoose'
import {
  Client,
  ClientSchema,
  Event,
  EventSchema,
  HashedIp,
  HashedIpSchema,
  RawEvent,
  RawEventSchema,
  IpLocation,
  IpLocationSchema,
} from './entities/event.mongo'
import { NestjsFormDataModule } from 'nestjs-form-data'
import { StoreConfigService } from './store-config.service'
import { EventService } from './event.service'
import { StorageModule, DriverType } from '@codebrew/nestjs-storage'
import { ConfigService } from '@nestjs/config'
import { ServerConfig } from 'src/config/config'
import { ClsModule } from 'nestjs-cls'
import { StoresModule } from 'src/stores/stores.module'

@Module({
  imports: [
    ClsModule,
    ShopifyModule,
    AuthModule,
    StoresModule,
    MongooseModule.forFeature([
      {
        name: Event.name,
        collection: 'events',
        schema: EventSchema,
      },
      {
        name: HashedIp.name,
        collection: 'hashed_ips',
        schema: HashedIpSchema,
      },
      {
        name: Client.name,
        collection: 'clients',
        schema: ClientSchema,
      },
      {
        name: RawEvent.name,
        collection: 'raw_events',
        schema: RawEventSchema,
      },
      {
        name: IpLocation.name,
        collection: 'ip_locations',
        schema: IpLocationSchema,
      },
    ]),
    NestjsFormDataModule,
    StorageModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const cdnBucket =
          configService.get<ServerConfig['intentnow']>('intentnow')
            ?.imageStorage.cdnBucket
        return {
          default: 'gcs',
          disks: {
            gcs: {
              driver: DriverType.GCS,
              config: {
                bucket: cdnBucket,
              },
            },
          },
        }
      },
    }),
  ],
  providers: [
    IntentnowService,
    GetDiscountService,
    StoreConfigService,
    EventService,
  ],
  controllers: [IntentnowShopifyEmbeddedController],
  exports: [
    IntentnowService,
    GetDiscountService,
    StoreConfigService,
    EventService,
  ],
})
export class IntentnowModule {
  constructor(private readonly shopifyService: ShopifyService) {}

  configure(consumer: MiddlewareConsumer) {
    const shopifyApps = this.shopifyService.getShopifyApps()

    shopifyApps.forEach((shopifyAppCfg) => {
      const shopifyApp = shopifyAppCfg.shopifyApp
      const appHandle = shopifyAppCfg.shopifyAppHandle

      //Authenticated routes
      ;[
        'intentnow/web-pixel',
        'intentnow/app-settings',
        'intentnow/analytics-settings',
        'intentnow/generated-discounts',
        'intentnow/create-link-store-request',
      ].forEach((route) => {
        consumer
          .apply(shopifyApp.cspHeaders())
          .forRoutes(`/api/shopify/${appHandle}/${route}`)

        consumer
          .apply(
            shopifyApp.ensureInstalledOnShop(),
            shopifyApp.validateAuthenticatedSession()
          )
          .forRoutes(`/api/shopify/${appHandle}/${route}`)
      })
    })
  }
}
