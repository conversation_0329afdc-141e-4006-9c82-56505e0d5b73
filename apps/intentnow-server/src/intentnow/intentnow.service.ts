import { Inject, Injectable, Logger } from '@nestjs/common'
import { ShopifyService } from 'src/shopify/shopify.service'
import { ConfigService } from '@nestjs/config'
import { ServerConfig } from 'src/config/config'
import { GetDiscountService } from './get-discount.service'
import * as crypto from 'crypto'
import { initializeApiFetchMetrics } from 'src/common/fetch'
import { Firestore } from '@google-cloud/firestore'
import * as amplitude from '@amplitude/analytics-node'
import { MetricService } from 'nestjs-otel'
import { Counter } from '@opentelemetry/api'
import { StoreConfigService } from './store-config.service'
import { EventService } from './event.service'
import { UserContext } from 'src/auth/entities/user-context'
import { StoresService } from 'src/stores/stores.service'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'

@Injectable()
export class IntentnowService {
  private readonly logger = new Logger(IntentnowService.name)
  private readonly intentowConfig: ServerConfig['intentnow']
  private readonly discountOnCounter: Counter
  private readonly discountOffCounter: Counter
  private readonly discountInvalidRequestCounter: Counter

  constructor(
    private readonly configService: ConfigService,
    private readonly shopifyService: ShopifyService,
    private readonly eventService: EventService,
    private readonly getDiscountService: GetDiscountService,
    private readonly storeConfigService: StoreConfigService,
    private readonly metricService: MetricService,
    private readonly storesService: StoresService,
    private readonly apiAuthGuard: ApiAuthGuard,
    @Inject('FIRESTORE_CLIENT') private readonly firestore: Firestore
  ) {
    this.intentowConfig =
      this.configService.get<ServerConfig['intentnow']>('intentnow')!

    this.discountOnCounter = this.metricService.getCounter('discount_on', {
      prefix: 'intentnow_server',
    })
    this.discountOffCounter = this.metricService.getCounter('discount_off', {
      prefix: 'intentnow_server',
    })
    this.discountInvalidRequestCounter = this.metricService.getCounter(
      'discount_invalid_request',
      {
        prefix: 'intentnow_server',
      }
    )
    this.metricService && initializeApiFetchMetrics(this.metricService)
  }

  async getClientDiscount(
    requester: UserContext | undefined,
    clientId: string | undefined,
    preview?: boolean | undefined,
    previewWidgetId?: string | undefined
  ) {
    const request = this.shopifyService.validateRequester(requester)
    const shop = request.shop!

    this.logger.log(
      {
        shop,
        clientId,
        preview,
      },
      `getClientDiscount: started`
    )

    if (preview) {
      this.logger.log({ shop }, `getClientDiscount: preview mode`)
      return await this.getDiscountService.getDiscountAndWidgetPreview(
        requester,
        previewWidgetId
      )
    }

    if (!clientId) {
      this.logger.log({ shop }, `getClientDiscount: no clientId`)
      this.discountInvalidRequestCounter.add(1, {
        shop,
      })
      return {}
    }

    const dynamicStoreConfig =
      await this.storeConfigService.getDyanmicStoreConfig(shop, clientId)
    const {
      getDiscount,
      modelSplit,
      statsigKeys,
      sendAmplitudeEvents,
      isNewStore,
    } = dynamicStoreConfig

    if (isNewStore) {
      this.logger.error(
        {
          shop,
        },
        `getClientDiscount: this is a new store, should not call this function`
      )
      return {}
    }

    const featureOn = Boolean(getDiscount && modelSplit)

    if (!featureOn) {
      this.logger.log(
        {
          shop,
          getDiscount,
          modelSplit,
          statsigKeys,
          clientId,
        },
        `getClientDiscount: feature off`
      )
      this.discountOffCounter.add(1, {
        shop,
      })
      return {}
    }
    this.logger.log(
      {
        shop,
        getDiscount,
        modelSplit,
        statsigKeys,
        clientId,
      },
      `getClientDiscount: feature on`
    )
    this.discountOnCounter.add(1, {
      shop,
    })

    try {
      const {
        dialog,
        discount,
        widgetId,
        widget2,
        widget3,
        promoCandidate,
        configName,
        configVariantIndex,
      } = await this.getDiscountService.getDiscountAndWidgetV2(
        requester,
        clientId,
        modelSplit!,
        statsigKeys?.modelExperimentKey ?? 'unknown'
      )

      if (sendAmplitudeEvents) {
        const identifyObj = new amplitude.Identify()
        identifyObj.set(
          'eventVersion',
          this.intentowConfig.analyticsEventVersion
        )

        if (configName && modelSplit === 'test') {
          identifyObj.set('launchConfig', `${configName}_${configVariantIndex}`)
        }

        if (promoCandidate !== undefined) {
          identifyObj.set(`promoCandidate`, promoCandidate)
        }

        amplitude.identify(identifyObj, {
          user_id: clientId,
          device_id: clientId,
        })

        if (promoCandidate !== undefined) {
          await this.eventService.saveEvent(
            {
              id: crypto.randomUUID(),
              name: 'intentnow-promo-candidate',
              shop,
              clientId,
              type: 'custom-intentnow',
              eventSource: 'intentnow-server',
              timestamp: new Date().toISOString(),
              data: {
                promoCandidate: promoCandidate,
              },
            },
            undefined,
            undefined,
            dynamicStoreConfig
          )
        }
      }

      return {
        configName,
        configVariantIndex,
        dialog,
        discount,
        widgetId,
        widget2,
        widget3,
      }
    } catch (e) {
      this.logger.error(
        e,
        `getClientDiscount: failed to create personalized discount code`
      )
    }

    this.logger.log(`getClientDiscount: no discount available`)
    return {}
  }
}
