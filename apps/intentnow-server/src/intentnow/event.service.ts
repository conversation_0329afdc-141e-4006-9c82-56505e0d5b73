import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { ServerConfig } from 'src/config/config'
import { GetDiscountService } from './get-discount.service'
import Statsig from 'statsig-node'
import { InjectModel } from '@nestjs/mongoose'
import {
  Client,
  Event,
  EventCustomer,
  HashedIp,
  RawEvent,
  IpLocation,
} from './entities/event.mongo'
import { Model, UpdateQuery } from 'mongoose'
import * as crypto from 'crypto'
import { getApiFetch } from 'src/common/fetch'
import * as amplitude from '@amplitude/analytics-node'
import { StoreConfigService } from './store-config.service'
import { EventRequestDto, RawEventRequestDto } from '../dto/event.dto'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { PixelEventsCheckoutCompletedData } from '@shopify/web-pixels-extension'

@Injectable()
export class EventService {
  logger = new Logger(EventService.name)
  private readonly intentowConfig: ServerConfig['intentnow']
  private readonly ipApiConfig: ServerConfig['ipApi']
  private readonly ipLocApiFetch: ReturnType<typeof getApiFetch>['apiFetch']

  constructor(
    private readonly configService: ConfigService,
    private readonly storeConfigService: StoreConfigService,
    private readonly getDiscountService: GetDiscountService,
    @InjectModel(Event.name) private eventModel: Model<Event>,
    @InjectModel(HashedIp.name) private hashedIpModel: Model<HashedIp>,
    @InjectModel(Client.name) private readonly clientModel: Model<Client>,
    @InjectModel(RawEvent.name) private rawEventModel: Model<RawEvent>,
    @InjectModel(IpLocation.name)
    private readonly ipLocationModel: Model<IpLocation>,
    private readonly apiAuthGuard: ApiAuthGuard
  ) {
    this.intentowConfig =
      this.configService.get<ServerConfig['intentnow']>('intentnow')!
    this.ipApiConfig = this.configService.get<ServerConfig['ipApi']>('ipApi')!

    const { apiFetch: ipLocApiFetch } = getApiFetch(
      this.ipApiConfig.baseUrl,
      undefined,
      this.logger
    )
    this.ipLocApiFetch = ipLocApiFetch
  }

  async saveEvents(
    events: EventRequestDto[],
    ipAddress: string | undefined,
    userAgent?: string | undefined,
    isShadow?: boolean
  ): Promise<
    {
      message: string
      prediction?: boolean
    }[]
  > {
    const response = []
    for (const event of events) {
      const res = await this.saveEvent(
        event,
        ipAddress,
        userAgent,
        undefined,
        isShadow
      )
      response.push(res)
    }

    return response
  }

  async saveEvent(
    event: EventRequestDto,
    ipAddress: string | undefined,
    userAgent?: string | undefined,
    dynamicStoreConfig?: Awaited<
      ReturnType<typeof this.storeConfigService.getDyanmicStoreConfig>
    >,
    isShadow?: boolean
  ): Promise<{ message: string; prediction?: boolean }> {
    try {
      if (!event.clientId || !event.shop || !event.name) {
        this.logger.warn(
          {
            event,
          },
          `saveEvent: missing required event attributes`
        )
        return {
          message: 'Event missing required attributes',
        }
      }

      const {
        predictWithEvents,
        modelSplit,
        sendStatsigEvents: sendStatsigEvents1,
        statsigKeys,
        sendAmplitudeEvents: sendAmplitudeEvents1,
        saveEvents,
        isNewStore,
        newStoreId,
      } = dynamicStoreConfig ??
      (await this.storeConfigService.getDyanmicStoreConfig(
        event.shop,
        event.clientId
      ))
      const sendStatsigEvents = !isShadow && sendStatsigEvents1
      const sendAmplitudeEvents = !isShadow && sendAmplitudeEvents1

      if (!saveEvents) {
        this.logger.debug(
          {
            event,
          },
          `saveEvent: saveEvents config is off`
        )
        return {
          message: 'Event not saved',
        }
      }

      if (event.id) {
        const existingEvent = await this.eventModel.findOne({
          id: event.id,
        })
        if (existingEvent) {
          this.logger.warn(
            {
              event,
            },
            `saveEvent: duplicate event id`
          )
          return {
            message: 'Duplciate event id',
          }
        }
      }

      let sendStatsig = Boolean(
        sendStatsigEvents &&
          event.name &&
          event.clientId &&
          this.intentowConfig.eventsSentToStatsig[event.name]
      )

      if (sendStatsig && event.name === 'page_viewed') {
        try {
          const lastEvent = await this.eventModel.findOne(
            {
              clientId: event.clientId,
              shop: event.shop,
              name: event.name,
            },
            undefined,
            {
              sort: {
                serverTimestamp: -1,
              },
            }
          )
          if (lastEvent?.serverTimestamp) {
            //Send statsig page_viewed events once per 15 minutes at most
            const now = new Date()
            if (
              now.getTime() <
              lastEvent.serverTimestamp.getTime() + 1000 * 60 * 15
            ) {
              sendStatsig = false
            }
          }
        } catch (e) {
          this.logger.error(
            e,
            {
              clientId: event.clientId,
              shop: event.shop,
            },
            `saveEvent: failed to check for the last page_viewed event`
          )
        }
      }

      const decoratedEvent = await this.decorateEvent(
        event,
        ipAddress,
        newStoreId
      )
      const createdEvent = new this.eventModel(decoratedEvent satisfies Event)
      await createdEvent.save()

      let prediction: boolean | undefined
      let launchConfigVariant:
        | {
            configName?: string
            configVariantIndex: number
          }
        | undefined

      if (event.name === 'page_viewed') {
        if (ipAddress && decoratedEvent.hashedIp) {
          try {
            await this.storeHashedIp(
              decoratedEvent.clientId,
              ipAddress,
              decoratedEvent.hashedIp
            )
          } catch (e) {
            //Record the error but continue
            this.logger.error(
              e,
              {
                clientId: decoratedEvent.clientId,
                ipAddress,
                hashedIp: decoratedEvent.hashedIp,
              },
              `saveEvent: failed to store hashed ip`
            )
          }
        }

        if (predictWithEvents && modelSplit) {
          try {
            const robotUser = await this.apiAuthGuard.robotUserForShop(
              event.shop
            )
            const { configName, configVariantIndex } =
              await this.getDiscountService.runPrediction(
                robotUser,
                event.clientId,
                modelSplit,
                statsigKeys?.modelExperimentKey ?? 'unknown'
              )
            launchConfigVariant = {
              configName,
              configVariantIndex,
            }
            prediction = true
          } catch (e) {
            //Failed to call prediction, but we should ignore it and continue
            this.logger.error(
              e,
              {
                shop: event.shop,
                clientId: event.clientId,
              },
              'saveEvent: failed to get model prediction'
            )
          }
        }
      }

      if (event.type === 'dom') {
        //Don't send dom events to analytics
      } else {
        //Currently only using eventName for Amplitude
        const { eventName, eventData } =
          this.getAnalyticsEventData(decoratedEvent)

        if (sendStatsig) {
          try {
            Statsig.logEvent(
              {
                userID: event.clientId,
              },
              event.name,
              undefined,
              eventData
            )
          } catch (e) {
            this.logger.error(
              e,
              {
                event,
              },
              `saveEvent: failed to send Statsig event`
            )
          }
        }

        //Send Amplitude event
        if (sendAmplitudeEvents) {
          try {
            const identifyObj = new amplitude.Identify()
            identifyObj.set(
              'eventVersion',
              this.intentowConfig.analyticsEventVersion
            )

            //identifyObj.set('shop', event.shop)
            //identifyObj.unset('shop')

            if (!isNewStore) {
              //This is only for the prediction case
              if (launchConfigVariant?.configName && modelSplit === 'test') {
                identifyObj.set(
                  'launchConfig',
                  `${launchConfigVariant.configName}_${launchConfigVariant.configVariantIndex}`
                )
              }
            }

            if (decoratedEvent.customer) {
              decoratedEvent.customer.customerId &&
                identifyObj.set(
                  'customer_id',
                  decoratedEvent.customer.customerId
                )
              decoratedEvent.customer.hash &&
                identifyObj.set('email_hash', decoratedEvent.customer.hash)
            }
            amplitude.identify(identifyObj, {
              user_id: event.clientId,
              device_id: event.clientId,
            })

            amplitude.track(eventName, eventData, {
              user_id: event.clientId,
              device_id: event.clientId,
              ip: ipAddress,
              user_agent: userAgent,
            })
          } catch (e) {
            this.logger.error(
              e,
              {
                event,
              },
              `saveEvent: failed to send Amplitude event`
            )
          }
        }

        if (event.name === 'checkout_completed') {
          await this.recordCheckoutToClient(
            decoratedEvent.shop,
            decoratedEvent.clientId,
            decoratedEvent.mergedClientId,
            decoratedEvent.customer?.hash
          )
        }
      }

      return {
        message: 'Event recorded successfully',
        prediction,
      }
    } catch (e) {
      this.logger.error(
        e,
        {
          event,
        },
        'saveEvent: failed to save event'
      )
      throw new InternalServerErrorException('Failed to save event')
    }
  }

  async saveRawEvents(events: RawEventRequestDto[]) {
    const response = []
    for (const event of events) {
      const res = await this.saveRawEvent(event)
      response.push(res)
    }
    return response
  }

  async saveRawEvent(event: RawEventRequestDto) {
    try {
      const decoratedEvent: RawEvent = {
        ...event,
        serverTimestamp: new Date(),
      }
      const createdEvent = new this.rawEventModel(decoratedEvent)
      await createdEvent.save()

      return {
        message: 'Raw event recorded successfully',
      }
    } catch (e) {
      this.logger.error(e, `saveRawEvent: failed`)
      throw new InternalServerErrorException('Failed to save raw event')
    }
  }

  async storeHashedIp(clientId: string, ipAddress: string, hashedIp: string) {
    //TODO: deprecate the hashed_ips table (and only keep the fetchIpLocationData() call) after the modeling switches to using the new ip_locations for location info
    try {
      const locationData = await this.fetchIpLocationData(ipAddress, hashedIp)
      const compositeKey = this.getCompositeKeyForHashedIp(clientId, hashedIp)

      const hashedIpRecord = await this.hashedIpModel.findById(compositeKey)
      if (!hashedIpRecord) {
        const createdHashedIp = new this.hashedIpModel({
          _id: compositeKey,
          client_id: clientId,
          hashed_ip: hashedIp,
          location_data: locationData,
          created_at: new Date(),
        } satisfies HashedIp)
        return await createdHashedIp.save()
      }

      return hashedIpRecord
    } catch (e) {
      //11000 is the error code for duplicate key (expected due to race condition)
      if (e.code !== 11000) {
        this.logger.error(
          e,
          {
            clientId,
            ipAddress,
            hashedIp,
          },
          `storeHashedIp: failed to store hashed ip`
        )
        throw e
      }
    }
  }

  async fetchIpLocationData(ipAddress: string, hashedIp: string) {
    //check if we already have the location data
    const existingIpLocation = await this.ipLocationModel.findById(hashedIp)
    if (existingIpLocation) {
      return existingIpLocation.locationData
    }

    const url = this.ipApiConfig.apiKey
      ? `/${ipAddress}?key=${this.ipApiConfig.apiKey}&fields=52799`
      : `/${ipAddress}?fields=52799`
    const response = await this.ipLocApiFetch<
      any & {
        status: string
      }
    >(url)

    if (response.status !== 'success') {
      this.logger.warn(
        {
          ipAddress,
          response,
        },
        `fetchIpLocaltionData: failed to fetch location data`
      )
      //throw new Error('Failed to fetch location data')
    } else {
      //Save the response to the DB
      try {
        const newIpLocation = new this.ipLocationModel({
          _id: hashedIp,
          hashedIp: hashedIp,
          createdAt: new Date(),
          locationData: response,
        } satisfies IpLocation)
        await newIpLocation.save()
      } catch (e) {
        //11000 is the error code for duplicate key (expected due to race condition)
        if (e.code !== 11000) {
          this.logger.error(
            e,
            {
              ipAddress,
              hashedIp,
            },
            `fetchIpLocationData: failed to save the ip location data`
          )
          throw e
        }
      }
    }

    return response
  }

  async decorateEvent(
    event: EventRequestDto,
    ipAddress: string | undefined,
    newStoreId: string | undefined
  ) {
    const now = new Date()
    const hashedIp = ipAddress ? this.hashIp(ipAddress) : undefined

    //Save client record
    let mergedClientId = event.clientId
    let mergedCustomer: EventCustomer | undefined
    try {
      const { mergedClientId: mergedId, mergedCustomer: mergedCust } =
        await this.mergeAndSaveClient(event.clientId, {
          customerId: event.customer?.customerId,
          emailHash: event.customer?.hash,
          shop: event.shop,
          hashedIp,
        })
      mergedClientId = mergedId
      mergedCustomer = mergedCust
    } catch (e) {
      this.logger.error(
        e,
        {
          clientId: event.clientId,
          shop: event.shop,
        },
        `saveEvent: failed to merge and save client record`
      )
    }

    const decoratedEvent = {
      ...event,
      mergedClientId,
      hashedIp,
      timestamp: event.timestamp ? new Date(event.timestamp) : now,
      serverTimestamp: now,
      storeId: newStoreId,

      ...(mergedCustomer ? { customer: mergedCustomer } : {}),
    } satisfies Event

    if (event.type === 'standard' && event.name === 'checkout_completed') {
      try {
        const checkoutData = event.data as PixelEventsCheckoutCompletedData

        const discountsInEvent =
          checkoutData?.checkout?.discountApplications ?? []

        let intentNowDiscount = false
        let intentNowDiscountCode: string | undefined
        for (const discount of discountsInEvent) {
          if (discount.type === 'DISCOUNT_CODE' && discount.title) {
            const { storedDiscount } =
              await this.getDiscountService.findStoredDiscountByCode(
                event.shop,
                event.clientId,
                discount.title
              )
            if (storedDiscount) {
              intentNowDiscount = true
              intentNowDiscountCode = storedDiscount.discount.code
              break
            }
          }
        }

        if (intentNowDiscount) {
          this.logger.log(
            `decorateEvent: checkout_completed event has IntentNow discount`
          )
          decoratedEvent.data = {
            ...event.data,
            intentNow: {
              useDiscount: true,
              discountCode: intentNowDiscountCode,
            },
          }
        }
      } catch (e) {
        this.logger.error(
          e,
          `decorateEvent: failed to retrieve checkout_completed event`
        )
      }
    }

    return decoratedEvent
  }

  getAnalyticsEventData(event: Event) {
    //TODO: map "shop" to "storeId" (Redis cache)
    const comonEventData = {
      eventVersion: this.intentowConfig.analyticsEventVersion,

      clientId: event.clientId,
      shop: event.shop,
      eventSource: event.eventSource,

      type: event.type,
      name: event.name,

      storeId: event.storeId,
    }

    if (event.type === 'custom-intentnow') {
      return {
        eventName: `[IntentNow] ${event.name}`,
        eventData: {
          ...event.data,
          ...comonEventData,
        },
      }
    } else {
      const checkoutData =
        event.name === 'checkout_completed'
          ? (event.data as PixelEventsCheckoutCompletedData)
          : undefined

      const eventData: any = {
        ...comonEventData,

        //all shopify events
        pathname: event.context?.document?.location?.pathname,

        //checkout_completed event
        ...(checkoutData
          ? {
              checkoutTotalPriceAmount:
                checkoutData?.checkout?.totalPrice?.amount,
              checkoutSubtotalPriceAmount:
                checkoutData?.checkout?.subtotalPrice?.amount,
              checkoutDiscountAmount:
                checkoutData?.checkout?.discountsAmount?.amount,
              checkoutDiscountCode:
                checkoutData?.checkout?.discountApplications?.[0]?.title,
              checkoutLineItemCount:
                checkoutData?.checkout?.lineItems?.length ?? 0,
              customerIsFirstOrder:
                checkoutData?.checkout?.order?.customer?.isFirstOrder,
            }
          : {}),

        checkoutUseIntentNowDiscount: event.data?.intentNow?.useDiscount,
      }

      if (eventData.checkoutSubtotalPriceAmount) {
        eventData.checkoutSubtotalPriceAmountBeforeDiscount =
          eventData.checkoutSubtotalPriceAmount +
          (eventData.checkoutDiscountAmount ?? 0)
      }

      const eventName = `[Shopify] ${event.name}`

      return {
        eventName,
        eventData,
      }
    }
  }

  hashIp(ip: string) {
    const hash = crypto.createHash('sha256')
    hash.update(ip)
    return hash.digest('hex')
  }

  getCompositeKeyForHashedIp(clientId: string, hashedIp: string) {
    const compositeString = `${hashedIp}${clientId}`
    const hash = crypto.createHash('sha256')
    hash.update(compositeString)
    return hash.digest('hex')
  }

  async recordCheckoutToClient(
    shop: string,
    clientId: string,
    mergedClientId: string,
    emailHash?: string
  ) {
    this.logger.log(
      {
        shop,
        clientId,
        mergedClientId,
        emailHash,
      },
      `recordCheckoutToClient: started`
    )
    try {
      const clientIds = [clientId]
      if (clientId !== mergedClientId) {
        clientIds.push(mergedClientId)
      }

      if (emailHash) {
        const results = await this.clientModel.find({
          shop: {
            $eq: shop,
          },
          emailHash: {
            $eq: emailHash,
          },
        })
        results.forEach((result) => {
          if (!clientIds.includes(result.clientId)) {
            clientIds.push(result.clientId)
          }
        })
      }
      this.logger.log(
        {
          clientIds,
        },
        `recordCheckoutToClient: start recording`
      )
      const now = new Date()
      await this.clientModel.updateMany(
        {
          _id: { $in: clientIds },
          shop: {
            $eq: shop,
          },
        },
        {
          updatedAt: now,
          lastCheckoutAt: now,
        }
      )

      this.logger.log(
        {
          shop,
          clientId,
          mergedClientId,
          emailHash,
        },
        `recordCheckoutToClient: done`
      )
    } catch (e) {
      this.logger.error(
        e,
        {
          shop,
          clientId,
          mergedClientId,
          emailHash,
        },
        `recordCheckoutToClient: failed`
      )
    }
  }

  async recordClient(
    clientId: string,
    mergedClientId: string,
    data: {
      shop: string
      customerId?: string
      emailHash?: string
      hashedIp?: string
    },
    existingClient?: Client | null
  ) {
    //TODO: use transaction to eliminate race condition?
    //so far it doesn't seem that the race condition is a big issue
    if (!existingClient) {
      existingClient = await this.clientModel.findById(clientId)
    }
    const now = new Date()

    let clientUpdate: UpdateQuery<Client> | undefined
    if (existingClient) {
      let isChanged = false
      const newHashedIps = existingClient.hashedIps ?? []
      if (data.hashedIp && !newHashedIps.includes(data.hashedIp)) {
        newHashedIps.push(data.hashedIp)
        isChanged = true
      }

      if (!isChanged) {
        if (existingClient.shop !== data.shop) {
          isChanged = true
        } else if (
          data.customerId &&
          existingClient.customerId2 !== data.customerId
        ) {
          isChanged = true
        } else if (
          data.emailHash &&
          existingClient.emailHash !== data.emailHash
        ) {
          isChanged = true
        } else if (existingClient.clientId !== clientId) {
          isChanged = true
        } else if (existingClient.mergedClientId2 !== mergedClientId) {
          isChanged = true
        }
      }

      if (isChanged) {
        this.logger.debug(
          {
            clientId,
          },
          `recordClient: updating existing client record`
        )
        clientUpdate = new this.clientModel({
          clientId,
          mergedClientId2: mergedClientId,
          updatedAt: now,
          lastSeenAt: now,
          shop: data.shop,

          ...(data.customerId
            ? {
                customerId2: data.customerId,
              }
            : {}),
          ...(data.emailHash
            ? {
                emailHash: data.emailHash,
              }
            : {}),
          hashedIps: newHashedIps,
        })
      } else {
        if (
          !existingClient.lastSeenAt ||
          now.getTime() - existingClient.lastSeenAt.getTime() > 1000 * 60 * 60 //1 hour
        ) {
          //Only updating lastSeenAt periodically if nothing else has changed
          clientUpdate = new this.clientModel({
            lastSeenAt: now,

            //For unknown reason, hashedIps will become empty if we don't include it in the partial update
            hashedIps: newHashedIps,
          })
        }
      }
    } else {
      this.logger.debug(
        {
          clientId,
        },
        `recordClient: creating new client record`
      )
      clientUpdate = new this.clientModel({
        _id: clientId,
        clientId,
        mergedClientId2: mergedClientId,
        createdAt: now,
        updatedAt: now,
        lastSeenAt: now,

        shop: data.shop,
        customerId2: data.customerId,
        emailHash: data.emailHash,

        hashedIps: data.hashedIp ? [data.hashedIp] : undefined,
      } satisfies Client)
    }

    if (clientUpdate) {
      await this.clientModel.findByIdAndUpdate(clientId, clientUpdate, {
        upsert: true,
      })
    }
  }

  async mergeAndSaveClient(
    clientId: string,
    data: {
      shop: string
      customerId?: string
      emailHash?: string
      hashedIp?: string
    }
  ): Promise<{
    mergedClientId: string
    mergedCustomer?: EventCustomer
  }> {
    //Merge with an existing older clientId if possible, and then create or update the client record.

    //TODO: use transaction to eliminate race condition?
    //so far it doesn't seem that the race condition is a big issue
    let mergedClientId = clientId

    const clientRecord = await this.clientModel.findById(clientId)
    if (clientRecord) {
      mergedClientId = clientRecord.mergedClientId2 ?? clientId
      data.customerId = data.customerId || clientRecord.customerId2
      data.emailHash = data.emailHash || clientRecord.emailHash
    }
    let mergedCustomerId = data.customerId
    let mergedEmailHash = data.emailHash

    if (
      clientId === mergedClientId &&
      data.emailHash &&
      !clientRecord?.emailHash
    ) {
      //This is the first time we see this client, or the client didn't have an email hash before, check whether it can be linked to a different existing clientId
      let leadClient: Client | undefined
      const clientWithSameEmail = await this.clientModel.findOne(
        {
          shop: data.shop,
          emailHash: data.emailHash,
        },
        undefined,
        {
          sort: {
            createdAt: 1,
          },
        }
      )
      if (clientWithSameEmail && clientWithSameEmail.clientId !== clientId) {
        leadClient = clientWithSameEmail
      }

      if (leadClient && leadClient.clientId !== clientId) {
        //Found a different clientId to merge with
        mergedClientId = leadClient.clientId
        mergedCustomerId = mergedCustomerId || leadClient.customerId2
        mergedEmailHash = mergedEmailHash || leadClient.emailHash

        if (
          clientId !== leadClient.clientId &&
          (mergedCustomerId !== leadClient.customerId2 ||
            mergedEmailHash !== leadClient.emailHash)
        ) {
          //If the leadClient is a different client, save new customer info back to the lead client record if needed
          await this.recordClient(
            leadClient.clientId,
            leadClient.clientId,
            {
              shop: leadClient.shop,
              customerId: mergedCustomerId,
              emailHash: mergedEmailHash,
            },
            leadClient
          )
        }
      }
    } else if (clientId !== mergedClientId && data.customerId) {
      //There is a corner case where the lead client may not have a customerId
      //TODO: should we check and update the customerId in the lead client record?
      //For now, we don't do it to avoid extra DB calls
    }

    //Now record the client
    await this.recordClient(
      clientId,
      mergedClientId,
      {
        ...data,
        customerId: mergedCustomerId,
        emailHash: mergedEmailHash,
      },
      clientRecord
    )

    return {
      mergedClientId,
      mergedCustomer:
        mergedCustomerId || mergedEmailHash
          ? {
              customerId: mergedCustomerId,
              hash: mergedEmailHash,
            }
          : undefined,
    }
  }

  async getLastCheckoutTime(
    shop: string,
    clientId: string
  ): Promise<Date | undefined> {
    const lastCheckoutEvent = await this.eventModel.findOne(
      {
        clientId,
        shop,
        name: 'checkout_completed',
      },
      undefined,
      {
        sort: {
          serverTimestamp: -1,
        },
      }
    )
    return lastCheckoutEvent?.serverTimestamp
  }
}
