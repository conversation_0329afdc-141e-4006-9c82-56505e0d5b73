import { Exclude, Expose, Type } from 'class-transformer'
import {
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator'

export class EventCustomerDto {
  @IsOptional()
  @IsString()
  customerId?: string

  @IsOptional()
  @IsString()
  hash?: string
}

export class EventShadowDataDto {
  @IsOptional()
  @IsString()
  ipAddress?: string

  @IsOptional()
  @IsString()
  userAgent?: string
}

export class EventRequestDto {
  @IsOptional()
  @IsString()
  id?: string

  @IsNotEmpty()
  @IsString()
  clientId: string

  @IsNotEmpty()
  @IsString()
  eventSource: string

  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  shop: string

  @IsString()
  type: string

  @IsOptional()
  @IsDateString()
  timestamp?: string

  @IsOptional()
  context?: any

  @IsOptional()
  data?: any

  @IsOptional()
  customData?: any

  @IsOptional()
  @ValidateNested()
  @Type(() => EventCustomerDto)
  customer?: EventCustomerDto
}

export class EventsRequestDto {
  @IsNotEmpty()
  @IsString()
  type: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EventRequestDto)
  events: EventRequestDto[]

  @IsOptional()
  @ValidateNested()
  @Type(() => EventShadowDataDto)
  shadowData?: EventShadowDataDto
}

@Exclude()
export class EventResponseDto {
  @Expose()
  message: string
  @Expose()
  prediction?: boolean
}

export class RawEventRequestDto {
  name: string
  shop: string
  eventSource: string
  event: any
}

export class RawEventsRequestDto {
  @IsNotEmpty()
  @IsString()
  type: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RawEventRequestDto)
  events: RawEventRequestDto[]
}

@Exclude()
export class RawEventResponseDto {
  @Expose()
  message: string
}
