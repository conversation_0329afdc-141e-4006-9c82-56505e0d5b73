import { Exclude, Expose, Type } from 'class-transformer'
import { DocumentDto, PaginatedDataDto } from './common.dto'
import { PartialType } from '@nestjs/swagger'
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDate,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from 'class-validator'

export enum StoreCampaignStatus {
  active = 'active',
  disabled = 'disabled',
  notStarted = 'notStarted',
  ended = 'ended',
  unknown = 'unknown',
}

@Exclude()
export class StoreCampaignVariantDto {
  @Expose()
  @IsString()
  @IsNotEmpty()
  offerId: string

  @Expose()
  @IsInt()
  @Min(0)
  @Max(100)
  allocation: number //percentage
}

@Exclude()
export class StoreCampaignScheduleDto {
  @Expose()
  @IsDate()
  @Type(() => Date)
  start: Date

  @Expose()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  end?: Date
}

@Exclude()
export class StoreCampaignDto extends DocumentDto {
  @Expose()
  storeId: string

  @Expose()
  createdAt: Date

  @Expose()
  updatedAt: Date

  @Expose()
  name: string

  @Expose()
  enabled: boolean

  @Expose()
  schedule: StoreCampaignScheduleDto

  @Expose()
  variants: StoreCampaignVariantDto[]

  //Derived
  @Expose()
  status?: StoreCampaignStatus
}

export class PaginatedStoreCampaignsDto extends PaginatedDataDto(
  StoreCampaignDto
) {}

export class StoreCampaignCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsBoolean()
  enabled: boolean

  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => StoreCampaignScheduleDto)
  schedule: StoreCampaignScheduleDto

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => StoreCampaignVariantDto)
  variants: StoreCampaignVariantDto[]
}

export class StoreCampaignUpdateDto extends PartialType(
  StoreCampaignCreateDto
) {}
