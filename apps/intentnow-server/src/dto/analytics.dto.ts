import { Exclude, Expose, Transform } from 'class-transformer'
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional } from 'class-validator'

export enum ChartPeriod {
  last7Days = '7d',
  last30Days = '30d',
  last90Days = '90d',
}

export class ChartQueryDto {
  @IsNotEmpty()
  @IsEnum(ChartPeriod)
  period: ChartPeriod

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value?.toLowerCase() === 'true')
  skipCache?: boolean
}

@Exclude()
export class FunnelChartDto {
  @Expose()
  chartName: string

  @Expose()
  groups: {
    groupName: string
    funnel: {
      funnelStep: string
      count: number
      conversionRate?: number
    }[]
  }[]
}

@Exclude()
export class AggregatedEventChartDto {
  @Expose()
  chartName: string

  @Expose()
  unit: string

  @Expose()
  groups: {
    groupName: string
    aggregatedValues: {
      name: string
      value: number
    }[]
  }[]
}

export enum StoreChartType {
  userConversion = 'user-conversion',
  otfUserConversion = 'otf-user-conversion',
  userRevenue = 'user-revenue',
}

@Exclude()
export class ChartDto {
  @Expose()
  funnelChart?: FunnelChartDto

  @Expose()
  aggregatedEventChart?: AggregatedEventChartDto
}
