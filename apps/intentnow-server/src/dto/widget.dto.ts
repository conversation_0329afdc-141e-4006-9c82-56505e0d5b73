export class DialogContentDto {
  brandName?: string
  preTitle?: string
  title: string
  mainImage: string
  description: string
  footer: string
  mTitle?: string
}

export class DiscountContentDto {
  title: string
  code: string
  link?: string
  startsAt: Date
  endsAt: Date
}

export class Widget3ConfigDto {
  dialog: any
  teaser: any
}

export class Widget2ConfigDto {
  dialog: any
  teaser: any
}
