import { Type } from '@nestjs/common'
import { ApiProperty } from '@nestjs/swagger'
import { Expose, Type as TransformType } from 'class-transformer'
import { IsInt, IsOptional } from 'class-validator'

export class DocumentDto {
  @Expose()
  _id: string

  //MongoDB data version
  // @Expose()
  // __v?: number
}

export class CrudQueryDto {
  @IsOptional()
  @IsInt()
  @TransformType(() => Number)
  page?: number = 0

  @IsOptional()
  @IsInt()
  @TransformType(() => Number)
  limit?: number = 10

  @IsOptional()
  @ApiProperty({ example: 'sort=name,asc&sort=createdAt,desc' })
  sort?: string

  @IsOptional()
  @ApiProperty({ example: 'filter=field,eq,123' })
  filter?: string
}

export class PaginationMetaDto {
  count: number
  total: number
  page: number
  pageSize: number
  pageCount: number
}

export function PaginatedDataDto<T>(itemType: Type<T>) {
  class paginatedDataDto {
    @Expose()
    @ApiProperty({ type: () => itemType, isArray: true })
    data: T[]

    @Expose()
    @ApiProperty({ type: PaginationMetaDto })
    meta: PaginationMetaDto
  }

  return paginatedDataDto
}

export interface SortQuery {
  field: string
  order: 'asc' | 'desc'
}

export enum FilterOp {
  //TODO: add more operators
  eq = 'eq',
  contains = 'contains',
}

export interface FilterQuery {
  field: string
  op: FilterOp
  value: string
}

export interface CrudQuery {
  page?: number
  limit?: number
  sorts?: SortQuery[]
  filters?: FilterQuery[]
  ids?: string[]
}
