import { Exclude, Expose } from 'class-transformer'
import {
  DialogContentDto,
  DiscountContentDto,
  Widget2ConfigDto,
  Widget3ConfigDto,
} from './widget.dto'
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator'

export class GetDiscountRequestDto {
  @IsNotEmpty()
  @IsString()
  clientId: string

  @IsOptional()
  @IsBoolean()
  preview?: boolean

  @IsOptional()
  @IsNotEmpty()
  @IsString()
  previewWidgetId?: string
}

@Exclude()
export class GetDiscountResponseDto {
  @Expose()
  preview?: boolean

  @Expose()
  dialog?: DialogContentDto

  @Expose()
  discount?: DiscountContentDto

  @Expose()
  widgetId?: string

  @Expose()
  widget2?: Widget2ConfigDto

  @Expose()
  widget3?: Widget3ConfigDto
}

@Expose()
export class WebPixelDto {
  id: string
  settings: any
}

@Exclude()
export class CreateWebPixelResponseDto {
  @Expose()
  webPixel: WebPixelDto
}

@Exclude()
export class DeleteWebPixelResponseDto {
  @Expose()
  deletedWebPixelId: string
}

export class ShopifyMetafieldDto {
  id: string
  namespace: string
  key: string
  value: string
}

export class MerchantPortalDto {
  linkedStoreId?: string
}

@Exclude()
export class ShopifyAppSettingsResponseDto {
  @Expose()
  appHandle: string

  @Expose()
  webPixel?: WebPixelDto

  @Expose()
  promoEmbedActivationLink?: string

  @Expose()
  promoEmbedActivated?: boolean

  @Expose()
  metafields?: ShopifyMetafieldDto[]

  @Expose()
  merchantPortal?: MerchantPortalDto
}

@Exclude()
export class StoreAnalyticsSettingsDto {
  @Expose()
  chartIds: string[]

  // internalDashboards?: {
  //   name: string
  //   link: string
  // }[]
}

export class PageInfoDto {
  count: number
  totalCount?: number
  first?: string
  last?: string
  hasNext: boolean
  hasPrev: boolean
  pageSize: number
}

export class GeneratedDiscountStatusDto {
  syncedAt: Date
  status: string
  usageCount: number
}

export class GeneratedDiscountDto {
  _id: string
  createdAt: Date
  shop: string
  clientId: string
  discountId: string
  discountId2?: string
  discount: DiscountContentDto
  discountStatus?: GeneratedDiscountStatusDto
}

@Exclude()
export class GeneratedDiscountsPageDto {
  @Expose()
  data: GeneratedDiscountDto[]

  @Expose()
  pageInfo: PageInfoDto
}
