import { Exclude, Expose } from 'class-transformer'
import { DocumentDto, PaginatedDataDto } from './common.dto'
import { IsNotEmpty, IsString } from 'class-validator'
import { PartialType } from '@nestjs/swagger'

@Exclude()
export class ShopifyAppDto extends DocumentDto {
  @Expose()
  createdAt: Date

  @Expose()
  name: string

  @Expose()
  appHandle: string

  @Expose()
  themeAppId: string

  @Expose()
  clientId: string

  @Expose()
  clientSecret: string

  @Expose()
  promoEmbedId: string
}

export class ShopifyAppCreateDto {
  @IsNotEmpty()
  @IsString()
  name: string

  @IsNotEmpty()
  @IsString()
  appHandle: string

  @IsNotEmpty()
  @IsString()
  themeAppId: string

  @IsNotEmpty()
  @IsString()
  clientId: string

  @IsNotEmpty()
  @IsString()
  clientSecret: string

  @IsNotEmpty()
  @IsString()
  promoEmbedId: string
}

export class ShopifyAppUpdateDto extends PartialType(ShopifyAppCreateDto) {}

export class PaginatedShopifyAppsDto extends PaginatedDataDto(ShopifyAppDto) {}
