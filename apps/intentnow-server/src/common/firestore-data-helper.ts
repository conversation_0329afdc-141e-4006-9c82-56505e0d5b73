import { Firestore, Query } from '@google-cloud/firestore'
import { BadRequestException } from '@nestjs/common'
import { DataPageInfo, DocumentEntity } from '@packages/shared-entities'

export async function pagedFirestoreDataReader<T extends DocumentEntity>(
  firestore: Firestore,
  collectionPath: string,
  baseQuery:
    | FirebaseFirestore.CollectionReference
    | Query<FirebaseFirestore.DocumentData, FirebaseFirestore.DocumentData>,
  pageSize: number,
  startAfter: string | undefined,
  endBefore: string | undefined,
  countTotal: boolean = false
): Promise<{
  data: T[]
  pageInfo: DataPageInfo
}> {
  if (startAfter && endBefore) {
    throw new BadRequestException(`startAfter and endBefore can't both be used`)
  }

  let totalCount: number | undefined
  if (countTotal) {
    totalCount = (await baseQuery.count().get()).data().count
  }

  let query: Query | undefined
  if (startAfter) {
    const startAfterRef = await firestore
      .collection(collectionPath)
      .doc(startAfter)
      .get()
    if (startAfterRef.exists) {
      query = baseQuery.startAfter(startAfterRef).limit(pageSize + 1)
    } else {
      startAfter = undefined
      query = baseQuery.limit(pageSize + 1)
    }
  } else if (endBefore) {
    if (endBefore === '__LAST__') {
      //A special endBefore value to load the last page
      query = baseQuery.limitToLast(pageSize + 1)
    } else {
      const endBeforeRef = await firestore
        .collection(collectionPath)
        .doc(endBefore)
        .get()
      if (endBeforeRef.exists) {
        query = baseQuery.endBefore(endBeforeRef).limitToLast(pageSize + 1)
      } else {
        endBefore = undefined
        query = baseQuery.limit(pageSize + 1)
      }
    }
  } else {
    query = baseQuery.limit(pageSize + 1)
  }

  let data = (await query.get()).docs.map((x) => {
    const entity = {
      ...x.data(),
      _id: x.id,
    } as T
    return entity
  })

  let hasPrev = Boolean(startAfter)
  let hasNext = Boolean(endBefore && endBefore !== '__LAST__')

  if (data.length > pageSize) {
    if (startAfter) {
      hasNext = true
      data = data.slice(0, pageSize)
    } else if (endBefore) {
      hasPrev = true
      data = data.slice(1, pageSize + 1)
    } else {
      hasNext = true
      data = data.slice(0, pageSize)
    }
  }

  return {
    data,
    pageInfo: {
      count: data.length,
      totalCount,
      first: data.length ? data[0]._id : undefined,
      last: data.length ? data[data.length - 1]._id : undefined,
      hasNext,
      hasPrev,
      pageSize,
    },
  }
}
