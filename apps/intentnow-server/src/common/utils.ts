import { Timestamp } from '@google-cloud/firestore'
import { createHash } from 'crypto'

export function mapTimestampFieldsToDate(obj: any, fields: string[]) {
  for (const field of fields) {
    if (obj[field]) {
      obj[field] = (obj[field] as any as Timestamp).toDate()
    }
  }
}

const globalSalt = '26d6498b-02e5-45cd-b252-c96ee9a69812'
export function hashSHA256ToNumber(text: string, salt: string) {
  const textToHash = `${globalSalt}-${salt}-${text}`
  const hash = createHash('sha256').update(textToHash).digest('hex')
  const partialHash = hash.slice(0, 8) //32bit number
  const hashNumber = parseInt(partialHash, 16)
  return hashNumber
}

export function randomHash(length: number = 8): string {
  return crypto.randomUUID().replace(/-/g, '').substring(0, length)
}
