import { buildMongoUpdate, parseCrudQueryFunc } from './data-helper'
import { ExecutionContext, BadRequestException } from '@nestjs/common'
import { FilterOp } from 'src/dto/common.dto'

describe('buildMongoUpdate tests', () => {
  it('should build a valid mongo update', () => {
    let update = buildMongoUpdate({
      name: 'test name',
      description: 'test desc',
    })
    expect(update).toEqual({
      name: 'test name',
      description: 'test desc',
      $unset: {},
    })

    update = buildMongoUpdate(
      {
        storeId: 'store-123',
        name: 'test name',
        description: null,
        nested: {
          field0: 'value0',
          field1: 'value1',
          field2: null,
        },
        nonNested: {
          field3: 'value3',
        },
      },
      {
        nested: {},
      },
      {
        storeId: {},
        nested: {
          field0: {},
        },
      }
    )
    expect(update).toEqual({
      name: 'test name',
      'nested.field1': 'value1',
      nonNested: {
        field3: 'value3',
      },
      $unset: {
        description: true,
        'nested.field2': true,
      },
    })
  })
})

describe('ParsedCrudQuery tests', () => {
  // Helper function to create mock ExecutionContext
  const createMockExecutionContext = (query: any): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({
          query,
        }),
      }),
    } as ExecutionContext
  }

  describe('Basic parsing', () => {
    it('should parse empty query parameters', () => {
      const ctx = createMockExecutionContext({})
      const result = parseCrudQueryFunc({}, ctx)

      expect(result).toEqual({
        page: undefined,
        limit: undefined,
        sorts: undefined,
        filters: undefined,
      })
    })

    it('should parse page and limit parameters', () => {
      const ctx = createMockExecutionContext({
        page: '2',
        limit: '20',
      })
      const result = parseCrudQueryFunc({}, ctx)

      expect(result).toEqual({
        page: 2,
        limit: 20,
        sorts: undefined,
        filters: undefined,
      })
    })

    it('should handle invalid page and limit parameters', () => {
      const ctx = createMockExecutionContext({
        page: 'invalid',
        limit: 'invalid',
      })
      const result = parseCrudQueryFunc({}, ctx)

      expect(result).toEqual({
        page: NaN,
        limit: NaN,
        sorts: undefined,
        filters: undefined,
      })
    })
  })

  describe('Sort parsing', () => {
    it('should parse single sort parameter with default order', () => {
      const ctx = createMockExecutionContext({
        sort: 'name',
      })
      const result = parseCrudQueryFunc(
        { sortFields: ['name', 'createdAt'] },
        ctx
      )

      expect(result.sorts).toEqual([
        {
          field: 'name',
          order: 'asc',
        },
      ])
    })

    it('should parse single sort parameter with explicit order', () => {
      const ctx = createMockExecutionContext({
        sort: 'name,desc',
      })
      const result = parseCrudQueryFunc(
        { sortFields: ['name', 'createdAt'] },
        ctx
      )

      expect(result.sorts).toEqual([
        {
          field: 'name',
          order: 'desc',
        },
      ])
    })

    it('should parse multiple sort parameters', () => {
      const ctx = createMockExecutionContext({
        sort: ['name,asc', 'createdAt,desc'],
      })
      const result = parseCrudQueryFunc(
        { sortFields: ['name', 'createdAt'] },
        ctx
      )

      expect(result.sorts).toEqual([
        {
          field: 'name',
          order: 'asc',
        },
        {
          field: 'createdAt',
          order: 'desc',
        },
      ])
    })

    it('should handle case insensitive sort order', () => {
      const ctx = createMockExecutionContext({
        sort: 'name,DESC',
      })
      const result = parseCrudQueryFunc({ sortFields: ['name'] }, ctx)

      expect(result.sorts).toEqual([
        {
          field: 'name',
          order: 'desc',
        },
      ])
    })

    it('should throw BadRequestException for invalid sort field', () => {
      const ctx = createMockExecutionContext({
        sort: 'invalidField',
      })

      expect(() => {
        parseCrudQueryFunc({ sortFields: ['name', 'createdAt'] }, ctx)
      }).toThrow(BadRequestException)
      expect(() => {
        parseCrudQueryFunc({ sortFields: ['name', 'createdAt'] }, ctx)
      }).toThrow('Invalid sort field: invalidField')
    })

    it('should throw BadRequestException for invalid sort order', () => {
      const ctx = createMockExecutionContext({
        sort: 'name,invalid',
      })

      expect(() => {
        parseCrudQueryFunc({ sortFields: ['name'] }, ctx)
      }).toThrow(BadRequestException)
      expect(() => {
        parseCrudQueryFunc({ sortFields: ['name'] }, ctx)
      }).toThrow('Invalid sort order: invalid')
    })

    it('should not parse sorts when sortFields is not provided', () => {
      const ctx = createMockExecutionContext({
        sort: 'name',
      })
      const result = parseCrudQueryFunc({}, ctx)

      expect(result.sorts).toBeUndefined()
    })

    it('should not parse sorts when sortFields is empty', () => {
      const ctx = createMockExecutionContext({
        sort: 'name',
      })
      const result = parseCrudQueryFunc({ sortFields: [] }, ctx)

      expect(result.sorts).toBeUndefined()
    })
  })

  describe('Filter parsing', () => {
    it('should parse single filter parameter', () => {
      const ctx = createMockExecutionContext({
        filter: 'name,eq,testValue',
      })
      const result = parseCrudQueryFunc(
        { filterFields: ['name', 'status'] },
        ctx
      )

      expect(result.filters).toEqual([
        {
          field: 'name',
          op: FilterOp.eq,
          value: 'testValue',
        },
      ])
    })

    it('should parse multiple filter parameters', () => {
      const ctx = createMockExecutionContext({
        filter: ['name,eq,testValue', 'status,eq,active'],
      })
      const result = parseCrudQueryFunc(
        { filterFields: ['name', 'status'] },
        ctx
      )

      expect(result.filters).toEqual([
        {
          field: 'name',
          op: FilterOp.eq,
          value: 'testValue',
        },
        {
          field: 'status',
          op: FilterOp.eq,
          value: 'active',
        },
      ])
    })

    it('should throw BadRequestException for invalid filter field', () => {
      const ctx = createMockExecutionContext({
        filter: 'invalidField,eq,value',
      })

      expect(() => {
        parseCrudQueryFunc({ filterFields: ['name', 'status'] }, ctx)
      }).toThrow(BadRequestException)
      expect(() => {
        parseCrudQueryFunc({ filterFields: ['name', 'status'] }, ctx)
      }).toThrow('Invalid filter field: invalidField')
    })

    it('should throw BadRequestException for invalid filter operator', () => {
      const ctx = createMockExecutionContext({
        filter: 'name,invalidOp,value',
      })

      expect(() => {
        parseCrudQueryFunc({ filterFields: ['name'] }, ctx)
      }).toThrow(BadRequestException)
      expect(() => {
        parseCrudQueryFunc({ filterFields: ['name'] }, ctx)
      }).toThrow('Invalid filter op: invalidOp')
    })

    it('should not parse filters when filterFields is not provided', () => {
      const ctx = createMockExecutionContext({
        filter: 'name,eq,value',
      })
      const result = parseCrudQueryFunc({}, ctx)

      expect(result.filters).toBeUndefined()
    })

    it('should not parse filters when filterFields is empty', () => {
      const ctx = createMockExecutionContext({
        filter: 'name,eq,value',
      })
      const result = parseCrudQueryFunc({ filterFields: [] }, ctx)

      expect(result.filters).toBeUndefined()
    })
  })

  describe('Combined parsing', () => {
    it('should parse page, limit, sorts, and filters together', () => {
      const ctx = createMockExecutionContext({
        page: '3',
        limit: '25',
        sort: ['name,asc', 'createdAt,desc'],
        filter: ['status,eq,active', 'name,eq,test'],
      })
      const result = parseCrudQueryFunc(
        {
          sortFields: ['name', 'createdAt'],
          filterFields: ['status', 'name'],
        },
        ctx
      )

      expect(result).toEqual({
        page: 3,
        limit: 25,
        sorts: [
          { field: 'name', order: 'asc' },
          { field: 'createdAt', order: 'desc' },
        ],
        filters: [
          { field: 'status', op: FilterOp.eq, value: 'active' },
          { field: 'name', op: FilterOp.eq, value: 'test' },
        ],
      })
    })

    it('should handle mixed valid and undefined parameters', () => {
      const ctx = createMockExecutionContext({
        page: '1',
        sort: 'name,asc',
        // no limit or filter
      })
      const result = parseCrudQueryFunc(
        {
          sortFields: ['name'],
          filterFields: ['status'],
        },
        ctx
      )

      expect(result).toEqual({
        page: 1,
        limit: undefined,
        sorts: [{ field: 'name', order: 'asc' }],
        filters: undefined,
      })
    })
  })

  describe('Edge cases', () => {
    it('should handle empty string values', () => {
      const ctx = createMockExecutionContext({
        page: '',
        limit: '',
      })
      const result = parseCrudQueryFunc({}, ctx)

      expect(result).toEqual({
        sorts: undefined,
        filters: undefined,
      })
    })

    it('should handle zero values', () => {
      const ctx = createMockExecutionContext({
        page: '0',
        limit: '0',
      })
      const result = parseCrudQueryFunc({}, ctx)

      expect(result).toEqual({
        page: 0,
        limit: 0,
        sorts: undefined,
        filters: undefined,
      })
    })

    it('should handle filter with empty value', () => {
      const ctx = createMockExecutionContext({
        filter: 'name,eq,',
      })
      const result = parseCrudQueryFunc({ filterFields: ['name'] }, ctx)

      expect(result.filters).toEqual([
        {
          field: 'name',
          op: FilterOp.eq,
          value: '',
        },
      ])
    })
  })
})
