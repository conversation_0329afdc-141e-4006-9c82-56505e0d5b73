import { Logger } from '@nestjs/common'
import { Counter, Histogram, ValueType } from '@opentelemetry/api'
import { MetricService } from 'nestjs-otel'

let fetchCounter: Counter | undefined
let fetchDuration: Histogram | undefined

export function initializeApiFetchMetrics(metricService: MetricService) {
  if (!fetchCounter) {
    fetchCounter = metricService.getCounter('api_fetch_count', {
      prefix: 'intentnow_server',
    })
    fetchDuration = metricService.getHistogram(
      'api_fetch_duration_milliseconds',
      {
        prefix: 'intentnow_server',
        unit: 'ms',
        valueType: ValueType.INT,
      }
    )
  }
}

export type ApiFetchError = Error & {
  response?: Response
}

export function getApiFetch(
  baseUrl: string,
  authToken?: string,
  logger?: Logger,
  defaultHeaders?: Record<string, string>,
  metricTag?: string
) {
  const apiFetch = async <DT = any>(
    apiUrl: string,
    init?: RequestInit,
    jsonRes = true
  ) => {
    const startTime = Date.now()
    const headers: any = {
      ...defaultHeaders,
      ...init?.headers,
      ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),
      'Content-Type': 'application/json',
    }

    let fetchUrl
    if (apiUrl.startsWith('http')) {
      //absolute URL
      fetchUrl = apiUrl
    } else {
      //relative URL
      fetchUrl = baseUrl + apiUrl
    }

    let success = false
    try {
      const response = await fetch(fetchUrl, { ...init, headers })

      let resBody = undefined
      if (jsonRes) {
        try {
          resBody = await response.json()
        } catch (e) {
          logger?.warn(
            e,
            {
              status: response.status,
            },
            'parse response json error'
          )
        }
      } else {
        try {
          resBody = await response.text()
        } catch (e) {
          logger?.warn(
            e,
            {
              status: response.status,
            },
            'parse response text error'
          )
        }
      }

      if (!response.ok) {
        logger?.error(
          {
            status: response.status,
            resBody,
          },
          'API fetch error'
        )
        const error = new Error(`API fetch error, status=${response.status}`)
        throw error
      }
      //logger?.log({ status: response.status, body: resBody }, 'API fetch success')
      success = true
      return resBody as DT
    } finally {
      const endTime = Date.now()
      const duration = endTime - startTime
      const attributes = {
        baseUrl,
        method: init?.method ?? 'GET',
        success,
        tag: metricTag,
      }
      fetchCounter && fetchCounter.add(1, attributes)
      fetchDuration && fetchDuration.record(duration, attributes)
    }
  }

  const apiPostFetch = async <DT = any, BT = any>(
    [apiUrl, body]: [string, BT],
    init?: RequestInit,
    jsonRes = true
  ) => {
    return await apiFetch<DT>(
      apiUrl ?? null,
      {
        ...init,
        method: 'POST',
        body: JSON.stringify(body ?? {}),
      },
      jsonRes
    )
  }

  const apiPutFetch = async <DT = any, BT = any>(
    [apiUrl, body]: [string, BT],
    init?: RequestInit,
    jsonRes = true
  ) => {
    return await apiFetch<DT>(
      apiUrl ?? null,
      {
        ...init,
        method: 'PUT',
        body: JSON.stringify(body ?? {}),
      },
      jsonRes
    )
  }

  return {
    apiFetch,
    apiPostFetch,
    apiPutFetch,
  }
}
