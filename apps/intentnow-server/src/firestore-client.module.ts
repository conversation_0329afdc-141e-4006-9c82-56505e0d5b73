import { Firestore } from '@google-cloud/firestore'
import { Global, Module, ModuleMetadata, Provider } from '@nestjs/common'

//A simple module to provide a shared Firestore client
@Global()
@Module({ providers: [], exports: [] })
export class FirestoreClientModule {
  static forRootAsync(
    options: {
      name?: string
      useFactory?: (...args: any[]) => { database?: string }
      inject?: any[]
    } & Pick<ModuleMetadata, 'imports'>
  ) {
    const firestoreClientModuleOptions = {
      provide: 'FIRESTORE_CLIENT_OPTIONS',
      useFactory: options.useFactory,
      inject: options.inject || [],
    }

    const firestoreClientProvider = {
      provide: 'FIRESTORE_CLIENT',
      useFactory: (config: { database?: string }) => {
        const firestore = new Firestore({
          ignoreUndefinedProperties: true,
          databaseId: config.database,
        })
        return firestore
      },
      inject: ['FIRESTORE_CLIENT_OPTIONS'],
    }

    return {
      module: FirestoreClientModule,
      imports: options.imports,
      providers: [
        firestoreClientModuleOptions as Provider,
        firestoreClientProvider,
      ],
      exports: [firestoreClientProvider],
    }
  }
}
