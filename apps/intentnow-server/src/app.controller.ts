import { Controller, Get, Req, UseGuards } from '@nestjs/common'
import { AppService } from './app.service'
import { ApiAuthGuard } from './auth/api-auth-guard'
import { UserRequest } from './auth/entities/user-context'

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('hello')
  getHello() {
    return this.appService.getHello()
  }

  @Get('api/hello')
  getHello2() {
    return this.appService.getHello()
  }

  @UseGuards(ApiAuthGuard)
  @Get('private-hello')
  getPrivateHello(@Req() request: UserRequest) {
    return this.appService.getHello(request.user)
  }

  @UseGuards(ApiAuthGuard)
  @Get('api/private-hello')
  getPrivateHello2(@Req() request: UserRequest) {
    return this.appService.getHello(request.user)
  }
}
