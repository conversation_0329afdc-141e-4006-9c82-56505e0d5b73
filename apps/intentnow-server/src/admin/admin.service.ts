import { Firestore } from '@google-cloud/firestore'
import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { validateIsAdmin } from 'src/auth/api-auth-guard'
import { UserContext } from 'src/auth/entities/user-context'

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name)

  constructor() {}

  async copyFirestoreDocument(
    requester: UserContext | undefined,
    source: {
      database: string
      collection: string
      id: string
    },
    target: {
      database: string
      collection?: string
      id?: string
    }
  ) {
    requester = validateIsAdmin(requester)

    const sourceFirestore = new Firestore({
      ignoreUndefinedProperties: true,
      databaseId: source.database,
    })
    const targetFirestore = new Firestore({
      ignoreUndefinedProperties: true,
      databaseId: target.database,
    })

    const sourceDoc = await sourceFirestore
      .collection(source.collection)
      .doc(source.id)
      .get()

    if (!sourceDoc.exists) {
      throw new NotFoundException('Source document not found')
    }

    const targetDoc = await targetFirestore
      .collection(target.collection || source.collection)
      .doc(target.id || source.id)
      .get()

    if (targetDoc.exists) {
      throw new BadRequestException('Target doc already exists')
    }

    const data = {
      ...sourceDoc.data(),
    }

    await targetFirestore
      .collection(target.collection || source.collection)
      .doc(target.id || source.id)
      .set(data)

    return {
      source,
      target,
      data,
    }
  }
}
