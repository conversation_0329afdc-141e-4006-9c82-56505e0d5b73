import { Module } from '@nestjs/common'
import { AdminService } from './admin.service'
import { AdminController } from './admin.controller'
import { ShopifyModule } from 'src/shopify/shopify.module'
import { AuthModule } from 'src/auth/auth.module'
import { IntentnowModule } from 'src/intentnow/intentnow.module'
import { ClsModule } from 'nestjs-cls'
import { StorefrontsModule } from 'src/storefronts/storefronts.module'
import { StoresModule } from 'src/stores/stores.module'

@Module({
  imports: [
    ShopifyModule,
    AuthModule,
    IntentnowModule,
    StoresModule,
    StorefrontsModule,
    ClsModule,
  ],
  providers: [AdminService],
  controllers: [AdminController],
  exports: [AdminService],
})
export class AdminModule {}
