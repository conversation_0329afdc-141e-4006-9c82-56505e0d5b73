import {
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { UserRequest } from 'src/auth/entities/user-context'
import {
  ShopifyStore,
  ShopifyStoreWidget,
  StoreLaunchConfig,
  WidgetType,
} from '@packages/shared-entities'
import { GetDiscountService } from 'src/intentnow/get-discount.service'
import { ShopifyService } from 'src/shopify/shopify.service'
import { AdminService } from './admin.service'
import { StoreConfigService } from 'src/intentnow/store-config.service'
import { FileInterceptor } from '@nestjs/platform-express'
import { StorefrontsService } from 'src/storefronts/storefronts.service'
import { ShopifyStoresService } from 'src/stores/shopify-stores.service'

@Controller('api/admin/intentnow')
export class AdminController {
  private readonly logger = new Logger(AdminController.name)

  constructor(
    private readonly adminService: AdminService,
    private readonly shopifyService: ShopifyService,
    private readonly getDiscountService: GetDiscountService,
    private readonly storeConfigService: StoreConfigService,
    private readonly storefrontsService: StorefrontsService,
    private readonly shopifyStoreService: ShopifyStoresService,
    private readonly apiAuthGuard: ApiAuthGuard
  ) {}

  @UseGuards(ApiAuthGuard)
  @Get('store-configs')
  async getStoreConfigs(@Req() request: UserRequest) {
    return this.storeConfigService.getAllStoreConfigs(request.user)
  }

  @UseGuards(ApiAuthGuard)
  @Get('store-configs/:shop')
  async getStoreConfigById(
    @Req() request: UserRequest,
    @Param('shop') shop: string
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, shop)
    return this.storeConfigService.getStoreConfigByShop(request.user, true)
  }

  @UseGuards(ApiAuthGuard)
  @Post('update-store-config')
  @HttpCode(200)
  async updateStoreConfig(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      data: Partial<ShopifyStore>
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return this.storeConfigService.updateStoreConfig(request.user, body.data)
  }

  @UseGuards(ApiAuthGuard)
  @Post('create-store-widget')
  async createStoreWidget(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      widgetType: WidgetType
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return this.storeConfigService.createStoreWidget(
      request.user,
      body.widgetType
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('update-store-widget')
  @HttpCode(200)
  async updateStoreWidget(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      widgetId: string
      data: Partial<ShopifyStoreWidget>
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return this.storeConfigService.updateStoreWidget(
      request.user,
      body.widgetId,
      body.data
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('delete-store-widget')
  @HttpCode(200)
  async deleteStoreWidget(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      widgetId: string
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return this.storeConfigService.deleteStoreWidget(
      request.user,
      body.widgetId
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('duplicate-store-widget')
  async duplicateStoreWidget(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      fromWidgetId: string
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return this.storeConfigService.duplicateStoreWidget(
      request.user,
      body.fromWidgetId
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('migrate-widget2')
  @HttpCode(200)
  async migrateWidget2(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return this.storeConfigService.migrateWidget2(request.user)
  }

  @UseGuards(ApiAuthGuard)
  @Post('store-info')
  @HttpCode(200)
  async getStoreInfo(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    const shopInfo = await this.shopifyService.getShopInfo(request.user)

    return {
      shopInfo,
    }
  }

  @UseGuards(ApiAuthGuard)
  @Post('/generated-discounts')
  @HttpCode(200)
  async getDiscounts(
    @Req() request: UserRequest,
    @Query('startAfter') startAfter: string | undefined,
    @Query('endBefore') endBefore: string | undefined,
    @Query('pageSize') pageSize: string | undefined,
    @Body()
    body: {
      shop: string
    }
  ) {
    const pageSizeNum = +(pageSize || '0')
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return await this.getDiscountService.getGeneratedDiscounts(
      request.user,
      pageSizeNum,
      startAfter,
      endBefore
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('test-get-discount')
  @HttpCode(200)
  async testGetDiscount(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      clientId: string | undefined
      preview?: boolean
      previewWidgetId?: string
    }
  ) {
    //This is an admin endpoint for testing purposes only
    //Admin or robot user required
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return this.storefrontsService.getShopifyStorefrontOfferTop(
      body.shop,
      body.clientId,
      body.preview,
      body.previewWidgetId
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('app-settings')
  @HttpCode(200)
  async getAppStatus(
    @Req() request: UserRequest,
    @Body() body: { shop: string; update: boolean }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return await this.shopifyStoreService.getAppSettings(
      request.user,
      body.update
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('feature-settings')
  @HttpCode(200)
  async getFeatureSettings(
    @Req() request: UserRequest,
    @Body() body: { shop: string; update: boolean }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return await this.storeConfigService.getFeatureSettings(request.user)
  }

  @UseGuards(ApiAuthGuard)
  @Post('sync-discount-statuses')
  @HttpCode(200)
  async syncDiscountStatuses(
    @Req() request: UserRequest,
    @Body() body: { shop: string }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return await this.getDiscountService.syncShopifyDiscountStatuses(
      request.user
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('init-store')
  @HttpCode(200)
  async initializeStore(
    @Req() request: UserRequest,
    @Body()
    body: {
      appHandle: string
      shop: string
      name: string
      website: string
      fromShop?: string
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(
      request.user,
      body.shop,
      body.appHandle
    )
    return await this.storeConfigService.initializeStore(
      request.user,
      body.name,
      body.website,
      body.fromShop
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('generate-app-configs')
  @HttpCode(200)
  async generateAppConfigs(
    @Req() request: UserRequest,
    @Body()
    body: {
      newShopifyApp: {
        appHandle: string
        appName: string
        apiKey: string
        secretKey: string
        promoEmbedId?: string
      }
    }
  ) {
    const tomlFile = this.shopifyService.generateAppTomlFile(
      request.user,
      body.newShopifyApp
    )

    const appConfigs = this.shopifyService.generateShopifyAppConfigs(
      request.user,
      body.newShopifyApp
    )

    return {
      tomlFile,
      appConfigs,
    }
  }

  @UseGuards(ApiAuthGuard)
  @Post('copy-firestore-doc')
  @HttpCode(200)
  async copyFirestoreDoc(
    @Req() request: UserRequest,
    @Body()
    body: {
      source: {
        database: string
        collection: string
        id: string
      }
      target: {
        database: string
        collection?: string
        id?: string
      }
    }
  ) {
    return await this.adminService.copyFirestoreDocument(
      request.user,
      body.source,
      body.target
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('store-images/upload')
  @UseInterceptors(FileInterceptor('imageFile'))
  async uploadImage(
    @Req() request: UserRequest,
    @UploadedFile() imageFile: Express.Multer.File,
    @Body()
    body: {
      shop: string
      imageName?: string
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return await this.storeConfigService.uploadStoreImage(
      request.user,
      body.imageName,
      imageFile
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('store-images/list')
  @HttpCode(200)
  async getImages(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return await this.storeConfigService.getStoreImages(request.user)
  }

  @UseGuards(ApiAuthGuard)
  @Post('store-images/delete')
  @HttpCode(200)
  async deleteImage(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      imageId: string
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return await this.storeConfigService.deleteStoreImage(
      request.user,
      body.imageId
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('launch-configs/create')
  async createLaunchConfig(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      data: Omit<
        StoreLaunchConfig,
        '_id' | 'experimentKey' | 'storeId' | 'createdAt'
      >
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return await this.storeConfigService.createLaunchConfig(
      request.user,
      body.data
    )
  }

  @UseGuards(ApiAuthGuard)
  @Post('launch-configs/list')
  @HttpCode(200)
  async getLaunchConfigs(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)
    return await this.storeConfigService.getStoreLaunchConfigs(request.user)
  }

  @UseGuards(ApiAuthGuard)
  @Post('store-configs/active')
  @HttpCode(200)
  async getActiveLaunchConfig(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
    }
  ) {
    try {
      return await this.storeConfigService.getActiveLaunchConfig(
        body.shop,
        true
      )
    } catch (error) {
      this.logger.warn(
        error,
        {
          shop: body.shop,
        },
        `getActiveLaunchConfig: failed`
      )
      return {}
    }
  }
}
