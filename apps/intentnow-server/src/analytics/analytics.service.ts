import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { getApiFetch } from 'src/common/fetch'
import { ServerConfig } from 'src/config/config'
import {
  AmplitudeEventSegmentsQuery,
  AmplitudeEventSegmentsResponse,
  AmplitudeFunnelsQuery,
  AmplitudeFunnelsResponse,
} from './entities/amplitude-api'
import { MurLock } from 'murlock'

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name)
  private readonly amplitudeConfig: ServerConfig['amplitude']
  private readonly amplitudeApiFetch: ReturnType<typeof getApiFetch>['apiFetch']

  constructor(private readonly configService: ConfigService) {
    this.amplitudeConfig =
      this.configService.get<ServerConfig['amplitude']>('amplitude')!
    const { apiFetch } = this.getAmplitudeApiFetch()
    this.amplitudeApiFetch = apiFetch
  }

  private getAmplitudeApiFetch() {
    const username = this.amplitudeConfig.apiKey
    const password = this.amplitudeConfig.secretKey
    const token = Buffer.from(`${username}:${password}`).toString('base64')
    const authHeader = `Basic ${token}`
    return getApiFetch(this.amplitudeConfig.apiUrl, undefined, this.logger, {
      authorization: authHeader,
    })
  }

  dateString(date: Date) {
    const pstDate = new Date(date.getTime() - 1000 * 60 * 60 * 8)
    return pstDate.toISOString().split('T')[0].replaceAll('-', '')
  }

  buildAmplitudeFunnelsQueryString(query: AmplitudeFunnelsQuery) {
    let queryString = `start=${this.dateString(query.start)}&end=${this.dateString(query.end)}`
    query.events.forEach((event) => {
      queryString += `&e=${encodeURIComponent(JSON.stringify(event))}`
    })
    if (query.interval) {
      queryString += `&i=${encodeURIComponent(query.interval)}`
    }
    if (query.segmentBy) {
      queryString += `&s=${encodeURIComponent(JSON.stringify(query.segmentBy))}`
    }
    if (query.groupBy) {
      queryString += `&g=${encodeURIComponent(query.groupBy)}`
    }
    if (query.conversionWindowInSeconds) {
      queryString += `&cs=${query.conversionWindowInSeconds}`
    }
    if (query.limit) {
      queryString += `&limit=${query.limit}`
    }
    return queryString
  }

  buildAmplitudeEventSegmentsQueryString(query: AmplitudeEventSegmentsQuery) {
    let queryString = `start=${this.dateString(query.start)}&end=${this.dateString(query.end)}`
    if (query.event) {
      queryString += `&e=${encodeURIComponent(JSON.stringify(query.event))}`
    }
    if (query.event2) {
      queryString += `&e2=${encodeURIComponent(JSON.stringify(query.event2))}`
    }
    if (query.metric) {
      queryString += `&m=${encodeURIComponent(query.metric)}`
    }
    if (query.userType) {
      queryString += `&n=${encodeURIComponent(query.userType)}`
    }
    if (query.interval) {
      queryString += `&i=${encodeURIComponent(query.interval)}`
    }
    if (query.segmentBy) {
      queryString += `&s=${encodeURIComponent(JSON.stringify(query.segmentBy))}`
    }
    if (query.groupBy) {
      queryString += `&g=${encodeURIComponent(query.groupBy)}`
    }
    if (query.groupBy2) {
      queryString += `&g2=${encodeURIComponent(query.groupBy2)}`
    }
    if (query.formula) {
      queryString += `&formula=${encodeURIComponent(query.formula)}`
    }
    if (query.rollingWindow) {
      queryString += `&rollingWindow=${query.rollingWindow}`
    }
    if (query.rollingAverage) {
      queryString += `&rollingAverage=${query.rollingAverage}`
    }
    if (query.limit) {
      queryString += `&limit=${query.limit}`
    }
    return queryString
  }

  //Use a single Redis lock for all amplitude queries to work around the rate-limit
  @MurLock(60000, 'AnalyticsService.queryAmplitude')
  async getAmplitudeFunnels(query: AmplitudeFunnelsQuery) {
    this.logger.log('getAmplitudeFunnels: started')
    const queryString = this.buildAmplitudeFunnelsQueryString(query)
    const response = await this.amplitudeApiFetch<AmplitudeFunnelsResponse>(
      `/2/funnels?${queryString}`
    )
    this.logger.log('getAmplitudeFunnels: done')
    return response
  }

  //Use a single Redis lock for all amplitude queries to work around the rate-limit
  @MurLock(60000, 'AnalyticsService.queryAmplitude')
  async getAmplitudeEventSegments(query: AmplitudeEventSegmentsQuery) {
    this.logger.log('getAmplitudeEventSegments: started')
    const queryString = this.buildAmplitudeEventSegmentsQueryString(query)
    const response =
      await this.amplitudeApiFetch<AmplitudeEventSegmentsResponse>(
        `/2/events/segmentation?${queryString}`
      )
    this.logger.log('getAmplitudeEventSegments: done')
    return response
  }
}
