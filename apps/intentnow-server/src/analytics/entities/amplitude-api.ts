export enum AmplitudePropOp {
  //is, is not, contains, does not contain, less, less or equal, greater, greater or equal, set is, or set is not
  is = 'is',
  isNot = 'is not',
  contains = 'contains',
  doesNotContain = 'does not contain',
  less = 'less',
  lessOrEqual = 'less or equal',
  greater = 'greater',
  greaterOrEqual = 'greater or equal',
  setIs = 'set is',
  setIsNot = 'set is not',
}

export enum AmplitudePropType {
  event = 'event',
  user = 'user',
}

export interface AmplitudeEventParam {
  event_type: string
  filters?: {
    subprop_type: AmplitudePropType
    subprop_key: string
    subprop_op: AmplitudePropOp
    subprop_value: string[]
  }[]
  group_by?: {
    type: AmplitudePropType
    value: string
  }[]
}

export interface AmplitudeSegmentParam {
  prop: string
  op: AmplitudePropOp
  values: string[]
}

export enum AmplitudeInterval {
  realtime = '-300000',
  hourly = '-3600000',
  daily = '1',
  weekly = '7',
  monthly = '30',
}

export enum AmplitudeMetric {
  // Non-property metrics: uniques, totals, pct_dau, or average. Defaults to uniques. Property metrics: histogram, sums, or value_avg
  uniques = 'uniques',
  totals = 'totals',
  pct_dau = 'pct_dau',
  average = 'average',
  histogram = 'histogram',
  sums = 'sums',
  value_avg = 'value_avg',
  formula = 'formula',
}

export enum AmplitudeUserType {
  any = 'any',
  active = 'active',
}

export interface AmplitudeFunnelsQuery {
  start: Date
  end: Date
  events: AmplitudeEventParam[] // "e="
  interval?: AmplitudeInterval // "i="
  segmentBy?: AmplitudeSegmentParam[] // "s="
  groupBy?: string // "g="
  conversionWindowInSeconds?: number // "cs="
  limit?: number
}

export interface AmplitudeFunnelsData {
  groupValue: string
  meta: {
    segmentIndex: number
  }
  dayMedianTransTimes: {
    series: number[][]
    xValues: string[]
    formattedXValues: string[]
  }
  dayAvgTransTimes: {
    series: number[][]
    xValues: string[]
    formattedXValues: string[]
  }
  stepByStep: number[]
  medianTransTimes: number[]
  cumulative: number[]
  cumulativeRaw: number[]
  avgTransTimes: number[]
  dayFunnels: {
    series: number[][]
    xValues: string[]
    formattedXValues: string[]
  }
  events: string[]
}

export interface AmplitudeFunnelsResponse {
  data: AmplitudeFunnelsData[]
}

export interface AmplitudeEventSegmentsQuery {
  start: Date
  end: Date
  event: AmplitudeEventParam // "e="
  event2?: AmplitudeEventParam // "e2="
  metric?: string // "m="
  userType?: string // "n="
  interval?: AmplitudeInterval // "i="
  segmentBy?: AmplitudeSegmentParam[] // "s="
  groupBy?: string // "g="
  groupBy2?: string // "g2="
  formula?: string
  rollingWindow?: number
  rollingAverage?: number
  limit?: number
}

export interface AmplitudeEventSegmentsData {
  series: number[][]
  seriesLabels: string[]
  seriesCollapsed: { value: number }[][]
  xValues: string[]
}

export interface AmplitudeEventSegmentsResponse {
  data: AmplitudeEventSegmentsData
}
