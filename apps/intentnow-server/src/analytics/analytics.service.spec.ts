import { Test, TestingModule } from '@nestjs/testing'
import { AnalyticsService } from './analytics.service'
import { ConfigService } from '@nestjs/config'
import {
  AmplitudeEventSegmentsQuery,
  AmplitudeFunnelsQuery,
  AmplitudeInterval,
  AmplitudeMetric,
  AmplitudePropOp,
  AmplitudePropType,
  AmplitudeUserType,
} from './entities/amplitude-api'

describe('AnalyticsService', () => {
  let service: AnalyticsService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AnalyticsService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            amplitude: {
              apiUrl: 'https://amplitude.com/api',
              apiKey: 'test',
              secretKey: 'test',
            },
          }
        }
        return {}
      })
      .compile()

    service = module.get<AnalyticsService>(AnalyticsService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })

  it('should build a valid funnels query string', () => {
    let query: AmplitudeFunnelsQuery = {
      events: [
        {
          event_type: '[Shopify] page_viewed',
          filters: [],
          group_by: [],
        },
      ],
      start: new Date('2025-07-08'),
      end: new Date('2025-07-15'),
    }
    let queryString = decodeURIComponent(
      service.buildAmplitudeFunnelsQueryString(query)
    )
    expect(queryString).toEqual(
      'start=20250707&end=20250714&e={"event_type":"[Shopify] page_viewed","filters":[],"group_by":[]}'
    )

    query = {
      events: [
        {
          event_type: '[Shopify] page_viewed',
          filters: [
            {
              subprop_type: AmplitudePropType.event,
              subprop_key: 'shop',
              subprop_op: AmplitudePropOp.is,
              subprop_value: ['gourmandbeauty-com.myshopify.com'],
            },
            {
              subprop_type: AmplitudePropType.user,
              subprop_key: 'gp:split_model-exp',
              subprop_op: AmplitudePropOp.is,
              subprop_value: ['control', 'test'],
            },
          ],
        },
        {
          event_type: '[Shopify] checkout_completed',
        },
      ],
      start: new Date('2025-07-08'),
      end: new Date('2025-07-15'),
      interval: AmplitudeInterval.daily,
      groupBy: 'gp:split_model-exp',
      conversionWindowInSeconds: 3600,
      limit: 100,
    }

    queryString = decodeURIComponent(
      service.buildAmplitudeFunnelsQueryString(query)
    )
    expect(queryString).toEqual(
      'start=20250707&end=20250714&e={"event_type":"[Shopify] page_viewed","filters":[{"subprop_type":"event","subprop_key":"shop","subprop_op":"is","subprop_value":["gourmandbeauty-com.myshopify.com"]},{"subprop_type":"user","subprop_key":"gp:split_model-exp","subprop_op":"is","subprop_value":["control","test"]}]}&e={"event_type":"[Shopify] checkout_completed"}&i=1&g=gp:split_model-exp&cs=3600&limit=100'
    )
  })

  it('should build a valid event segments query string', () => {
    let query: AmplitudeEventSegmentsQuery = {
      start: new Date('2025-07-08'),
      end: new Date('2025-07-15'),
      event: {
        event_type: '[Shopify] page_viewed',
        filters: [],
        group_by: [],
      },
    }
    let queryString = decodeURIComponent(
      service.buildAmplitudeEventSegmentsQueryString(query)
    )
    expect(queryString).toEqual(
      'start=20250707&end=20250714&e={"event_type":"[Shopify] page_viewed","filters":[],"group_by":[]}'
    )

    query = {
      start: new Date('2025-07-08'),
      end: new Date('2025-07-15'),
      event: {
        event_type: '[Shopify] page_viewed',
        filters: [
          {
            subprop_type: AmplitudePropType.event,
            subprop_key: 'shop',
            subprop_op: AmplitudePropOp.is,
            subprop_value: ['gourmandbeauty-com.myshopify.com'],
          },
          {
            subprop_type: AmplitudePropType.user,
            subprop_key: 'gp:split_model-exp',
            subprop_op: AmplitudePropOp.is,
            subprop_value: ['control', 'test'],
          },
        ],
        group_by: [],
      },
      event2: {
        event_type: '[Shopify] checkout_completed',
        filters: [],
        group_by: [],
      },
      metric: AmplitudeMetric.uniques,
      userType: AmplitudeUserType.active,
      interval: AmplitudeInterval.daily,
      groupBy: 'gp:split_model-exp',
      groupBy2: 'shop',
      formula: 'e1 / e2',
      rollingWindow: 7,
      rollingAverage: 30,
      limit: 100,
    }

    queryString = decodeURIComponent(
      service.buildAmplitudeEventSegmentsQueryString(query)
    )

    expect(queryString).toEqual(
      'start=20250707&end=20250714&e={"event_type":"[Shopify] page_viewed","filters":[{"subprop_type":"event","subprop_key":"shop","subprop_op":"is","subprop_value":["gourmandbeauty-com.myshopify.com"]},{"subprop_type":"user","subprop_key":"gp:split_model-exp","subprop_op":"is","subprop_value":["control","test"]}],"group_by":[]}&e2={"event_type":"[Shopify] checkout_completed","filters":[],"group_by":[]}&m=uniques&n=active&i=1&g=gp:split_model-exp&g2=shop&formula=e1 / e2&rollingWindow=7&rollingAverage=30&limit=100'
    )
  })
})
