import { Module } from '@nestjs/common'
import { StoresController } from './stores.controller'
import { AuthModule } from 'src/auth/auth.module'
import { StoresService } from './stores.service'
import {
  LinkStoreRequest,
  LinkStoreRequestSchema,
  Store,
  StoreGeneratedDiscount,
  StoreGeneratedDiscountSchema,
  StoreSchema,
} from './entities/store.mongo'
import { MongooseModule } from '@nestjs/mongoose'
import { ClsModule } from 'nestjs-cls'
import { UsersModule } from 'src/users/users.module'
import { AnalyticsModule } from 'src/analytics/analytics.module'
import { StoreCampaignsService } from './store-campaigns.service'
import { StoreCampaign, StoreCampaignSchema } from './entities/campaign.mongo'
import { StoreCampaignsController } from './store-campaigns.controller'
import { StoreOffer, StoreOfferSchema } from './entities/offer.mongo'
import { StoreOffersService } from './store-offers.service'
import { StoreOffersController } from './store-offers.controller'
import { ShopifyModule } from 'src/shopify/shopify.module'
import { ShopifyStoresService } from './shopify-stores.service'
import { StorageModule, DriverType } from '@codebrew/nestjs-storage'
import { NestjsFormDataModule } from 'nestjs-form-data'
import { ConfigService } from '@nestjs/config'
import { ServerConfig } from 'src/config/config'
import { StoreImage, StoreImageSchema } from './entities/image.mongo'
import {
  StoreLaunchConfig,
  StoreLaunchConfigSchema,
} from './entities/launch-config.mongo'
import { BillingModule } from 'src/billing/billing.module'

@Module({
  imports: [
    AuthModule,
    UsersModule,
    AnalyticsModule,
    ShopifyModule,
    BillingModule,
    ClsModule,
    MongooseModule.forFeature([
      {
        name: Store.name,
        collection: 'stores',
        schema: StoreSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: LinkStoreRequest.name,
        collection: 'link_store_requests',
        schema: LinkStoreRequestSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: StoreCampaign.name,
        collection: 'store_campaigns',
        schema: StoreCampaignSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: StoreOffer.name,
        collection: 'store_offers',
        schema: StoreOfferSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: StoreImage.name,
        collection: 'store_images',
        schema: StoreImageSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: StoreLaunchConfig.name,
        collection: 'store_launchconfigs',
        schema: StoreLaunchConfigSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: StoreGeneratedDiscount.name,
        collection: 'store_generateddiscounts',
        schema: StoreGeneratedDiscountSchema,
      },
    ]),
    UsersModule,
    NestjsFormDataModule,
    StorageModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const cdnBucket =
          configService.get<ServerConfig['intentnow']>('intentnow')
            ?.imageStorage.cdnBucket
        return {
          default: 'gcs',
          disks: {
            gcs: {
              driver: DriverType.GCS,
              config: {
                bucket: cdnBucket,
              },
            },
          },
        }
      },
    }),
  ],
  controllers: [
    StoresController,
    StoreCampaignsController,
    StoreOffersController,
  ],
  providers: [
    StoresService,
    StoreCampaignsService,
    StoreOffersService,
    ShopifyStoresService,
  ],
  exports: [
    StoresService,
    StoreCampaignsService,
    StoreOffersService,
    ShopifyStoresService,
  ],
})
export class StoresModule {}
