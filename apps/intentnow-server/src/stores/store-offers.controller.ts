import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common'
import { StoreOffersService } from './store-offers.service'
import { CrudQuery, CrudQueryDto } from 'src/dto/common.dto'
import { ParsedCrudQuery } from 'src/common/data-helper'
import { StoreOfferCreateDto, StoreOfferUpdateDto } from 'src/dto/offer.dto'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'

@UseGuards(ApiAuthGuard)
@Controller('api/intentnow')
export class StoreOffersController {
  constructor(private readonly offersService: StoreOffersService) {}

  @Get('stores/:storeId/offers')
  async getStoreOffers(
    @Param('storeId') storeId: string,
    @Query() q: CrudQueryDto, //Keep this for swagger,
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name', 'status'],
      filterFields: ['status'],
      getMany: true,
    })
    query: CrudQuery
  ) {
    return await this.offersService.getStoreOffers(storeId, query)
  }

  @Get('stores/:storeId/offers/:offerId')
  async getStoreOffer(
    @Param('storeId') storeId: string,
    @Param('offerId') offerId: string
  ) {
    return await this.offersService.getStoreOffer(storeId, offerId)
  }

  @Post('stores/:storeId/Offers')
  async createStoreOffer(
    @Param('storeId') storeId: string,
    @Body() dto: StoreOfferCreateDto
  ) {
    return await this.offersService.createStoreOffer(storeId, dto)
  }

  @Patch('stores/:storeId/Offers/:offerId')
  async updateStoreOffer(
    @Param('storeId') storeId: string,
    @Param('offerId') offerId: string,
    @Body() dto: StoreOfferUpdateDto
  ) {
    return await this.offersService.updateStoreOffer(storeId, offerId, dto)
  }

  @Delete('stores/:storeId/offers/:offerId')
  async deleteStoreOffer(
    @Param('storeId') storeId: string,
    @Param('offerId') offerId: string
  ) {
    return await this.offersService.deleteStoreOffer(storeId, offerId)
  }
}
