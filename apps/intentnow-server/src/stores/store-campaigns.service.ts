import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { StoresService } from 'src/stores/stores.service'
import { StoreCampaign, StoreCampaignDocument } from './entities/campaign.mongo'
import { ClientSession, Connection, PaginateModel } from 'mongoose'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { UserPermissions } from 'src/users/user-permissions'
import { CrudQuery } from 'src/dto/common.dto'
import {
  buildMongoQuery,
  buildMongoUpdate,
  mapPaginatedMongoResultsToDto,
} from 'src/common/data-helper'
import {
  PaginatedStoreCampaignsDto,
  StoreCampaignCreateDto,
  StoreCampaignDto,
  StoreCampaignStatus,
  StoreCampaignUpdateDto,
} from 'src/dto/campaign.dto'
import { plainToInstance } from 'class-transformer'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { ServerConfig } from 'src/config/config'
import { StoreOfferDto } from 'src/dto/offer.dto'
import { StoreOffersService } from './store-offers.service'

@Injectable()
export class StoreCampaignsService {
  private readonly logger = new Logger(StoreCampaignsService.name)
  private readonly appName: string

  constructor(
    private readonly configService: ConfigService,
    private readonly userPermissions: UserPermissions,
    private readonly storeService: StoresService,
    private readonly storeOffersService: StoreOffersService,
    @InjectConnection() private mongoConnection: Connection,
    @InjectModel(StoreCampaign.name)
    private readonly storeCampaignModel: PaginateModel<StoreCampaignDocument>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache
  ) {
    this.appName = configService.get<ServerConfig['app']>('app')!.name
  }

  async getStoreCampaign(
    storeId: string,
    campaignId: string
  ): Promise<StoreCampaignDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const storeCampaign = await this.storeCampaignModel.findById(campaignId)
    if (!storeCampaign || storeCampaign.storeId !== storeId) {
      throw new NotFoundException('StoreCampaign not found')
    }

    const campaign = plainToInstance(StoreCampaignDto, storeCampaign.toObject())
    await this.decorateCampaignStatuses(storeId, [campaign])
    return campaign
  }

  async getStoreCampaigns(
    storeId: string,
    crudQuery: CrudQuery
  ): Promise<PaginatedStoreCampaignsDto> {
    if (crudQuery.ids) {
      const campaigns = await this.getManyStoreCampaigns(storeId, crudQuery.ids)
      return {
        data: campaigns,
        meta: {
          count: campaigns.length,
          total: campaigns.length,
          page: 1,
          pageSize: campaigns.length,
          pageCount: 1,
        },
      }
    }

    await this.userPermissions.requiresAccessOnStore(storeId)

    const { filter, options } = buildMongoQuery(
      this.storeCampaignModel,
      crudQuery
    )
    const paginatedResults = await this.storeCampaignModel.paginate(
      {
        ...filter,
        storeId: {
          $eq: storeId,
        },
      },
      options
    )

    const campaigns = mapPaginatedMongoResultsToDto(
      paginatedResults,
      StoreCampaignDto,
      PaginatedStoreCampaignsDto
    )
    await this.decorateCampaignStatuses(storeId, campaigns.data)
    return campaigns
  }

  async getManyStoreCampaigns(storeId: string, ids: string[]) {
    await this.userPermissions.requiresAccessOnStore(storeId)
    const findResults = await this.storeCampaignModel.find({
      _id: { $in: ids },
    })

    const campaigns = findResults.map((x) =>
      plainToInstance(StoreCampaignDto, x.toObject())
    )
    await this.decorateCampaignStatuses(storeId, campaigns)
    return campaigns
  }

  async createStoreCampaign(
    storeId: string,
    newStoreCampaign: StoreCampaignCreateDto
  ): Promise<StoreCampaignDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const now = new Date()
    const created = await this.mongoConnection.transaction(async (session) => {
      await this.validateCampaign(
        storeId,
        {
          schedule: newStoreCampaign.schedule,
          enabled: newStoreCampaign.enabled,
        },
        session
      )
      const store = new this.storeCampaignModel({
        storeId,
        createdAt: now,
        updatedAt: now,
        name: newStoreCampaign.name,
        schedule: newStoreCampaign.schedule,
        variants: newStoreCampaign.variants,
        enabled: newStoreCampaign.enabled,
      } satisfies StoreCampaign)
      const created = await store.save({ session })
      return created
    })

    return plainToInstance(StoreCampaignDto, created.toObject())
  }

  async updateStoreCampaign(
    storeId: string,
    campaignId: string,
    updateDto: StoreCampaignUpdateDto
  ): Promise<StoreCampaignDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const updateObj = buildMongoUpdate(
      updateDto,
      {},
      {
        storeId: {},
      }
    )

    const storeCampaign = await this.mongoConnection.transaction(
      async (session) => {
        const existingCampaign = await this.storeCampaignModel.findById(
          campaignId,
          null,
          { session }
        )
        if (!existingCampaign || existingCampaign.storeId !== storeId) {
          throw new NotFoundException('StoreCampaign not found')
        }
        await this.validateCampaign(
          storeId,
          {
            _id: campaignId,
            schedule: updateDto.schedule ?? existingCampaign.schedule,
            enabled: updateDto.enabled ?? existingCampaign.enabled,
          },
          session
        )

        const storeCampaign = await this.storeCampaignModel.findByIdAndUpdate(
          campaignId,
          {
            ...updateObj,
            updatedAt: new Date(),
          },
          {
            new: true,
            session,
          }
        )
        return storeCampaign
      }
    )

    if (!storeCampaign) {
      throw new NotFoundException('StoreCampaign not found')
    }
    return plainToInstance(StoreCampaignDto, storeCampaign.toObject())
  }

  async deleteStoreCampaign(storeId: string, campaignId: string) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    //Validate storeId/campaignId
    await this.getStoreCampaign(storeId, campaignId)

    const deleted = await this.storeCampaignModel.findByIdAndDelete(campaignId)
    if (!deleted) {
      throw new NotFoundException('StoreCampaign not found')
    }
    return plainToInstance(StoreCampaignDto, deleted.toObject())
  }

  async getActiveCampaign(
    storeId: string
  ): Promise<StoreCampaignDto | undefined> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    const now = new Date()
    const storeCampaign = (
      await this.storeCampaignModel.paginate(
        {
          storeId: {
            $eq: storeId,
          },
          enabled: {
            $eq: true,
          },
          ['schedule.start']: { $lte: now },
          $or: [
            { ['schedule.end']: { $eq: null } },
            { ['schedule.end']: { $exists: false } },
            { ['schedule.end']: { $gte: now } },
          ],
        },
        {
          //In case there may be more than one active campaign, return the latest one
          sort: { 'schedule.start': -1, createdAt: -1, updatedAt: -1 },
          limit: 1,
        }
      )
    )?.docs[0]
    if (!storeCampaign) {
      return undefined
    }
    const campaign = plainToInstance(StoreCampaignDto, storeCampaign.toObject())
    campaign.status = StoreCampaignStatus.active

    return campaign
  }

  async decorateCampaignStatuses(
    storeId: string,
    campaigns: StoreCampaignDto[]
  ) {
    const now = new Date()
    const activeCampaign = await this.getActiveCampaign(storeId)
    campaigns.forEach((campaign) => {
      if (campaign._id === activeCampaign?._id) {
        campaign.status = StoreCampaignStatus.active
      } else if (!campaign.enabled) {
        campaign.status = StoreCampaignStatus.disabled
      } else if (campaign.schedule && campaign.schedule.start > now) {
        campaign.status = StoreCampaignStatus.notStarted
      } else if (campaign.schedule?.end && campaign.schedule.end < now) {
        campaign.status = StoreCampaignStatus.ended
      } else {
        //A unlikely case (due to rare edge condition)
        this.logger.error(
          {
            storeId,
            campaignId: campaign._id,
          },
          'decorateCampaignStatuses: unknown status'
        )
        campaign.status = StoreCampaignStatus.unknown
      }
    })
  }

  async validateCampaign(
    storeId: string,
    campaign: {
      _id?: string
      schedule: {
        start: Date
        end?: Date
      }
      enabled: boolean
    },
    session?: ClientSession | undefined
  ) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    if (
      campaign.schedule.end &&
      campaign.schedule.start > campaign.schedule.end
    ) {
      throw new BadRequestException('invalid-start-end')
    }

    //Now check if there is conflicting campaigns
    if (!campaign.enabled) {
      return undefined
    }

    const periodFilters: any[] = []
    if (campaign.schedule.end) {
      periodFilters.push({
        ['schedule.start']: {
          $gte: campaign.schedule.start,
          $lte: campaign.schedule.end,
        },
      })
      periodFilters.push({
        ['schedule.end']: {
          $gte: campaign.schedule.start,
          $lte: campaign.schedule.end,
        },
      })
      periodFilters.push({
        ['schedule.start']: { $lte: campaign.schedule.end },
        $or: [
          { ['schedule.end']: { $exists: false } },
          { ['schedule.end']: { $eq: null } },
        ],
      })
    } else {
      periodFilters.push({
        ['schedule.end']: { $exists: false },
      })
      periodFilters.push({
        ['schedule.end']: { $eq: null },
      })
      periodFilters.push({
        ['schedule.end']: { $gte: campaign.schedule.start },
      })
    }

    const conflictingCampaigns = await this.storeCampaignModel.paginate(
      {
        storeId: {
          $eq: storeId,
        },
        ...(campaign._id
          ? {
              _id: {
                $ne: campaign._id,
              },
            }
          : {}),
        enabled: {
          $eq: true,
        },
        $or: periodFilters,
      },
      {
        limit: 1,
        session,
      }
    )
    if (conflictingCampaigns.docs.length > 0) {
      throw new BadRequestException('conflicting-campaign')
    }
  }

  async getActiveCampaignData(
    storeId: string,
    skipCache?: boolean
  ): Promise<
    | {
        activeCampaign: StoreCampaignDto
        buckets: {
          allocation: number
          variant?: {
            variantIndex: number
            offer: StoreOfferDto
          }
        }[]
      }
    | undefined
  > {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const cacheKey = `intentnow-activeStoreCampaignData:${this.appName}:${storeId}`
    if (!skipCache) {
      const cachedData = await this.cacheManager.get<{
        data?: ReturnType<
          typeof StoreCampaignsService.prototype.getActiveCampaignData
        >
      }>(cacheKey)
      if (cachedData) {
        return cachedData.data
      }
    }

    const activeCampaign = await this.getActiveCampaign(storeId)
    let controlAllocation = 100
    let buckets: {
      allocation: number
      variant?: {
        variantIndex: number
        offer: StoreOfferDto
      }
    }[] = activeCampaign
      ? await Promise.all(
          activeCampaign.variants.map(async (variant, index) => {
            const offer = await this.storeOffersService.getStoreOffer(
              storeId,
              variant.offerId
            )
            controlAllocation -= variant.allocation
            return {
              allocation: variant.allocation,
              variant: {
                variantIndex: index,
                offer,
              },
            }
          })
        )
      : []

    if (controlAllocation < 0) {
      this.logger.error(
        {
          storeId,
          activeCampaignId: activeCampaign?._id,
          controlAllocation,
        },
        'getActiveCampaignData: control allocation is negative'
      )
      throw new InternalServerErrorException('control allocation is negative')
    }

    buckets = [
      {
        allocation: controlAllocation,
      },
      ...buckets,
    ]

    const data = activeCampaign
      ? {
          activeCampaign,
          buckets,
        }
      : undefined

    await this.cacheManager.set(cacheKey, { data }, 1000 * 60) //cache for 1 minute
    return data
  }
}
