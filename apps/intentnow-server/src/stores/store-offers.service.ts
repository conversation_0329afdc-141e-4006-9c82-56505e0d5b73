import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { StoresService } from 'src/stores/stores.service'
import { StoreOffer, StoreOfferDocument } from './entities/offer.mongo'
import { Connection, PaginateModel } from 'mongoose'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { UserPermissions } from 'src/users/user-permissions'
import { CrudQuery } from 'src/dto/common.dto'
import {
  buildMongoQuery,
  buildMongoUpdate,
  mapPaginatedMongoResultsToDto,
} from 'src/common/data-helper'
import {
  OfferDiscountCodeConfigDto,
  PaginatedStoreOffersDto,
  StoreOfferCreateDto,
  StoreOfferDto,
  StoreOfferUpdateDto,
} from 'src/dto/offer.dto'
import { plainToInstance } from 'class-transformer'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { ServerConfig } from 'src/config/config'

@Injectable()
export class StoreOffersService {
  private readonly logger = new Logger(StoreOffersService.name)
  private readonly appName: string

  constructor(
    private readonly configService: ConfigService,
    private readonly userPermissions: UserPermissions,
    private readonly storeService: StoresService,
    @InjectConnection() private mongoConnection: Connection,
    @InjectModel(StoreOffer.name)
    private readonly storeOfferModel: PaginateModel<StoreOfferDocument>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache
  ) {
    this.appName = configService.get<ServerConfig['app']>('app')!.name
  }

  async getStoreOffer(
    storeId: string,
    offerId: string
  ): Promise<StoreOfferDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const storeOffer = await this.storeOfferModel.findById(offerId)
    if (!storeOffer || storeOffer.storeId !== storeId) {
      throw new NotFoundException('StoreOffer not found')
    }

    const offer = plainToInstance(StoreOfferDto, storeOffer.toObject())
    return offer
  }

  async getStoreOffers(
    storeId: string,
    crudQuery: CrudQuery
  ): Promise<PaginatedStoreOffersDto> {
    if (crudQuery.ids) {
      const offers = await this.getManyStoreOffers(storeId, crudQuery.ids)
      return {
        data: offers,
        meta: {
          count: offers.length,
          total: offers.length,
          page: 1,
          pageSize: offers.length,
          pageCount: 1,
        },
      }
    }

    await this.userPermissions.requiresAccessOnStore(storeId)

    const { filter, options } = buildMongoQuery(this.storeOfferModel, crudQuery)
    const paginatedResults = await this.storeOfferModel.paginate(
      {
        ...filter,
        storeId: {
          $eq: storeId,
        },
      },
      options
    )

    const offers = mapPaginatedMongoResultsToDto(
      paginatedResults,
      StoreOfferDto,
      PaginatedStoreOffersDto
    )
    return offers
  }

  async getManyStoreOffers(storeId: string, ids: string[]) {
    await this.userPermissions.requiresAccessOnStore(storeId)
    const findResults = await this.storeOfferModel.find({
      _id: { $in: ids },
    })

    const campaigns = findResults.map((x) =>
      plainToInstance(StoreOfferDto, x.toObject())
    )

    return campaigns
  }

  private adjustDiscountTitle(discountConfig?: OfferDiscountCodeConfigDto) {
    if (discountConfig) {
      if (discountConfig.discount.type === 'percent') {
        discountConfig.discount.title = `${discountConfig.discount.percentOff}% Off`
      } else if (discountConfig.discount.type === 'amount') {
        discountConfig.discount.title = `$${discountConfig.discount.amountOff} Off`
      } else {
        discountConfig.discount.title = ''
      }
    }
  }

  async createStoreOffer(
    storeId: string,
    newStoreOffer: StoreOfferCreateDto
  ): Promise<StoreOfferDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    this.adjustDiscountTitle(newStoreOffer.discountConfig)

    const status =
      newStoreOffer.name &&
      newStoreOffer.widgetConfig &&
      newStoreOffer.discountConfig
        ? 'complete'
        : 'draft'

    const now = new Date()
    const newOffer = {
      storeId,
      createdAt: now,
      updatedAt: now,
      name: newStoreOffer.name,
      status,
      widgetConfig: newStoreOffer.widgetConfig,
      discountConfig: newStoreOffer.discountConfig,
    } satisfies StoreOffer
    const created = await this.storeOfferModel.create(newOffer)
    return plainToInstance(StoreOfferDto, created.toObject())
  }

  async updateStoreOffer(
    storeId: string,
    offerId: string,
    updateDto: StoreOfferUpdateDto
  ): Promise<StoreOfferDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)
    await this.storeService.getStore(storeId)

    //Validate storeId/offerId
    await this.getStoreOffer(storeId, offerId)

    this.adjustDiscountTitle(updateDto.discountConfig)

    const updateObj = buildMongoUpdate(
      updateDto,
      {
        widgetConfig: {},
      },
      {
        storeId: {},
      }
    )

    let storeOffer = await this.storeOfferModel.findByIdAndUpdate(
      offerId,
      {
        ...updateObj,
        updatedAt: new Date(),
      },
      {
        new: true,
      }
    )
    if (!storeOffer) {
      throw new NotFoundException('StoreOffer not found')
    }

    const newStatus =
      storeOffer.name && storeOffer.widgetConfig && storeOffer.discountConfig
        ? 'complete'
        : 'draft'
    if (storeOffer.status !== newStatus) {
      storeOffer = await this.storeOfferModel.findByIdAndUpdate(
        offerId,
        {
          status: newStatus,
        },
        {
          new: true,
        }
      )
      if (!storeOffer) {
        throw new NotFoundException('StoreOffer not found')
      }
    }

    return plainToInstance(StoreOfferDto, storeOffer.toObject())
  }

  async deleteStoreOffer(storeId: string, offerId: string) {
    await this.userPermissions.requiresAccessOnStore(storeId)

    //Validate storeId/offerId
    await this.getStoreOffer(storeId, offerId)

    const deleted = await this.storeOfferModel.findByIdAndDelete(offerId)
    if (!deleted) {
      throw new NotFoundException('StoreOffer not found')
    }
    return plainToInstance(StoreOfferDto, deleted.toObject())
  }
}
