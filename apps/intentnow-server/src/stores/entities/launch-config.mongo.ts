import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'
import * as paginate from 'mongoose-paginate-v2'
import { mongooseSchemaOptions } from 'src/common/data-helper'

export class ModelConfigV1 {
  @Prop()
  model: string

  @Prop()
  floor: number

  @Prop()
  ceiling: number

  @Prop()
  start: number

  @Prop()
  end: number
}

export class StoreLaunchConfigVariant {
  @Prop()
  modelConfigV1?: ModelConfigV1
}

@Schema(mongooseSchemaOptions)
export class StoreLaunchConfig {
  @Prop()
  storeId: string

  @Prop()
  createdAt: Date

  @Prop()
  name: string

  @Prop({
    type: [StoreLaunchConfigVariant],
  })
  configVariants: StoreLaunchConfigVariant[]
}

export type StoreLaunchConfigDocument = HydratedDocument<StoreLaunchConfig>
export const StoreLaunchConfigSchema =
  SchemaFactory.createForClass(StoreLaunchConfig)
StoreLaunchConfigSchema.plugin(paginate)
