import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'
import * as paginate from 'mongoose-paginate-v2'
import { mongooseSchemaOptions } from 'src/common/data-helper'

export class StoreCampaignVariant {
  @Prop()
  offerId: string

  @Prop()
  allocation: number //percentage
}

export class StoreCampaignSchedule {
  @Prop()
  start: Date

  @Prop()
  end?: Date
}

@Schema(mongooseSchemaOptions)
export class StoreCampaign {
  @Prop()
  storeId: string

  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  name: string

  @Prop()
  enabled: boolean

  @Prop({
    type: StoreCampaignSchedule,
  })
  schedule: StoreCampaignSchedule

  @Prop({
    type: [StoreCampaignVariant],
  })
  variants: StoreCampaignVariant[]
}

export type StoreCampaignDocument = HydratedDocument<StoreCampaign>
export const StoreCampaignSchema = SchemaFactory.createForClass(StoreCampaign)
StoreCampaignSchema.plugin(paginate)
