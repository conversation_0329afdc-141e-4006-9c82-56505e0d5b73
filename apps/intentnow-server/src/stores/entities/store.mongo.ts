import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'
import * as paginate from 'mongoose-paginate-v2'
import { mongooseSchemaOptions } from 'src/common/data-helper'
import { OfferDiscountCodeConfig } from './offer.mongo'

export class StoreShopifyConfig {
  @Prop()
  myshopifyDomain: string

  @Prop()
  appHandle?: string
}

export class StoreConfig {
  @Prop()
  receiveEvents?: boolean

  @Prop()
  sendAmplitude?: boolean

  @Prop()
  showOffers?: boolean

  @Prop()
  predictOffers?: boolean

  @Prop()
  eventShadowRatio?: number

  @Prop()
  isLegacy?: boolean

  @Prop({ type: {} })
  modelOverrides?: any

  @Prop()
  stripeSubscriptionId?: string
}

@Schema(mongooseSchemaOptions)
export class Store {
  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  name: string

  @Prop()
  website: string

  @Prop({
    type: StoreConfig,
  })
  config?: StoreConfig

  @Prop({
    type: StoreShopifyConfig,
  })
  shopifyConfig?: StoreShopifyConfig
}

export type StoreDocument = HydratedDocument<Store>
export const StoreSchema = SchemaFactory.createForClass(Store)
StoreSchema.plugin(paginate)

export enum StoreWidgetType {
  widget2 = 'widget2',
  widget3 = 'widget3',
}

@Schema(mongooseSchemaOptions)
export class StoreWidget {
  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  storeId: string

  @Prop()
  name: string

  @Prop()
  type: StoreWidgetType

  // @Prop()
  // discount: DiscountConfig

  // @Prop()
  // widget2?: Widget2Config

  // @Prop()
  // widget3?: Widget3Config
}

export type StoreWidgetDocument = HydratedDocument<StoreWidget>
export const StoreWidgetSchema = SchemaFactory.createForClass(StoreWidget)
StoreWidgetSchema.plugin(paginate)

@Schema(mongooseSchemaOptions)
export class LinkStoreRequest {
  @Prop()
  status: 'pending' | 'completed' | 'expired' | 'failed'

  @Prop()
  storeType: 'shopify'

  @Prop()
  storeRef: string //For shopify store this is the myshopifydomain

  @Prop()
  storeName: string

  @Prop()
  storeWebsite: string

  @Prop()
  source: string //Fo shopify store this is the app handle

  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  expiresAt: Date
}

export type LinkStoreRequestDocument = HydratedDocument<LinkStoreRequest>
export const LinkStoreRequestSchema =
  SchemaFactory.createForClass(LinkStoreRequest)
LinkStoreRequestSchema.plugin(paginate)

export enum StoreGeneratedDiscountType {
  shopify = 'shopify',
}

export class GeneratedDiscountData {
  @Prop()
  storeRef: string //For shopify store this is the myshopifydomain

  @Prop()
  discountRef: string //For shopify store this is the "discountCodeBasicCreate.codeDiscountNode.id"

  @Prop({
    type: {},
  })
  discountPayload: any
}

export class GeneratedDiscountContent {
  @Prop()
  code: string

  @Prop()
  title: string

  @Prop()
  startsAt: Date

  @Prop()
  endsAt: Date
}

export class SourceDiscountConfig {
  @Prop()
  campaignId: string

  @Prop()
  offerId: string

  @Prop({
    type: OfferDiscountCodeConfig,
  })
  discountConfig: OfferDiscountCodeConfig
}

@Schema(mongooseSchemaOptions)
export class StoreGeneratedDiscount {
  @Prop()
  createdAt: Date

  @Prop()
  storeId: string

  @Prop()
  clientId: string

  @Prop({
    enum: StoreGeneratedDiscountType,
  })
  type: StoreGeneratedDiscountType

  @Prop({ type: SourceDiscountConfig })
  source?: SourceDiscountConfig

  @Prop({ type: GeneratedDiscountContent })
  discountContent?: GeneratedDiscountContent

  @Prop({
    type: GeneratedDiscountData,
  })
  discountData: GeneratedDiscountData
}

export type StoreGeneratedDiscountDocument =
  HydratedDocument<StoreGeneratedDiscount>
export const StoreGeneratedDiscountSchema = SchemaFactory.createForClass(
  StoreGeneratedDiscount
)
StoreGeneratedDiscountSchema.plugin(paginate)
