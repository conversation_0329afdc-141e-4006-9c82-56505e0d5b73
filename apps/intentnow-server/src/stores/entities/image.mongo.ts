import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'
import * as paginate from 'mongoose-paginate-v2'
import { mongooseSchemaOptions } from 'src/common/data-helper'

export class StoreImageFileInfo {
  @Prop()
  filename: string

  @Prop()
  mimetype: string

  @Prop()
  size: number

  @Prop()
  ext: string

  @Prop()
  format: string

  @Prop()
  width: number

  @Prop()
  height: number
}

export class StoreImageUploadInfo {
  @Prop()
  cdnBucket: string

  @Prop()
  path: string
}

@Schema(mongooseSchemaOptions)
export class StoreImage {
  @Prop()
  storeId: string

  @Prop()
  name: string

  @Prop()
  imageUrl: string

  @Prop()
  createdAt: Date

  @Prop({
    type: StoreImageFileInfo,
  })
  fileInfo: StoreImageFileInfo

  @Prop({
    type: StoreImageUploadInfo,
  })
  uploadInfo?: StoreImageUploadInfo
}

export type StoreImageDocument = HydratedDocument<StoreImage>
export const StoreImageSchema = SchemaFactory.createForClass(StoreImage)
StoreImageSchema.plugin(paginate)
