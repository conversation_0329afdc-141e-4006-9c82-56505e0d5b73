import { Test, TestingModule } from '@nestjs/testing'
import { StoreOffersService } from './store-offers.service'
import { MongoMemoryReplSet } from 'mongodb-memory-server'
import { MongoClient } from 'mongodb'
import { UserPermissions } from 'src/users/user-permissions'
import { MongooseModule } from '@nestjs/mongoose'
import { StoreOffer, StoreOfferSchema } from './entities/offer.mongo'
import { StoresService } from 'src/stores/stores.service'
import { UnauthorizedException } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { ConfigService } from '@nestjs/config'

describe('OffersService', () => {
  let service: StoreOffersService
  let mongoServer: MongoMemoryReplSet
  let mongoClient: MongoClient //use for insert initial test data

  beforeAll(async () => {
    //A replset is needed for the MongoDB transaction to work
    //mongoServer = await MongoMemoryServer.create()
    mongoServer = await MongoMemoryReplSet.create({ replSet: { count: 4 } })
    mongoClient = await MongoClient.connect(mongoServer.getUri(), {})

    //Insert initial test data into MongoDB here
    //...
  }, 60000)

  beforeEach(async () => {
    const cacheManagerMock = {
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
    }

    const module: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(mongoServer.getUri()),
        MongooseModule.forFeature([
          {
            name: StoreOffer.name,
            collection: 'store_offers',
            schema: StoreOfferSchema,
          },
        ]),
      ],
      providers: [StoreOffersService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            app: {
              name: 'test-app',
            },
          }
        } else if (token === UserPermissions) {
          return {
            requiresAccessOnStore: jest.fn(),
          }
        } else if (token === StoresService) {
          return {
            getStore: jest.fn(),
          }
        } else if (token === CACHE_MANAGER) {
          return cacheManagerMock
        }
        return {}
      })
      .compile()

    service = module.get<StoreOffersService>(StoreOffersService)
  })

  afterEach(async () => {
    await mongoClient.db().collection('store_offers').deleteMany({})
  })

  afterAll(async (): Promise<any> => {
    if (mongoClient) {
      await mongoClient.close()
    }
    if (mongoServer) {
      await mongoServer.stop()
    }
  })

  it('store offer crud operations', async () => {
    const storeId = 'test-store-id'

    let getOffersResults = await service.getStoreOffers(storeId, {})
    expect(getOffersResults.data.length).toBe(0)
    expect(getOffersResults.meta.total).toBe(0)

    const newStoreOffer = {
      name: 'test-offer',
    }

    const created = await service.createStoreOffer(storeId, newStoreOffer)
    expect(created).toBeDefined()
    expect(created.name).toBe(newStoreOffer.name)

    getOffersResults = await service.getStoreOffers(storeId, {})
    expect(getOffersResults.data.length).toBe(1)
    expect(getOffersResults.meta.total).toBe(1)
    expect(getOffersResults.data[0].name).toEqual(created.name)
    expect(getOffersResults.data[0].status).toBe('draft')

    await service.updateStoreOffer(storeId, created._id, {
      name: 'test-offer-updated',
    })

    const getOffer = await service.getStoreOffer(storeId, created._id)
    expect(getOffer.name).toBe('test-offer-updated')
    expect(getOffer.status).toBe('draft')

    await service.deleteStoreOffer(storeId, created._id)

    getOffersResults = await service.getStoreOffers(storeId, {})
    expect(getOffersResults.data.length).toBe(0)
    expect(getOffersResults.meta.total).toBe(0)
  }, 60000)

  it('store offer paginated list', async () => {
    const storeId = 'test-store-id'

    let getOffersResults = await service.getStoreOffers(storeId, {})
    expect(getOffersResults.data.length).toBe(0)
    expect(getOffersResults.meta.total).toBe(0)

    const newStoreOffer = {}

    await service.createStoreOffer(storeId, {
      ...newStoreOffer,
      name: 'offer-1',
    })
    await service.createStoreOffer(storeId, {
      ...newStoreOffer,
      name: 'offer-3',
    })
    await service.createStoreOffer(storeId, {
      ...newStoreOffer,
      name: 'offer-4',
    })
    await service.createStoreOffer(storeId, {
      ...newStoreOffer,
      name: 'offer-2',
    })
    await service.createStoreOffer(storeId, {
      ...newStoreOffer,
      name: 'offer-5',
    })
    //an offer that belongs to different store
    await service.createStoreOffer('other-store-id', {
      ...newStoreOffer,
      name: 'offer-6',
    })

    getOffersResults = await service.getStoreOffers(storeId, {
      limit: 2,
      sorts: [{ field: 'name', order: 'asc' }],
    })
    expect(getOffersResults.data.length).toBe(2)
    expect(getOffersResults.meta.total).toBe(5)
    expect(getOffersResults.meta.page).toBe(1)
    expect(getOffersResults.meta.pageCount).toBe(3)
    expect(getOffersResults.data[0].name).toBe('offer-1')
    expect(getOffersResults.data[1].name).toBe('offer-2')

    getOffersResults = await service.getStoreOffers(storeId, {
      limit: 3,
      page: 2,
      sorts: [{ field: 'name', order: 'asc' }],
    })
    expect(getOffersResults.data.length).toBe(2)
    expect(getOffersResults.meta.total).toBe(5)
    expect(getOffersResults.meta.page).toBe(2)
    expect(getOffersResults.meta.pageCount).toBe(2)
  })
})

describe('OffersService Access Control', () => {
  let service: StoreOffersService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [StoreOffersService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            app: {
              name: 'test-app',
            },
          }
        } else if (token === UserPermissions) {
          return {
            requiresAccessOnStore: () => {
              throw new UnauthorizedException('Unauthorized')
            },
          }
        } else if (token === StoresService) {
          return {
            getStore: jest.fn(),
          }
        }
        return {}
      })
      .compile()

    service = module.get<StoreOffersService>(StoreOffersService)
  })

  it('should throw unauthorized exception', async () => {
    await expect(service.getStoreOffers('test-store-id', {})).rejects.toThrow(
      'Unauthorized'
    )

    await expect(service.getStoreOffer('test-store-id', '123')).rejects.toThrow(
      'Unauthorized'
    )

    await expect(
      service.createStoreOffer('test-store-id', {
        name: 'test-offer',
      })
    ).rejects.toThrow('Unauthorized')

    await expect(
      service.updateStoreOffer('test-store-id', '123', {
        name: 'test-offer',
      })
    ).rejects.toThrow('Unauthorized')

    await expect(
      service.deleteStoreOffer('test-store-id', '123')
    ).rejects.toThrow('Unauthorized')
  })
})
