import { Injectable, Logger, NotFoundException } from '@nestjs/common'
import { PaginateModel } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import {
  User,
  UserDocument,
  UserStoreAccess,
  UserStoreAccessDocument,
} from './entities/user.mongo'
import { UserPermissions } from './user-permissions'
import {
  PaginatedPublicUsersDto,
  PaginatedUsersDto,
  PaginatedUserStoreAccessesDto,
  PublicUserDto,
  UserCreateDto,
  UserDto,
  UserStoreAccessCreateDto,
  UserStoreAccessDto,
  UserUpdateDto,
} from 'src/dto/user.dto'
import {
  mapPaginatedMongoResultsToDto,
  buildMongoUpdate,
  buildMongoQuery,
} from 'src/common/data-helper'
import { plainToInstance } from 'class-transformer'
import { CrudQuery, FilterOp } from 'src/dto/common.dto'

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name)

  constructor(
    @InjectModel(User.name)
    private readonly userModel: PaginateModel<UserDocument>,
    @InjectModel(UserStoreAccess.name)
    private readonly userStoreAccessModel: PaginateModel<UserStoreAccessDocument>,
    private readonly userPermissions: UserPermissions
  ) {}

  async getUser(userId: string): Promise<UserDto> {
    await this.userPermissions.requiresAccessOnUser(userId)

    const user = await this.userModel.findById(userId)
    if (!user || user.deletedAt) {
      throw new NotFoundException('User not found')
    }
    return plainToInstance(UserDto, user.toObject())
  }

  async getUsers(crudQuery: CrudQuery): Promise<PaginatedUsersDto> {
    this.userPermissions.requiresAdmin()

    const { filter, options } = buildMongoQuery(this.userModel, crudQuery)

    const paginatedResults = await this.userModel.paginate(
      {
        ...filter,
        deletedAt: {
          $exists: false,
        },
      },
      options
    )

    return mapPaginatedMongoResultsToDto(
      paginatedResults,
      UserDto,
      PaginatedUsersDto
    )
  }

  async updateUser(userId: string, updateDto: UserUpdateDto): Promise<UserDto> {
    await this.userPermissions.requiresAccessOnUser(userId)

    const updateObj = buildMongoUpdate(updateDto, {
      shopifyConfig: {},
    })

    const user = await this.userModel.findByIdAndUpdate(
      {
        userId,
        deletedAt: {
          $exists: false,
        },
      },
      {
        ...updateObj,
        updatedAt: new Date(),
      },
      {
        new: true,
      }
    )
    if (!user) {
      throw new NotFoundException('User not found')
    }
    return plainToInstance(UserDto, user.toObject())
  }

  async createUser(newUser: UserCreateDto): Promise<UserDto> {
    await this.userPermissions.requiresAccessOnUser(newUser._id)

    const now = new Date()
    const update = {
      $setOnInsert: {
        createdAt: now,
      },
      $set: {
        updatedAt: now,
        authType: newUser.authType,
        displayName: newUser.displayName,
        email: newUser.email,
      },
      $unset: {
        deletedAt: true,
      },
    }

    const user = await this.userModel.findOneAndUpdate(
      { _id: newUser._id },
      update,
      { new: true, upsert: true }
    )

    return plainToInstance(UserDto, user.toObject())
  }

  async deleteUser(userId: string) {
    this.userPermissions.requiresAdmin()

    const deleted = await this.userModel.findByIdAndDelete(userId)
    if (!deleted) {
      throw new NotFoundException('User not found')
    }
    return plainToInstance(UserDto, deleted.toObject())
  }

  async softDeleteUser(userId: string): Promise<UserDto> {
    await this.userPermissions.requiresAccessOnUser(userId)

    const now = new Date()
    const user = await this.userModel.findByIdAndUpdate(
      userId,
      {
        deletedAt: now,
        updatedAt: now,
      },
      {
        new: true,
      }
    )
    if (!user) {
      throw new NotFoundException('User not found')
    }
    return plainToInstance(UserDto, user.toObject())
  }

  async getUserStoreAccessesByUser(
    userId: string,
    crudQuery: CrudQuery
  ): Promise<PaginatedUserStoreAccessesDto> {
    await this.userPermissions.requiresAccessOnUser(userId)

    //Check if user exists
    //await this.getUser(userId)

    const { filter, options } = buildMongoQuery(
      this.userStoreAccessModel,
      crudQuery
    )
    const paginatedResults = await this.userStoreAccessModel.paginate(
      {
        ...filter,
        userId,
      },
      options
    )

    return mapPaginatedMongoResultsToDto(
      paginatedResults,
      UserStoreAccessDto,
      PaginatedUserStoreAccessesDto
    )
  }

  async getUsersByStore(
    storeId: string,
    crudQuery: CrudQuery
  ): Promise<PaginatedPublicUsersDto> {
    await this.userPermissions.requiresAccessOnStore(storeId)

    const { filter, options } = buildMongoQuery(
      this.userStoreAccessModel,
      crudQuery
    )
    const paginatedResults = await this.userStoreAccessModel.paginate(
      {
        ...filter,
        storeId,
      },
      options
    )
    const { meta } = mapPaginatedMongoResultsToDto(
      paginatedResults,
      UserStoreAccessDto,
      PaginatedUserStoreAccessesDto
    )
    const userIds = paginatedResults.docs.map((x) => x.userId)

    const paginatedUserResults = await this.userModel.paginate({
      _id: { $in: userIds },
    })

    const { data } = mapPaginatedMongoResultsToDto(
      paginatedUserResults,
      PublicUserDto,
      PaginatedPublicUsersDto
    )
    return {
      data,
      meta,
    }
  }

  async createUserStoreAccessByEmail(
    email: string,
    newUserStoreAccess: UserStoreAccessCreateDto
  ): Promise<UserStoreAccessDto> {
    this.userPermissions.requiresAdmin()

    //Find user
    const { data: users } = await this.getUsers({
      filters: [
        {
          field: 'email',
          op: FilterOp.eq,
          value: email,
        },
      ],
    })
    if (users.length <= 0) {
      throw new NotFoundException('User not found')
    }

    return await this.createUserStoreAccess(users[0]._id, newUserStoreAccess)
  }

  async createUserStoreAccess(
    userId: string,
    newUserStoreAccess: UserStoreAccessCreateDto
  ): Promise<UserStoreAccessDto> {
    this.userPermissions.requiresAdmin()

    //Check if user exists
    await this.getUser(userId)

    //Check if an entry already exists
    //TODO: do it in a transaction?
    const existing = await this.userStoreAccessModel.findOne({
      userId,
      storeId: newUserStoreAccess.storeId,
    })
    if (existing) {
      return plainToInstance(UserStoreAccessDto, existing.toObject())
    }

    const now = new Date()
    const userStoreAccess = new this.userStoreAccessModel({
      createdAt: now,
      userId: userId,
      storeId: newUserStoreAccess.storeId,
      grantedBy: newUserStoreAccess.grantedBy,
    } satisfies UserStoreAccess)
    const created = await userStoreAccess.save()
    return plainToInstance(UserStoreAccessDto, created.toObject())
  }

  async deleteUserStoreAccess(userId: string, userStoreAccessId: string) {
    this.userPermissions.requiresAdmin()

    //Check if user exists
    //await this.getUser(userId)

    const deleted =
      await this.userStoreAccessModel.findByIdAndDelete(userStoreAccessId)
    if (!deleted) {
      throw new NotFoundException('UserStoreAccess not found')
    }
    return plainToInstance(UserStoreAccessDto, deleted.toObject())
  }

  async deleteUserStoreAccessByStoreId(userId: string, storeId: string) {
    this.userPermissions.requiresAdmin()

    //Check if user exists
    //await this.getUser(userId)

    const deleted = await this.userStoreAccessModel.findOneAndDelete({
      userId,
      storeId,
    })
    if (!deleted) {
      throw new NotFoundException('UserStoreAccess not found')
    }
    return plainToInstance(UserStoreAccessDto, deleted.toObject())
  }
}
