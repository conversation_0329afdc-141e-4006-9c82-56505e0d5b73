import { CACHE_MANAGER } from '@nestjs/cache-manager'
import {
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { ClsService } from 'nestjs-cls'
import { Cache } from 'cache-manager'
import { AuthType, UserContext } from '@packages/shared-entities'
import { Model } from 'mongoose'
import { UserStoreAccess, UserStoreAccessDocument } from './entities/user.mongo'
import { InjectModel } from '@nestjs/mongoose'

@Injectable()
export class UserPermissions {
  private readonly logger = new Logger(UserPermissions.name)

  constructor(
    configService: ConfigService,
    private readonly cls: ClsService,
    @InjectModel(UserStoreAccess.name)
    private readonly userStoreAccessModel: Model<UserStoreAccessDocument>,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache
  ) {}

  currentUserContext() {
    return this.cls.get('user') as UserContext | undefined
  }

  requiresAdmin() {
    const user = this.cls.get('user') as UserContext | undefined
    if (!user?.roles?.admin) {
      this.logger.warn(
        {
          userId: user?.userId,
        },
        `requiresAdmin: user does not have admin role`
      )
      throw new UnauthorizedException('Admin permission required')
    }

    return user
  }

  requiresUser() {
    const user = this.currentUserContext()
    if (!user) {
      this.logger.warn(`requiresMerchant: no user`)
      throw new UnauthorizedException('User required')
    }

    //TODO: validate user exists in DB??

    return user
  }

  requiresMerchant() {
    const user = this.requiresUser()
    if (user.roles?.admin) {
      return user
    }

    if (!user.roles?.merchant) {
      this.logger.warn(
        {
          userId: user?.userId,
          roles: user?.roles,
        },
        `requiresMerchant: user does not have merchant role`
      )
      throw new UnauthorizedException('Merchant role required')
    }

    return user
  }

  async requiresAccessOnUser(accessedUserId: string) {
    const user = this.requiresUser()
    if (user.roles?.admin) {
      return user
    }

    if (user.userId !== accessedUserId) {
      this.logger.warn(
        {
          userId: user.userId,
          accessedUserId,
        },
        `hasAccessOnUser: user does not have access to user data`
      )
      throw new UnauthorizedException(`Can\'t access user data`)
    }
    return user
  }

  async requiresAccessOnStore(storeId: string) {
    const user = this.requiresUser()
    if (user.roles?.admin) {
      return user
    }

    const storeAccesses = user?.storeAccesses ?? {}
    if (
      storeAccesses[storeId] === undefined ||
      storeAccesses[storeId] === null
    ) {
      //check the user_store_access table
      const accessResult = await this.userStoreAccessModel.findOne({
        userId: user?.userId,
        storeId,
      })

      if (accessResult) {
        storeAccesses[storeId] = true
      } else {
        storeAccesses[storeId] = false
      }
    }

    //Cache the access check result back to the async store
    user['storeAccesses'] = storeAccesses

    if (storeAccesses[storeId]) {
      return user
    }

    this.logger.warn(
      {
        userId: user?.userId,
        storeId,
      },
      `hasAccessOnStore: user does not have access to store`
    )
    throw new UnauthorizedException(`Can't access store`)
  }

  //Attention: call this with extreme caution. This will force to allow the current API call session to access a store. Make sure that your use case makes sense to do so. It normally only makes sense in a storefront or webhook context.
  grantAccessOnStore(storeId: string) {
    const user = this.requiresUser()
    user.storeAccesses = user.storeAccesses ?? {}
    user.storeAccesses[storeId] = true
    return user
  }

  //Attention: call this with extreme caution. This will force to allow the current API call session to access a store. Make sure that your use case makes sense to do so. Only grant after validating the shopify store is linked to a store that the user has access to.
  grantAccessOnShopifyStore(shop: string, appHandle: string) {
    const user = this.requiresUser()
    user.shopifyContext = {
      shop,
      appHandle,
    }
    return user
  }

  async requiresAccessOnShopifyStore(shop: string) {
    const user = this.requiresUser()

    if (user.roles?.admin) {
      return user
    }

    if (
      [AuthType.shopifyAdmin, AuthType.shopifyProxy, AuthType.robot].includes(
        user.authType
      ) &&
      user.shopifyContext
    ) {
      if (user.shopifyContext.shop === shop) {
        return user
      }
    }

    if (user.authType === AuthType.clerk) {
      //TODO: check if the shop is linked to a store that the user has access to
    }

    this.logger.warn(
      {
        userId: user?.userId,
        shop,
      },
      `requiresAccessOnShopifyStore: user does not have access to shopify store`
    )
    throw new UnauthorizedException(`Can't access shopify store`)
  }
}
