import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'
import * as paginate from 'mongoose-paginate-v2'
import { mongooseSchemaOptions } from 'src/common/data-helper'

@Schema(mongooseSchemaOptions)
export class User {
  @Prop()
  _id: string

  @Prop()
  authType: 'clerk'

  @Prop()
  createdAt: Date

  @Prop()
  updatedAt: Date

  @Prop()
  displayName: string

  @Prop()
  email: string

  @Prop()
  deletedAt?: Date
}

export type UserDocument = HydratedDocument<User>
export const UserSchema = SchemaFactory.createForClass(User)
UserSchema.plugin(paginate)

@Schema(mongooseSchemaOptions)
export class UserStoreAccess {
  @Prop()
  userId: string

  @Prop()
  storeId: string

  @Prop()
  grantedBy: string

  @Prop()
  createdAt: Date
}

export type UserStoreAccessDocument = HydratedDocument<UserStoreAccess>
export const UserStoreAccessSchema =
  SchemaFactory.createForClass(UserStoreAccess)
UserStoreAccessSchema.plugin(paginate)
