import { Module } from '@nestjs/common'
import { UsersService } from './users.service'
import { UsersController } from './users.controller'
import { UserPermissions } from './user-permissions'
import { ClsModule } from 'nestjs-cls'
import { AuthModule } from 'src/auth/auth.module'
import { MongooseModule } from '@nestjs/mongoose'
import {
  User,
  UserSchema,
  UserStoreAccess,
  UserStoreAccessSchema,
} from './entities/user.mongo'

@Module({
  imports: [
    AuthModule,
    ClsModule,
    MongooseModule.forFeature([
      {
        name: User.name,
        collection: 'users',
        schema: UserSchema,
      },
    ]),
    MongooseModule.forFeature([
      {
        name: UserStoreAccess.name,
        collection: 'user_store_accesses',
        schema: UserStoreAccessSchema,
      },
    ]),
  ],
  providers: [UsersService, UserPermissions],
  controllers: [UsersController],
  exports: [UsersService, UserPermissions],
})
export class UsersModule {}
