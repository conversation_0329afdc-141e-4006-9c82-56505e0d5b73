import { Options } from 'pino-http'
import { shopifyServerConfig } from './shopify-config'
import * as firebaseAdmin from 'firebase-admin'
import { VerifyWebhookOptions } from '@clerk/backend/webhooks'

export enum AppEnvironment {
  development = 'development',
  staging = 'staging',
  production = 'production',
}

const PinoLevelToSeverityLookup: Record<string, string> = {
  trace: 'DEBUG',
  debug: 'DEBUG',
  info: 'INFO',
  warn: 'WARNING',
  error: 'ERROR',
  fatal: 'CRITICAL',
}

export type ClerkConfig = {
  publishableKey: string
  secretKey: string
  jwtKey: string
  verifyWebhookOptions: VerifyWebhookOptions
}

interface LogRecord {
  trace_id?: string
  span_id?: string
  trace_flags?: string
  [key: string]: unknown
}

export function getServerConfig() {
  const isDev = process.env.APP_ENV === AppEnvironment.development
  const { private_key: firebasePrivateKey } = JSON.parse(
    process.env.FIREBASE_PRIVATE_KEY || '{ "private_key": null }'
  )
  const gcpProject = process.env.GCP_PROJECT
  const isGCP = Boolean(gcpProject)

  if (!process.env.INTENTNOW_ANALYTICS_EVENT_VERSION) {
    throw new Error('INTENTNOW_ANALYTICS_EVENT_VERSION is not set')
  }

  return {
    app: {
      port: 4000,
      name: process.env.APP_NAME || 'intentnow-server-unknown',
      environment: process.env.APP_ENV ?? 'undefined',
      gitVersion: process.env.GIT_VERSION ?? 'unknown',
      gcpProject,
    },
    auth: {
      robotTokens: process.env.AUTH_ROBOT_TOKENS?.split(',') ?? [],
      allowedUserEmails: process.env.AUTH_USER_EMAILS?.split(',') ?? [],
    },
    logger: {
      pinoHttp: {
        customProps: (req) => ({
          context: 'HTTP',
          requestId: req.headers['x-correlation-id'],
        }),
        customSuccessObject: (req, res, successObject) => {
          return {
            ...successObject,
            url: req.url,
          }
        },
        customErrorObject: (req, res, error, errorObject) => {
          return {
            ...errorObject,
            url: req.url,
            statusCode: res.statusCode,
          }
        },
        redact: {
          paths: ['req', 'res', 'hostname'],
          remove: true,
        },
        transport: isDev
          ? {
              target: 'pino-pretty',
              options: {
                singleLine: true,
                messageFormat: '{requestId} [{context}] {msg}',
              },
            }
          : undefined,
        level: process.env.LOGGER_LEVEL || 'info',

        //Converted to GCP logging format
        messageKey: 'message',
        formatters: {
          log(object: LogRecord): Record<string, unknown> {
            // Add trace context attributes following Cloud Logging structured log format described
            // in https://cloud.google.com/logging/docs/structured-logging#special-payload-fields
            if (gcpProject) {
              const { trace_id, span_id, trace_flags, ...rest } = object

              return {
                'logging.googleapis.com/trace': trace_id
                  ? `projects/${gcpProject}/traces/${trace_id}`
                  : trace_id,
                'logging.googleapis.com/spanId': span_id,
                'logging.googleapis.com/trace_sampled': trace_flags
                  ? trace_flags === '01'
                  : undefined,

                ...rest,
              }
            } else {
              return object
            }
          },
          level(label: string, number: number) {
            return {
              severity:
                PinoLevelToSeverityLookup[label] ||
                PinoLevelToSeverityLookup['info'],
              level: number,
            }
          },
        },
      } satisfies Options,
    },
    cache: {
      redisUrl: process.env.REDIS_URL,
    },
    shopify: shopifyServerConfig,
    mongoDB: {
      uri: process.env.MONGODB_URI ?? '',
    },
    intentnow: {
      eventApi: {
        url: process.env.INTENTNOW_EVENT_API ?? '',
        token: process.env.INTENTNOW_EVENT_API_TOKEN ?? '',
        shadowUrl: process.env.INTENTNOW_EVENT_SHADOW_API ?? '',
      },
      modelApi: {
        baseUrl: process.env.INTENTNOW_MODEL_API_BASE_URL ?? '',
        token: process.env.INTENTNOW_MODEL_API_TOKEN,
      },
      storePromo: {
        storeConfigCacheTTLInSeconds: 60 * 2,
      },
      eventsSentToStatsig: {
        page_viewed: true,
        checkout_completed: true,
        'intentnow-promo-shown': true,
        'intentnow-promo-copybtn-clicked': true,
        'intentnow-promo-minimizedui-clicked': true,
        'intentnow-promo-minimizebtn-clicked': true,
        'intentnow-promo-minimizeclosebtn-clicked': true,
        'intentnow-promo-backdrop-clicked': true,
        'intentnow-promo-candidate': true,
      } as Record<string, boolean | undefined>,
      imageStorage: {
        cdnBucket: 'intentnow-cdn',
        uploadPath: 'cdn/uploaded-images',
        imageUrlPrefix: 'https://cdn.intentnow.com/',
      },
      analyticsEventVersion: +process.env.INTENTNOW_ANALYTICS_EVENT_VERSION,
    },
    statsig: {
      secretKey: process.env.STATSIG_SECRET_KEY,
      environment: process.env.APP_ENV,
      consoleApi: {
        baseUrl: 'https://statsigapi.net/console',
        token: process.env.STATSIG_CONSOLE_API_TOKEN ?? '',
        version: '20240601',
      },
    },
    amplitude: {
      apiUrl: 'https://amplitude.com/api',
      apiKey: process.env.AMPLITUDE_API_KEY ?? '',
      secretKey: process.env.AMPLITUDE_SECRET_KEY ?? '',
      logLevel: +(process.env.AMPLITUDE_LOG_LEVEL ?? '2'),
    },
    ipApi: {
      baseUrl: process.env.IP_API_BASE_URL ?? '',
      apiKey: process.env.IP_API_KEY,
    },
    firestore: {
      //Default to the production DB "(default)"" if none specified
      database: process.env.FIRESTORE_DATABASE,
    },
    firebase: {
      credential: isGCP
        ? firebaseAdmin.credential.applicationDefault()
        : firebaseAdmin.credential.cert({
            projectId: process.env.FIREBASE_PROJECT_ID,
            clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
            privateKey: firebasePrivateKey,
          }),
    },
    clerk: {
      publishableKey: process.env.CLERK_PUBLISHABLE_KEY ?? '',
      secretKey: process.env.CLERK_SECRET_KEY ?? '',
      jwtKey: process.env.CLERK_JWT_PUBLIC_KEY ?? '',
      verifyWebhookOptions: {
        signingSecret: process.env.CLERK_WEBHOOK_SIGNING_SECRET,
      },
    } as ClerkConfig,
    openTelemetry: {
      metrics: {
        hostMetrics: true, // Includes Host Metrics
        apiMetrics: {
          enable: true, // Includes api metrics
          defaultAttributes: {
            // You can set default labels for api metrics
          },
          //ignoreRoutes: ['/favicon.ico'], // You can ignore specific routes (See https://docs.nestjs.com/middleware#excluding-routes for options)
          ignoreUndefinedRoutes: false, //Records metrics for all URLs, even undefined ones
          prefix: 'intentnow_server', // Add a custom prefix to all API metrics
        },
      },
    },
    billing: {
      stripe: {
        apiKey: process.env.STRIPE_SECRET_KEY ?? '',
        customerPortalUrl:
          'https://billing.stripe.com/p/login/bJe3cve5U5Sb7e42iM6kg00',
      },
    },
  }
}

export type ServerConfig = ReturnType<typeof getServerConfig>
