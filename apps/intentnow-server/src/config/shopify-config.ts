import { ApiVersion, LogSeverity } from '@shopify/shopify-api'
import { AppConfigParams } from '@shopify/shopify-app-express'

const shopifyAppConfigEnv: {
  APP_HANDLE: string
  API_KEY: string
  SECRET_KEY: string
  APP_HANDLE_2?: string
  PROMO_EMBED_ID?: string
}[] = JSON.parse(process.env.SHOPIFY_APP_CONFIGS ?? '[]')

//Some basic validation
shopifyAppConfigEnv.forEach((config) => {
  if (!config.APP_HANDLE || !config.API_KEY || !config.SECRET_KEY) {
    throw new Error(
      'SHOPIFY_APP_CONFIGS must contain APP_HANDLE, API_KEY, SECRET_KEY'
    )
  }
})

const shopifyAppAccessScopes =
  process.env.SHOPIFY_APP_ACCESS_SCOPES?.split(',') ?? []

export enum ShopifySessionStorageType {
  memory = 'memory',
  redis = 'redis',
  firestore = 'firestore',
}

export interface ShopifyAppConfig {
  shopifyAppHandle: string
  shopifyAppHandle2?: string
  promoEmbedId?: string
  shopifyAppConfig: AppConfigParams
  rawConfig: {
    APP_HANDLE: string
    APP_HANDLE_2?: string
    PROMO_EMBED_ID?: string
    API_KEY: string
    SECRET_KEY: string
  }
}

const shopifyAppConfigs: ShopifyAppConfig[] = shopifyAppConfigEnv.map(
  (env) => ({
    shopifyAppHandle: env.APP_HANDLE ?? 'undefined',
    shopifyAppHandle2: env.APP_HANDLE_2,
    promoEmbedId: env.PROMO_EMBED_ID,
    shopifyAppConfig: {
      api: {
        apiKey: env.API_KEY ?? 'undefined',
        apiSecretKey: env.SECRET_KEY ?? 'undefined',

        // Keep this list in sync with the .toml files
        scopes: shopifyAppAccessScopes,
        hostName: process.env.SHOPIFY_APP_SERVER_HOST ?? 'undefined',
        isEmbeddedApp: true,
        //Need to also upate .graphqlrc.ts to match this version
        apiVersion: '2024-07' as ApiVersion,
        logger: {
          level: ((): LogSeverity => {
            const logLevel = (process.env.LOGGER_LEVEL || 'info').toLowerCase()
            let shopifyLogLevel: LogSeverity = LogSeverity.Info
            switch (logLevel) {
              case 'debug':
                shopifyLogLevel = LogSeverity.Debug
                break
              case 'error':
                shopifyLogLevel = LogSeverity.Error
                break
              case 'warn':
                shopifyLogLevel = LogSeverity.Warning
                break
              case 'info':
              default:
                shopifyLogLevel = LogSeverity.Info
                break
            }
            return shopifyLogLevel
          })(),
        },
      },
      auth: {
        path: `/api/shopify/${env.APP_HANDLE}/login`,
        callbackPath: `/api/shopify/${env.APP_HANDLE}/callback`,
      },
      webhooks: {
        path: `/api/shopify/${env.APP_HANDLE}/webhooks`,
      },
      useOnlineTokens: false,
    },
    rawConfig: env,
  })
)

export interface ShopifyServerConfig {
  sessionStorageType: ShopifySessionStorageType
  shopifyAppConfigs: ShopifyAppConfig[]
}

export const shopifyServerConfig: ShopifyServerConfig = {
  sessionStorageType: process.env
    .SHOPIFY_SESSION_STORAGE as ShopifySessionStorageType,
  shopifyAppConfigs,
}
