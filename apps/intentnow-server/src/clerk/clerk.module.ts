import { Module } from '@nestjs/common'
import { ClerkWebhookController } from './clerk-webhook.controller'
import { ClerkWebhookService } from './clerk-webhook.service'
import { UsersModule } from '../users/users.module'
import { ClsModule } from 'nestjs-cls'

@Module({
  imports: [UsersModule, ClsModule],
  controllers: [ClerkWebhookController],
  providers: [ClerkWebhookService],
})
export class ClerkModule {}
