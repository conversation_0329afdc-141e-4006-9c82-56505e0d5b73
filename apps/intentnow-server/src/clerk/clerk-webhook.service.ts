import { Injectable, Logger } from '@nestjs/common'
import { WebhookEvent, UserJSO<PERSON> } from '@clerk/backend'
import { UsersService } from '../users/users.service'
import { UserCreateDto } from '../dto/user.dto'
import { UserWebhookEvent } from '@clerk/backend/dist/api/resources/Webhooks'

@Injectable()
export class ClerkWebhookService {
  private readonly logger = new Logger(ClerkWebhookService.name)

  constructor(private readonly usersService: UsersService) {}

  async handleEvent(event: WebhookEvent): Promise<void> {
    switch (event.type) {
      case 'user.created':
        await this.handleUserCreated(event as UserWebhookEvent)
        break
      case 'user.updated':
        await this.handleUserCreated(event as UserWebhookEvent)
        break
      case 'user.deleted':
        await this.handleUserDeleted(event as UserWebhookEvent)
        break
      default:
        this.logger.warn(`Unhandled event type: ${event.type}`)
    }
  }

  private async handleUserCreated(event: UserWebhookEvent): Promise<void> {
    const user = event.data as UserJSON

    const userCreateDto: UserCreateDto = {
      _id: user.id,
      authType: 'clerk',
      email: this.extractPrimaryEmail(user),
      displayName: this.extractDisplayName(user),
    }

    try {
      await this.usersService.createUser(userCreateDto)
    } catch (error) {
      this.logger.error(
        `Failed to create user ${user.id}: ${error.message}`,
        error.stack
      )
      throw error
    }
  }

  private extractPrimaryEmail(user: UserJSON): string {
    const primaryEmail = user.email_addresses?.find(
      (email) => email.id === user.primary_email_address_id
    )
    return (
      primaryEmail?.email_address ||
      user.email_addresses?.[0]?.email_address ||
      ''
    )
  }

  private extractDisplayName(user: UserJSON): string {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`.trim()
    }
    if (user.first_name) {
      return user.first_name
    }
    if (user.last_name) {
      return user.last_name
    }
    return user.username || this.extractPrimaryEmail(user)
  }

  private async handleUserDeleted(event: UserWebhookEvent): Promise<void> {
    const user = event.data as UserJSON

    try {
      await this.usersService.softDeleteUser(user.id)
      this.logger.log(`Successfully soft deleted user ${user.id}`)
    } catch (error) {
      this.logger.error(
        `Failed to soft delete user ${user.id}: ${error.message}`,
        error.stack
      )
      throw error
    }
  }
}
