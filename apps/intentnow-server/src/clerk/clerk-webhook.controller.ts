import {
  Controller,
  HttpCode,
  HttpStatus,
  InternalServerErrorException,
  Logger,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common'
import { ClerkWebhookService } from './clerk-webhook.service'
import { WebhookEvent } from '@clerk/backend'
import { ClerkWebhookGuard } from './clerk-webhook-guard'
import { ClerkWebhookRequest } from './entities/clerk-webhook-request'

@Controller('api/intentnow/clerk-webhooks')
export class ClerkWebhookController {
  private readonly logger = new Logger(ClerkWebhookController.name)

  constructor(private readonly clerkWebhookService: ClerkWebhookService) {}
  @Post()
  @HttpCode(HttpStatus.OK)
  @UseGuards(ClerkWebhookGuard)
  async handleWebhook(
    @Req() req: ClerkWebhookRequest
  ): Promise<{ received: boolean }> {
    const event: WebhookEvent | undefined = req.clerkWebhookEvent

    if (!event) {
      this.logger.error('No verified webhook event found in request')
      throw new InternalServerErrorException('Webhook event not found')
    }

    await this.clerkWebhookService.handleEvent(event)
    return { received: true }
  }
}
