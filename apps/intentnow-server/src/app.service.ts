import { Injectable, Logger } from '@nestjs/common'
import <PERSON>atsig from 'statsig-node'
import { MetricService, OtelMethodCounter } from 'nestjs-otel'
import { Counter } from '@opentelemetry/api'
import { UserContext } from './auth/entities/user-context'
import { ConfigService } from '@nestjs/config'
import { ServerConfig } from './config/config'

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name)
  private readonly appConfig: ServerConfig['app']
  private helloCounter: Counter

  constructor(
    private readonly metricService: MetricService,
    private readonly configService: ConfigService
  ) {
    this.helloCounter = this.metricService.getCounter('hello_counter', {
      description: 'getHello counter',
    })
    this.appConfig = this.configService.get<ServerConfig['app']>('app')!
  }

  @OtelMethodCounter()
  getHello(requester?: UserContext | undefined) {
    this.logger.log('getHello: called')
    this.helloCounter.add(1)
    return {
      message: 'Hello World!',
      app: {
        name: this.appConfig.name,
        environment: this.appConfig.environment,
        gitVersion: this.appConfig.gitVersion,
      },
      user: requester
        ? {
            userId: requester.userId,
            authType: requester.authType,
            userInfo: requester.userInfo,
            roles: requester.roles,
          }
        : undefined,
    }
  }

  beforeApplicationShutdown() {
    this.logger.log('beforeApplicationShutdown: started')
    Statsig.shutdown()
    this.logger.log('beforeApplicationShutdown: finished')
  }
}
