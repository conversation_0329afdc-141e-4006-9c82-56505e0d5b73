import { InjectStripeClient } from '@golevelup/nestjs-stripe'
import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { ServerConfig } from 'src/config/config'
import { StoreBillingDto } from 'src/dto/store.dto'
import { UserPermissions } from 'src/users/user-permissions'
import Stripe from 'stripe'

@Injectable()
export class BillingService {
  private readonly logger = new Logger(BillingService.name)
  private readonly billingConfig: ServerConfig['billing']

  constructor(
    private configService: ConfigService,
    @InjectStripeClient() private readonly stripeClient: Stripe,
    private readonly userPermissions: UserPermissions
  ) {
    this.billingConfig =
      this.configService.get<ServerConfig['billing']>('billing')!
  }

  async getStripeSubscriptionInfo(
    subscriptionId: string,
    returnUrl?: string | undefined
  ): Promise<StoreBillingDto> {
    const userContext = this.userPermissions.requiresUser()
    const userEmail = userContext.userInfo?.email

    const stripeSubscription =
      await this.stripeClient.subscriptions.retrieve(subscriptionId)
    const currentPeriodEnd: number | undefined =
      stripeSubscription.items?.data[0]?.current_period_end

    const productId: string | undefined = (
      stripeSubscription as any
    )?.plan?.product?.toString()
    const stripeProduct = productId
      ? await this.stripeClient.products.retrieve(productId)
      : undefined
    const stripePrice = stripeSubscription.items?.data[0]?.price

    const stripePaymentMethod = stripeSubscription.default_payment_method
      ? await this.stripeClient.paymentMethods.retrieve(
          stripeSubscription.default_payment_method as string
        )
      : undefined

    const autoLogin =
      userContext.roles?.admin ||
      (stripePaymentMethod?.billing_details?.email &&
        stripePaymentMethod.billing_details.email.toLowerCase() ===
          userEmail?.toLowerCase())
    const stripeBillingPortalSession = autoLogin
      ? await this.stripeClient.billingPortal.sessions.create({
          customer: stripeSubscription.customer.toString(),
          return_url: returnUrl,
        })
      : undefined

    const subscription: StoreBillingDto['subscription'] = {
      status: stripeSubscription.status,
      productName: stripeProduct?.name ?? 'Unknown',
      renewsAt: currentPeriodEnd
        ? new Date(currentPeriodEnd * 1000)
        : undefined,

      price: {
        amount: (stripePrice.unit_amount ?? 0) / 100,
        currency: stripePrice.currency.toUpperCase(),
        interval: stripePrice.recurring?.interval.toUpperCase(),
      },

      payment: stripePaymentMethod?.card
        ? {
            card: {
              brand: stripePaymentMethod.card.brand,
              last4: stripePaymentMethod.card.last4,
              expMonth: stripePaymentMethod.card.exp_month,
              expYear: stripePaymentMethod.card.exp_year,
            },
          }
        : undefined,
    }

    return {
      subscription,
      stripe: {
        billingPortalUrl: stripeBillingPortalSession
          ? stripeBillingPortalSession.url
          : this.billingConfig.stripe.customerPortalUrl,
      },
    }
  }
}
