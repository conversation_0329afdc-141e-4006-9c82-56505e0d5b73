import { Module } from '@nestjs/common'
import { BillingService } from './billing.service'
import { BillingController } from './billing.controller'
import { StripeModule } from '@golevelup/nestjs-stripe'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { ServerConfig } from 'src/config/config'
import { AuthModule } from 'src/auth/auth.module'
import { UsersModule } from 'src/users/users.module'
import { ClsModule } from 'nestjs-cls'

@Module({
  imports: [
    ConfigModule,
    AuthModule,
    UsersModule,
    ClsModule,
    StripeModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const config =
          configService.get<ServerConfig['billing']>('billing')!.stripe
        return {
          apiKey: config.apiKey,
        }
      },
      inject: [ConfigService],
    }),
  ],
  providers: [BillingService],
  controllers: [BillingController],
  exports: [BillingService],
})
export class BillingModule {}
