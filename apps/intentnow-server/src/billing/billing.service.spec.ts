import { Test, TestingModule } from '@nestjs/testing'
import { BillingService } from './billing.service'
import { ConfigService } from '@nestjs/config'
import { STRIPE_CLIENT_TOKEN } from '@golevelup/nestjs-stripe'

describe('BillingService', () => {
  let service: BillingService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BillingService,
        ConfigService,
        {
          provide: STRIPE_CLIENT_TOKEN,
          useValue: {},
        },
      ],
    }).compile()

    service = module.get<BillingService>(BillingService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })
})
