import { Global, Module, DynamicModule, Provider } from '@nestjs/common'
import { FirebaseAdminModuleAsyncOptions } from './firebase-admin.interface'
import {
  FIREBASE_ADMIN_MODULE_OPTIONS,
  FIREBASE_ADMIN_INJECT,
} from './firebase-admin.constant'
import * as admin from 'firebase-admin'
//import { FirebaseAuthGuard } from './firebase-auth-guard.ts.bak'

@Global()
@Module({
  providers: [],
  exports: [],
})
export class FirebaseAdminModule {
  static forRoot(options: admin.AppOptions): DynamicModule {
    const firebaseAdminModuleOptions = {
      provide: FIREBASE_ADMIN_MODULE_OPTIONS,
      useValue: options,
    }

    const app =
      admin.apps.length === 0 ? admin.initializeApp(options) : admin.apps[0]
    try {
      app?.firestore().settings({ ignoreUndefinedProperties: true })
    } catch (error) {}

    const firebaseAuthencationProvider = {
      provide: FIREBASE_ADMIN_INJECT,
      useValue: app,
    }

    return {
      module: FirebaseAdminModule,
      providers: [firebaseAdminModuleOptions, firebaseAuthencationProvider],
      exports: [firebaseAdminModuleOptions, firebaseAuthencationProvider],
    }
  }

  static forRootAsync(options: FirebaseAdminModuleAsyncOptions): DynamicModule {
    const firebaseAdminModuleOptions = {
      provide: FIREBASE_ADMIN_MODULE_OPTIONS,
      useFactory: options.useFactory,
      inject: options.inject || [],
    }

    const firebaseAuthencationProvider = {
      provide: FIREBASE_ADMIN_INJECT,
      useFactory: (opt: admin.AppOptions) => {
        const app =
          admin.apps.length === 0 ? admin.initializeApp(opt) : admin.apps[0]
        try {
          app?.firestore().settings({ ignoreUndefinedProperties: true })
        } catch (error) {}

        return app
      },
      inject: [FIREBASE_ADMIN_MODULE_OPTIONS],
    }

    return {
      module: FirebaseAdminModule,
      imports: options.imports,
      providers: [
        firebaseAdminModuleOptions as Provider,
        firebaseAuthencationProvider,
      ],
      exports: [
        firebaseAdminModuleOptions as Provider,
        firebaseAuthencationProvider,
      ],
    }
  }
}
