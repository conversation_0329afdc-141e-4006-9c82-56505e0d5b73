import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common'
import { UserContext, AuthType } from 'src/auth/entities/user-context'
import { Session } from '@shopify/shopify-api'
import { ShopifyRequestContext } from './entities/shopify'
import { ClsService } from 'nestjs-cls'

@Injectable()
export class ShopifyEmbeddedAuthGuard implements CanActivate {
  private readonly logger = new Logger(ShopifyEmbeddedAuthGuard.name)

  constructor(private readonly cls: ClsService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest()
    const shopifySession = request.res?.locals?.shopify?.session as
      | Session
      | undefined

    if (!shopifySession?.shop) {
      throw new UnauthorizedException('auth/none-shopify-session')
    }

    if (!request.params.appHandle) {
      throw new UnauthorizedException('auth/missing-app-handle')
    }

    const shopifyContext: ShopifyRequestContext = {
      shop: shopifySession.shop,
      appHandle: request.params.appHandle,
    }
    const user: UserContext = {
      authType: AuthType.shopifyAdmin,
      userId: shopifySession.shop,
      roles: {
        admin: false,
        merchant: false,
      },
      shopifyContext,
    }
    this.cls.set('user', user)
    request['user'] = user

    this.logger.log(
      {
        shop: shopifySession.shop,
      },
      'canActivate: shopify session found, access granted'
    )
    return true
  }
}
