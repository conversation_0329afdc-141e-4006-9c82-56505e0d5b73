import { MiddlewareConsumer, Module } from '@nestjs/common'
import { ShopifyService } from './shopify.service'
import { ShopifyController } from './shopify.controller'
import { ShopifyEmbeddedController } from './shopify-embedded.controller'
import { AuthModule } from 'src/auth/auth.module'
import { ClsModule } from 'nestjs-cls'
import { UsersModule } from 'src/users/users.module'
import { MongooseModule } from '@nestjs/mongoose'
import { ShopifyAppMongo, ShopifyAppSchema } from './entities/shopify.mongo'

@Module({
  imports: [
    AuthModule,
    UsersModule,
    ClsModule,
    MongooseModule.forFeature([
      {
        name: ShopifyAppMongo.name,
        collection: 'shopify_apps',
        schema: ShopifyAppSchema,
      },
    ]),
  ],
  providers: [ShopifyService],
  controllers: [ShopifyController, ShopifyEmbeddedController],
  exports: [ShopifyService],
})
export class ShopifyModule {
  constructor(private readonly shopifyService: ShopifyService) {}
  configure(consumer: MiddlewareConsumer) {
    const shopifyApps = this.shopifyService.getShopifyApps()
    shopifyApps.forEach((shopifyAppCfg) => {
      const shopifyApp = shopifyAppCfg.shopifyApp
      const appHandle = shopifyAppCfg.shopifyAppHandle
      //OAuth routes
      ;['login', 'callback'].forEach((route) => {
        consumer
          .apply(shopifyApp.cspHeaders())
          .forRoutes(`/api/shopify/${appHandle}/${route}`)
      })
      consumer
        .apply(shopifyApp.auth.begin())
        .forRoutes(`/api/shopify/${appHandle}/login`)
      consumer
        .apply(
          shopifyApp.auth.callback(),
          shopifyApp.redirectToShopifyOrAppRoot()
        )
        .forRoutes(`/api/shopify/${appHandle}/callback`)

      //Authenticated routes
      ;['debug-info'].forEach((route) => {
        consumer
          .apply(shopifyApp.cspHeaders())
          .forRoutes(`/api/shopify/${appHandle}/${route}`)

        consumer
          .apply(
            shopifyApp.ensureInstalledOnShop(),
            shopifyApp.validateAuthenticatedSession()
          )
          .forRoutes(`/api/shopify/${appHandle}/${route}`)
      })
    })
  }
}
