import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common'
import { ShopifyService } from 'src/shopify/shopify.service'
import * as crypto from 'crypto'
import {
  UserContext,
  UserRequest,
  AuthType,
} from 'src/auth/entities/user-context'
import { ClsService } from 'nestjs-cls'

@Injectable()
export class ShopifyProxyAuthGuard implements CanActivate {
  private readonly logger = new Logger(ShopifyProxyAuthGuard.name)

  constructor(
    private readonly shopifyService: ShopifyService,
    private readonly cls: ClsService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest() as UserRequest
    const signature = request.query.signature as string | undefined

    if (!signature) {
      throw new UnauthorizedException('auth/missing-signature')
    }

    if (!request.params.appHandle) {
      throw new UnauthorizedException('auth/missing-app-handle')
    }
    const appHandle = request.params.appHandle
    const appSecretKey =
      this.shopifyService.getShopifyApp(appHandle).api.config.apiSecretKey

    const joinedQuery = Object.keys(request.query)
      .sort()
      .filter((key) => key !== 'signature')
      .map((key) => {
        let value: string | undefined
        const rawValue = request.query[key]
        if (Array.isArray(rawValue)) {
          value = rawValue.join(',')
        } else {
          value = rawValue as string
        }
        return `${key}=${value}`
      })
      .join('')

    const hash = crypto
      .createHmac('sha256', appSecretKey)
      .update(joinedQuery)
      .digest('hex')

    if (hash !== signature) {
      throw new UnauthorizedException('auth/invalid-signature')
    }

    if (!request.query.shop || typeof request.query.shop !== 'string') {
      throw new UnauthorizedException('auth/invalid-shop')
    }

    const user: UserContext = {
      authType: AuthType.shopifyProxy,
      userId: request.query.shop,
      roles: {
        admin: false,
        merchant: false,
      },
      shopifyContext: {
        shop: request.query.shop,
        appHandle,
      },
    }
    this.cls.set('user', user)
    request['user'] = user

    return true
  }
}
