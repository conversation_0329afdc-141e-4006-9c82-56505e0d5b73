import { Test, TestingModule } from '@nestjs/testing'
import { ShopifyService } from './shopify.service'
import { ConfigService } from '@nestjs/config'

describe('ShopifyService', () => {
  let service: ShopifyService

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ShopifyService, ConfigService],
    })
      .useMocker((token) => {
        if (token === 'CONFIGURATION_TOKEN') {
          return {
            shopify: {
              shopifyAppConfigs: [],
            },
            cache: {},
          }
        }
        return {}
      })
      .compile()

    service = module.get<ShopifyService>(ShopifyService)
  })

  it('should be defined', () => {
    expect(service).toBeDefined()
  })
})
