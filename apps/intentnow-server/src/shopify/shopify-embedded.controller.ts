import { Controller, Get, Req, UseGuards } from '@nestjs/common'
import { ShopifyService } from './shopify.service'
import { ShopifyEmbeddedAuthGuard } from './shopify-auth-guard'
import { UserRequest } from 'src/auth/entities/user-context'

@Controller('api/shopify')
export class ShopifyEmbeddedController {
  constructor(private readonly shopifyService: ShopifyService) {}

  @UseGuards(ShopifyEmbeddedAuthGuard)
  @Get(':appHandle/debug-info')
  async debugInfo(@Req() request: UserRequest) {
    const shopInfo = await this.shopifyService.getShopInfo(request.user)

    return {
      shopInfo,
    }
  }
}
