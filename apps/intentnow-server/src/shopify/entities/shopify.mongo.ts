import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose'
import { HydratedDocument } from 'mongoose'
import * as paginate from 'mongoose-paginate-v2'
import { mongooseSchemaOptions } from 'src/common/data-helper'

@Schema(mongooseSchemaOptions)
export class ShopifyAppMongo {
  @Prop()
  createdAt: Date

  @Prop()
  name: string

  @Prop()
  appHandle: string

  //For some reason, Shopify does not use the standard app handle for the appembed block string, so we will need this ID to look up the promo embed block.
  @Prop()
  themeAppId: string

  // API key
  @Prop()
  clientId: string

  // API secret
  @Prop()
  clientSecret: string

  @Prop()
  promoEmbedId: string
}

export type ShopifyAppDocument = HydratedDocument<ShopifyAppMongo>
export const ShopifyAppSchema = SchemaFactory.createForClass(ShopifyAppMongo)
ShopifyAppSchema.plugin(paginate)
