import {
  Body,
  Controller,
  Get,
  Logger,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { UserRequest } from 'src/auth/entities/user-context'
import { ShopifyRestOp, ShopifyService } from './shopify.service'

@Controller('api/shopify')
export class ShopifyController {
  private readonly logger = new Logger(ShopifyController.name)

  constructor(
    private readonly shopifyService: ShopifyService,
    private readonly apiAuthGuard: ApiAuthGuard
  ) {}

  @Get(':appHandle/callback')
  async callback() {}

  @Get(':appHandle/login')
  async login() {}

  @UseGuards(ApiAuthGuard)
  @Post('rest')
  async shopifyRest(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      rest: {
        op: ShopifyRestOp
        path: string
        query?: any
        data?: any
      }
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)

    return await this.shopifyService.executeRest(request.user, body.rest)
  }

  @UseGuards(ApiAuthGuard)
  @Post('graphql')
  async shopifyGraphQl(
    @Req() request: UserRequest,
    @Body()
    body: {
      shop: string
      graphQl: {
        query: string
        variables?: any
      }
    }
  ) {
    await this.apiAuthGuard.appendShopifyContext(request.user, body.shop)

    return await this.shopifyService.executeGraphQl(request.user, body.graphQl)
  }
}
