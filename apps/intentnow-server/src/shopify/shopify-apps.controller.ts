import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common'
import { ApiAuthGuard } from 'src/auth/api-auth-guard'
import { CrudQuery, CrudQueryDto } from '../dto/common.dto'
import { ParsedCrudQuery } from 'src/common/data-helper'
import { ShopifyAppsService } from './shopify-apps.service'
import {
  PaginatedShopifyAppsDto,
  ShopifyAppCreateDto,
  ShopifyAppDto,
  ShopifyAppUpdateDto,
} from 'src/dto/shopify.dto'

@UseGuards(ApiAuthGuard)
@Controller('api/intentnow')
export class ShopifyAppsController {
  constructor(private readonly shopifyAppsService: ShopifyAppsService) {}

  @Get('shopify-apps')
  async getList(
    @Query() q: CrudQueryDto, //Only for swagger
    @ParsedCrudQuery({
      sortFields: ['createdAt', 'name', 'appHandle'],
      filterFields: ['name', 'appHandle'],
    })
    query: CrudQuery
  ): Promise<PaginatedShopifyAppsDto> {
    const shopifyApps = await this.shopifyAppsService.getShopifyApps(query)
    return shopifyApps
  }

  @Post('shopify-apps')
  async createShopifyApp(
    @Body() dto: ShopifyAppCreateDto
  ): Promise<ShopifyAppDto> {
    return await this.shopifyAppsService.createShopifyApp(dto)
  }

  @Patch('shopify-apps/:id')
  async updateShopifyApp(
    @Param('id') id: string,
    @Body() dto: ShopifyAppUpdateDto
  ): Promise<ShopifyAppDto> {
    return await this.shopifyAppsService.updateShopifyApp(id, dto)
  }
}
