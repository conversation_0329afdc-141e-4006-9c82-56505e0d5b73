import { InjectModel } from '@nestjs/mongoose'
import { ShopifyAppDocument, ShopifyAppMongo } from './entities/shopify.mongo'
import { Injectable, Logger, NotFoundException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { PaginateModel } from 'mongoose'
import { UserPermissions } from 'src/users/user-permissions'
import { CrudQuery } from 'src/dto/common.dto'
import {
  buildMongoQuery,
  buildMongoUpdate,
  mapPaginatedMongoResultsToDto,
} from 'src/common/data-helper'
import {
  PaginatedShopifyAppsDto,
  ShopifyAppCreateDto,
  ShopifyAppDto,
  ShopifyAppUpdateDto,
} from 'src/dto/shopify.dto'
import { plainToInstance } from 'class-transformer'

@Injectable()
export class ShopifyAppsService {
  private readonly logger = new Logger(ShopifyAppsService.name)

  constructor(
    private readonly configService: ConfigService,
    private readonly userPermissions: UserPermissions,
    @InjectModel(ShopifyAppMongo.name)
    private readonly shopifyAppConfigModel: PaginateModel<ShopifyAppDocument>
  ) {}

  async getShopifyApps(crudQuery: CrudQuery): Promise<PaginatedShopifyAppsDto> {
    this.userPermissions.requiresAdmin()

    const { filter, options } = buildMongoQuery(
      this.shopifyAppConfigModel,
      crudQuery
    )
    const paginatedResults = await this.shopifyAppConfigModel.paginate(
      filter,
      options
    )

    return mapPaginatedMongoResultsToDto(
      paginatedResults,
      ShopifyAppDto,
      PaginatedShopifyAppsDto
    )
  }

  async createShopifyApp(dto: ShopifyAppCreateDto): Promise<ShopifyAppDto> {
    this.userPermissions.requiresAdmin()

    const newShopifyApp = new this.shopifyAppConfigModel({
      ...dto,
      createdAt: new Date(),
    })
    const created = await newShopifyApp.save()
    return plainToInstance(ShopifyAppDto, created.toObject())
  }

  async updateShopifyApp(
    id: string,
    dto: ShopifyAppUpdateDto
  ): Promise<ShopifyAppDto> {
    this.userPermissions.requiresAdmin()
    const updateObj = buildMongoUpdate(dto, {})

    const updated = await this.shopifyAppConfigModel.findByIdAndUpdate(
      id,
      {
        ...updateObj,
        updatedAt: new Date(),
      },
      {
        new: true,
      }
    )
    if (!updated) {
      throw new NotFoundException('ShopifyApp not found')
    }
    return plainToInstance(ShopifyAppDto, updated.toObject())
  }
}
