{
  "folders": [
    {
      "path": "apps/intentnow-server",
    },
    {
      "path": "apps/intentnow-web",
    },
    {
      "path": "packages/intentnow-tag",
    },
    {
      "path": "packages/shared-entities",
    },
    {
      "path": ".",
    },
  ],
  "settings": {
    "jest.disabledWorkspaceFolders": ["intentnow-monorepo"],
    "editor.tabSize": 2,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
    },
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "rvest.vs-code-prettier-eslint",
    //"i18n-ally.localesPaths": ["../intentnow-web/src/messages"],
    "i18n-ally.keystyle": "nested",
    "i18n-ally.extract.autoDetect": false,
    "[typescript]": {
      "editor.defaultFormatter": "rvest.vs-code-prettier-eslint",
    },
    "[javascript]": {
      "editor.defaultFormatter": "rvest.vs-code-prettier-eslint",
    },
    "[liquid]": {
      "editor.defaultFormatter": "Shopify.theme-check-vscode",
    },
  },
  "extensions": {
    "recommendations": [
      "dbaeumer.vscode-eslint",
      "orta.vscode-jest",
      "eamodio.gitlens",
      "styled-components.vscode-styled-components",
    ],
  },
}
