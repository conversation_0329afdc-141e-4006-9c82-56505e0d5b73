receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

processors:
  batch:
    # batch metrics before sending to reduce API usage
    send_batch_max_size: 200
    send_batch_size: 200
    timeout: 5s

  memory_limiter:
    # drop metrics if memory usage gets too high
    check_interval: 1s
    limit_percentage: 65
    spike_limit_percentage: 20

  # automatically detect Cloud Run resource metadata
  resourcedetection:
    detectors: [env, gcp]
    timeout: 2s
    override: false

  resource:
    attributes:
      - key: service.instance.id
        from_attribute: faas.id
        action: upsert
      - key: service.name
        # value: ${env:K_SERVICE}
        from_attribute: faas.name
        action: upsert

  # filter:
  #   error_mode: ignore
  #   metrics:
  # http_client_duration
  # http_server_duration
  # hello_counter
  # metric:
  #   - name != "http_server_duration"

exporters:
  googlemanagedprometheus: # Note: this is intentionally left blank
    # metric:
    #   resource_filters:
    #     - prefix: "cloud"
    #     - prefix: "k8s"
    #     - prefix: "faas"
    #     - regex: "container.id"
    #     - regex: "process.pid"
    #     - regex: "host.name"
    #     - regex: "host.id"
  googlecloud:
    log:
      default_log_name: opentelemetry.io/collector-exported-log
  # debug:
  #   verbosity: detailed

extensions:
  health_check:
    endpoint: "0.0.0.0:13133"

service:
  # telemetry:
  #   logs:
  #     level: "DEBUG"
  #     development: true
  #     encoding: "json"
  extensions: [health_check]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch, resourcedetection, resource]
      exporters: [googlecloud]
    metrics:
      receivers: [otlp]
      processors: [memory_limiter, batch, resourcedetection, resource]
      exporters: [googlemanagedprometheus]
