{"name": "intentnow-monorepo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "pnpm --filter @packages/shared-entities build && pnpm run --parallel dev", "dev:web": "pnpm --filter @packages/shared-entities build && pnpm --filter @apps/intentnow-web... run --parallel dev", "dev:server": "pnpm --filter @packages/shared-entities build && pnpm --filter @apps/intentnow-server... run --parallel dev", "shopify:dev": "pnpm --filter @packages/shared-entities build && pnpm run --parallel shopify:dev", "build": "pnpm run --parallel build", "watch-packages": "pnpm --filter '@packages/*' --parallel build --watch", "publish-dto": "pnpm --filter @apps/intentnow-server publish-dto", "test": "pnpm run --parallel test", "lint": "pnpm run --parallel lint", "format": "pnpm run --parallel format", "typecheck": "pnpm run --parallel typecheck", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "keywords": [], "author": "", "license": "ISC"}