# Docker Compose Override for Development
# This file provides development-specific configurations

services:
  # Development-specific MongoDB configuration
  mongodb:
    environment:
      # Enable MongoDB logging for development
      MONGO_LOG_LEVEL: info
    # Mount a local directory for database dumps/backups
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init.js:/docker-entrypoint-initdb.d/init.js:ro
      - ./docker/mongodb/dumps:/dumps

  # Development-specific Redis configuration
  redis:
    # Enable Redis logging
    command: redis-server --appendonly yes --loglevel notice

  # Enhanced Firebase emulator with UI
  firebase-emulator:
    environment:
      - FIREBASE_PROJECT=intentnow-dev
      - FIRESTORE_EMULATOR_HOST=0.0.0.0:8080

volumes:
  mongodb_data:
  redis_data: