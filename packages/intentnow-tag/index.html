<html>

<head>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>IntentNow Widget Test</title>
  <style>
    .container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .top-bar {
      width: 100%;
      ;
    }

    .select-store {
      padding: 2px;
      margin: 10px;
    }

    .container-2 {
      display: flex;
      height: 100%;
      min-height: 700px;
      min-width: 800px;
    }

    .left-panel {
      width: 200px;

    }

    .preview-panel {
      flex: 1;
      background-color: lightgray;
      border: #000 2px dotted;
      position: relative;
    }
  </style>

  <script src="../dist/scripts/intentnow-tag-local.js"></script>
</head>

<body>
  <div id="widget-root"></div>
  <div class="container">
    <div class="top-bar">
      <h2>IntentNow Widget Test Harness</h2>
      <!-- <select class="select-store" id="select-store">
        <option value="quickstart-1d36e8f9.myshopify.com">quickstart-1d36e8f9.myshopify.com</option>
        <option value="ilia-beauty-v2.myshopify.com">ilia-beauty-v2.myshopify.com'</option>
      </select> -->

    </div>
    <div class="container-2">
      <div class="left-panel" id="action-panel">
        <div>
          <button onclick="IntentnowTag.testWidget2('widget-root', '15% Off [1]')">
            test widget2 [discount 1]
          </button>
        </div>
        <div>
          <button onclick="IntentnowTag.testWidget2('widget-root', '15% Off [2]')">
            test widget2 [discount 2]
          </button>
        </div>
        <div>
          <button onclick="IntentnowTag.testWidget2('widget-root', undefined)">
            test widget2 [no discount]
          </button>
        </div>
        <div>
          <button onclick="IntentnowTag.testWidget3('widget-root', '15% Off [1]')">
            test widget3 [discount 1]
          </button>
        </div>
      </div>
      <div class="preview-panel" id="preview-root">
      </div>
    </div>
  </div>
  <script>
    // function getStore() {
    //   const select = document.getElementById('select-store')
    //   return select.value
    // }
  </script>
</body>

</html>