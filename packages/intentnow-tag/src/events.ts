import { getCookies, hash } from './utils'

export interface BaseEvent {
  id: string
  clientId: string
  eventSource: string
  name: string
  shop: string
  type: string
  timestamp: string
  data: any
  customer?: {
    customerId?: string
    hash?: string
  }
}

export interface ShopifyEvent extends BaseEvent {
  context: any
}

export interface IntentnowEvent extends BaseEvent {}

export interface RawEvent {
  name: string
  shop: string
  eventSource: string
  event: any
}
export interface EventSettings {
  shop: string
  eventSource: string
  eventApiUrl: string
  eventApiToken: string
  batchTimeoutInMs?: number
  batchTimeoutHighPriorityInMs?: number
  customerId?: string
  customerEmail?: string
  customerEmailHash?: string
  logging?: boolean
  storeEvents?: boolean
  browser?: {
    localStorage: any
    sessionStorage: any
  }
}

let eventSettings: EventSettings | undefined = undefined

//Exported for debugging purpose
export const eventQueue: (BaseEvent | RawEvent)[] = []
const intentnowStorageEventQueueKey = 'intentnow_event_queue'

async function queueEvent(event: BaseEvent | RawEvent, highPriority: boolean) {
  if (!eventSettings) {
    console.debug('Event settings not initialized, event dropped: ', event.name)
    return
  }

  try {
    eventQueue.push(event)

    if (eventSettings.storeEvents) {
      if (eventSettings.browser?.sessionStorage) {
        await eventSettings.browser.sessionStorage.setItem(
          intentnowStorageEventQueueKey,
          JSON.stringify({ events: eventQueue })
        )
      } else {
        sessionStorage.setItem(
          intentnowStorageEventQueueKey,
          JSON.stringify({ events: eventQueue })
        )
      }
    }

    if (!eventSettings.batchTimeoutInMs) {
      await batchSendEvents()
    } else {
      const timeout = highPriority
        ? eventSettings.batchTimeoutHighPriorityInMs
        : eventSettings.batchTimeoutInMs

      setTimeout(async () => {
        await batchSendEvents()
      }, timeout)
    }
  } catch (error) {
    eventSettings.logging && console.error('Error queuing event:', error)
  }
}

async function batchSendEvents(type = 'batch') {
  if (!eventSettings) {
    console.debug('Event settings not initialized')
    return
  }

  try {
    const events: (BaseEvent | RawEvent)[] = eventQueue.slice(0)
    eventQueue.length = 0

    if (eventSettings.storeEvents) {
      if (eventSettings.browser?.sessionStorage) {
        await eventSettings.browser.sessionStorage.removeItem(
          intentnowStorageEventQueueKey
        )
      } else {
        sessionStorage.removeItem(intentnowStorageEventQueueKey)
      }
    }

    if (!events.length) {
      eventSettings.logging && console.debug(`empty event queue`)
      return
    }

    eventSettings.logging &&
      console.debug(`sending ${events.length} events`, events)
    const dataToSend = JSON.stringify({ events, type })
    const response = await fetch(eventSettings.eventApiUrl, {
      keepalive: true,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${eventSettings.eventApiToken}`,
      },
      body: dataToSend,
    })

    if (response.ok) {
      eventSettings.logging && console.debug('events tracked')
    } else {
      eventSettings.logging && console.error('events tracking failed')
    }
  } catch (error) {
    eventSettings.logging && console.error('Error sending events', error)
  }
}

async function restoreEventsFromStorage() {
  if (!eventSettings) {
    console.debug('Event settings not initialized')
    return
  }

  if (!eventSettings.storeEvents) {
    eventSettings.logging && console.debug(`storeEvents setting is not set`)
    return
  }

  try {
    eventSettings.logging &&
      console.debug('start restoring events from storage')

    let value: string | undefined | null
    if (eventSettings.browser) {
      value = await eventSettings.browser.sessionStorage.getItem(
        intentnowStorageEventQueueKey
      )
      await eventSettings.browser.sessionStorage.removeItem(
        intentnowStorageEventQueueKey
      )
    } else {
      value = sessionStorage.getItem(intentnowStorageEventQueueKey)
      sessionStorage.removeItem(intentnowStorageEventQueueKey)
    }

    let restored = false
    if (value) {
      const jsonValue = JSON.parse(value)
      if (jsonValue?.events) {
        const restoredEvents = jsonValue.events
        restored = restoredEvents.length > 0
        eventSettings.logging &&
          console.debug(
            `${restoredEvents.length} events restored: `,
            restoredEvents
          )

        //put the events to the top of the queue
        const newEvents = [...restoredEvents, ...eventQueue]
        eventQueue.length = 0
        eventQueue.push(...newEvents)
      }
    }

    if (restored) {
      eventSettings.logging &&
        console.debug(`start to batchSend the restored events`)
      await batchSendEvents('batchOnInit')
    }

    eventSettings.logging && console.debug(`restoring done`)
  } catch (error) {
    eventSettings.logging &&
      console.error(`Failed to restore events from storage`, error)
  }
}

function removePersonalInfoFromShopifyEvent(originalEvent: any) {
  const event = { ...originalEvent }
  if (event.type === 'dom') {
    delete event.data
    return event
  }

  if (event.data && event.data.checkout) {
    const allowedKeys = [
      'discountApplications',
      'discountsAmount',
      'lineItems',
      'subtotalPrice',
      'totalPrice',
      'totalTax',
      'billingAddress',
      'shippingAddress',
      'order',
    ]

    const allowedAddressKeys = [
      'city',
      'province',
      'provinceCode',
      'country',
      'zip',
    ]

    const checkoutData = Object.fromEntries(
      Object.entries(event.data.checkout)
        .filter(([key]) => allowedKeys.includes(key))
        .map(([key, value]) => {
          if (key === 'billingAddress' || key === 'shippingAddress') {
            return [
              key,
              Object.fromEntries(
                Object.entries(value as any).filter(([addressKey]) =>
                  allowedAddressKeys.includes(addressKey)
                )
              ),
            ]
          }
          return [key, value]
        })
    )
    event.data = {
      ...event.data,
      checkout: checkoutData,
    }
  }

  return event
}

export async function initializeEvents(settings: EventSettings) {
  if (typeof window !== 'undefined' && typeof document !== 'undefined') {
    const params = new URLSearchParams(window.location.search)
    const preview = params.get('intentnow-preview')
    if (preview) {
      settings.logging && console.debug('preview on, events disabled')
      return
    }
  }

  //TODO: validate settings values
  settings.logging && console.debug('eventSettings', settings)
  eventSettings = settings

  if (eventSettings.storeEvents) {
    //Check if there are left-over events in the session storage
    eventSettings.logging &&
      console.debug(`Check left-over events in session storage`)
    await restoreEventsFromStorage()
  }

  if (eventSettings.customerEmail) {
    eventSettings.customerEmailHash = await hash(eventSettings.customerEmail)
  }

  if (eventSettings.batchTimeoutInMs) {
    if (!eventSettings.batchTimeoutHighPriorityInMs) {
      eventSettings.batchTimeoutHighPriorityInMs =
        eventSettings.batchTimeoutInMs
    }

    //Subscribe to all events below to improve the reliability of sending out remaining events in queue when current page is being unloaded.
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      eventSettings.logging && console.debug(`Set up page unload processing`)

      document.addEventListener('visibilitychange', () => {
        eventSettings?.logging &&
          console.debug(`visibilitychange event => `, document.visibilityState)
        if (document.visibilityState === 'hidden') {
          batchSendEvents('batchOnUnload')
        }
      })

      window.addEventListener('pagehide', () => {
        eventSettings?.logging && console.debug(`pagehide event`)
        batchSendEvents('batchOnUnload')
      })

      window.addEventListener('beforeunload', () => {
        eventSettings?.logging && console.debug(`beforeunload event`)
        batchSendEvents('batchOnUnload')
      })

      window.addEventListener('unload', () => {
        eventSettings?.logging && console.debug(`unload event`)
        batchSendEvents('batchOnUnload')
      })
    }
  }
}

const highPriorityShopifyEventTypes = ['standard']

export async function trackShopifyEvent(originalEvent: ShopifyEvent) {
  if (!eventSettings) {
    console.debug(
      'Event settings not initialized, shopify event dropped: ',
      originalEvent.name
    )
    return
  }

  eventSettings.logging &&
    console.debug(`trackShopifyEvent: ${originalEvent.name}`)

  if (!originalEvent.clientId) {
    eventSettings.logging && console.debug('missing clientId')
    return
  }

  if (!eventSettings.customerEmail) {
    const email = (originalEvent as any).data?.checkout?.email
    if (email) {
      eventSettings.customerEmail = email
    }
  }

  if (eventSettings.customerEmail && !eventSettings.customerEmailHash) {
    eventSettings.customerEmailHash = await hash(eventSettings.customerEmail)
  }

  const event = removePersonalInfoFromShopifyEvent(originalEvent)
  event.eventSource = eventSettings.eventSource
  event.shop = eventSettings.shop

  if (eventSettings.customerId || eventSettings.customerEmailHash) {
    event.customer = {
      customerId: eventSettings.customerId,
      hash: eventSettings.customerEmailHash,
    }
  }

  const highPriority = highPriorityShopifyEventTypes.includes(event.type)
  queueEvent(event, highPriority)
}

export async function trackIntentnowEvent(
  eventName: string,
  eventData: any,
  highPriority = false
) {
  if (!eventSettings) {
    console.debug(
      'Event settings not initialized, intentnow event dropped: ',
      eventName
    )
    return
  }

  eventSettings.logging && console.debug(`trackIntentnowEvent: ${eventName}`)

  if (eventSettings.customerEmail && !eventSettings.customerEmailHash) {
    eventSettings.customerEmailHash = await hash(eventSettings.customerEmail)
  }

  const shop = eventSettings.shop
  if (!shop) {
    eventSettings.logging && console.error('shop is not defined')
    return
  }

  const customerId = eventSettings.customerId
  const customerEmailHash = eventSettings.customerEmailHash

  const eventApiUrl = eventSettings.eventApiUrl
  if (!eventApiUrl) {
    eventSettings.logging && console.error('eventApiUrl is not defined')
    return
  }

  const cookies = getCookies()
  const clientId = cookies['_shopify_y']

  if (!clientId) {
    eventSettings.logging && console.debug('missing clientId')
    return
  }

  const event: IntentnowEvent = {
    id: crypto.randomUUID(),
    clientId: clientId,
    name: eventName,
    data: eventData,
    shop: shop,
    timestamp: new Date().toISOString(),
    eventSource: 'intentnow-promo-widget',
    type: 'custom-intentnow',

    customer:
      customerId || customerEmailHash
        ? {
            customerId,
            hash: customerEmailHash,
          }
        : undefined,
  }

  queueEvent(event, highPriority)
}

async function queueGTagEvent(event: any) {
  if (!eventSettings) {
    console.debug('Event settings not initialized, gtag event dropped')
    return
  }

  try {
    if (!event?.event || (event.event as string).startsWith('gtm.')) {
      return
    }

    queueEvent(
      {
        name: event.event as string,
        shop: eventSettings.shop,
        eventSource: eventSettings.eventSource,
        event,
      },
      false
    )
  } catch (e) {
    eventSettings.logging && console.error('queueGTagEvent failed', e)
  }
}

;(function () {
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return
  }

  const scripts = document.getElementsByTagName('script')
  let eventSource: string | null | undefined
  let shop: string | null | undefined
  let env: string | null | undefined
  for (let i = scripts.length - 1; i >= 0; i--) {
    const source = scripts[i].src
    if (
      source.indexOf('intentnow.com') >= 0 &&
      source.indexOf('intentnow-tag') >= 0 &&
      source.indexOf('intentnow-event-source') >= 0
    ) {
      console.debug('intentnow script found:', source)
      const params = new URL(source).searchParams
      shop = params.get('shop')
      eventSource = params.get('intentnow-event-source')
      env = params.get('env')
      console.debug(`shop=${shop}, eventSource=${eventSource}`)
      break
    }
  }

  if (shop && eventSource) {
    console.debug('IntentNow eventSource', eventSource)
    if (eventSource === 'gtag') {
      if (typeof window !== 'undefined') {
        const eventApiUrl =
          env === 'staging'
            ? 'https://intentnow-server-staging-1-639359072775.us-central1.run.app/api/intentnow/raw-events'
            : 'https://api2.intentnow.com/api/intentnow/raw-events'
        initializeEvents({
          shop,
          eventSource,
          eventApiUrl,
          eventApiToken: '',
          logging: true,
        })

        const windowObj: any = window
        windowObj.dataLayer = windowObj.dataLayer || []
        const oldPush = windowObj.dataLayer.push
        windowObj.dataLayer.push = function (...args: any[]) {
          const states = [].slice.call(args, 0)
          console.debug('dataLayer.push', states)
          queueGTagEvent(states[0])
          return oldPush.apply(windowObj.dataLayer, states)
        }
      }
    }
  }
})()
