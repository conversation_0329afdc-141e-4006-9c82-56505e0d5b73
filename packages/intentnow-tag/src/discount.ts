import { GetDiscountResponseDto } from '@packages/shared-entities'
import { getCookies, sleep } from './utils'

export async function getDiscount({
  shop,
  baseApiUrl,
  logging,
  mockGetDiscount,
  preview,
  previewWidgetId,
}: {
  shop: string
  baseApiUrl: string
  logging?: boolean
  mockGetDiscount?: GetDiscountResponseDto

  preview?: boolean
  previewWidgetId?: string
}): Promise<GetDiscountResponseDto> {
  logging && console.debug('getDiscount called', shop)
  logging && console.debug('baseApiUrl', baseApiUrl)

  if (mockGetDiscount) {
    // Mock response for testing purpose
    logging && console.debug('getDiscount mocked')
    await sleep(1000)
    return mockGetDiscount
  }

  const cookies = getCookies()
  let clientId = cookies['_shopify_y']

  if (!clientId) {
    if (preview) {
      clientId = 'preview-client-id'
    } else {
      logging && console.debug('missing clientId')
      return {}
    }
  }

  const body = {
    shop,
    clientId,
    preview,
    previewWidgetId,
  }

  const isTest = baseApiUrl.startsWith('https://api.intentnow.com')
  const getDiscountUrl = isTest
    ? `${baseApiUrl}/get-discount?shop=${shop}`
    : `${baseApiUrl}/get-discount`

  const response = await fetch(getDiscountUrl, {
    method: 'POST',
    body: JSON.stringify(body),
    redirect: 'manual',
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      //To support local testing through ngrok (a https proxy service)
      'ngrok-skip-browser-warning': 'true',
    },
  })

  if (response.ok) {
    const resBody = await response.json()
    logging && console.debug('discount response', resBody)
    return resBody
  } else {
    return {}
  }
}
