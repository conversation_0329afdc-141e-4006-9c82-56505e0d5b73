import { trackIntentnowEvent } from './events'

export function getKlaviyoStateInternal(
  settings: { logging?: boolean } | undefined
) {
  if (!settings) {
    return undefined
  }

  try {
    const klaviyoStateBlob = localStorage.getItem('klaviyoOnsite')
    if (klaviyoStateBlob) {
      const kalviyoState = JSON.parse(klaviyoStateBlob)
      settings.logging && console.debug('klaviyoState', kalviyoState)
      return kalviyoState
    }
  } catch (e) {
    settings.logging && console.error('getKlaviyoState error', e)
  }
}

let attentiveOfferHidden = false
let kalviyoOfferHidden = false
export function hideWelcomeOfferInternal(
  settings:
    | {
        shop: string
        logging?: boolean
      }
    | undefined
) {
  if (!settings) {
    return
  }
  const logging = settings.logging

  if (settings.shop === 'ilia-beauty-v2.myshopify.com') {
    const attentiveNode = document.getElementById('attentive_overlay')
    if (attentiveNode) {
      logging && console.debug('hiding attentive offer')
      attentiveNode.style.display = 'none'
      if (!attentiveOfferHidden) {
        attentiveOfferHidden = true
        trackIntentnowEvent('intentnow-promo-conflict', { app: 'attentive' })
      }
    }
  } else if (
    [
      'quickstart-1d36e8f9.myshopify.com',
      'toupees-by-thomas.myshopify.com',
      'gourmandbeauty-com.myshopify.com',
      'truwestern.myshopify.com',
    ].includes(settings.shop)
  ) {
    const observer = new MutationObserver((mutations) => {
      let interrupted = false
      mutations.forEach((mutation) => {
        if (interrupted) {
          return
        }

        if (mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (interrupted) {
              return
            }

            if (node.nodeType === Node.ELEMENT_NODE) {
              logging && console.debug('New element added:', node)
              const attributes = (node as Element).attributes
              if (attributes) {
                Array.from(attributes).forEach((attr) => {
                  if (interrupted) {
                    return
                  }

                  if (attr.name && attr.value) {
                    logging && console.log(`${attr.name} = ${attr.value}`)
                    if (
                      attr.name === 'class' &&
                      attr.value == 'needsclick  kl-private-reset-css-Xuajs1'
                    ) {
                      logging && console.debug(`klaviyo offer added`)
                      ;(node as any).style.display = 'none'

                      observer.disconnect()
                      interrupted = true

                      if (!kalviyoOfferHidden) {
                        kalviyoOfferHidden = true
                        trackIntentnowEvent('intentnow-promo-conflict', {
                          app: 'klaviyo',
                        })
                      }
                    }
                  }
                })
              }
            }
          })
        }
      })
    })

    // Start observing the target node (e.g., document.body) for configured mutations
    observer.observe(document.body, {
      childList: true, // Watch for direct children being added or removed
      subtree: true, // Also watch the descendants of the target node
    })

    logging && console.debug('detecting klaviyo offer for once')
    const klaviyoNode = document.querySelector('.go1272136950')
    if (klaviyoNode?.parentElement) {
      logging && console.debug('hiding klaviyo offer')
      klaviyoNode.parentElement.style.display = 'none'
      if (!kalviyoOfferHidden) {
        kalviyoOfferHidden = true
        trackIntentnowEvent('intentnow-promo-conflict', { app: 'klaviyo' })
      }
    }

    const klaviyoTeasers = [
      document.querySelector('.kl-teaser-XEhYW3'), //Gourmand
      document.querySelector('.kl-teaser-UGGuAc'), //Truwestern
      document.querySelector('.kl-teaser-VdMH4y'), //Quickstart
      document.querySelector('.kl-teaser-SircFj'), //Toupees-by-Thomas
    ]
    klaviyoTeasers.forEach((teaserNode) => {
      if (teaserNode) {
        logging && console.debug('hiding klaviyo teaser')
        ;(teaserNode as any).style.display = 'none'
        if (!kalviyoOfferHidden) {
          kalviyoOfferHidden = true
          trackIntentnowEvent('intentnow-promo-conflict', { app: 'klaviyo' })
        }
      }
    })

    //Disable DOM listener after 10 seconds
    setTimeout(() => {
      logging && console.debug('DOM listener disconnected')
      observer.disconnect()
    }, 10000)
  }
}
