import { GetDiscountResponseDto } from '@packages/shared-entities'

export interface PromoWidgetContent extends GetDiscountResponseDto {}

export interface IntentnowSettings {
  shop: string
  baseApiUrl: string
  delayTimeMs?: number
  hideWelcomeOffer?: boolean

  preview?: boolean
  previewWidgetId?: string

  logging?: boolean
  isEnabled: () => boolean

  //Mock getDiscount response for testing purposes
  mockGetDiscount?: GetDiscountResponseDto | {}
}

export enum PreviewMode {
  previewTeaser = 'previewTeaser',
  previewTeaserMobile = 'previewTeaserMobile',
  previewDialog = 'previewDialog',
  previewDialogMobile = 'previewDialogMobile',
  previewBoth = 'previewBoth',
  previewBothMobile = 'previewBothMobile',
}

export interface PromoWidgetSettings {
  previewMode?: PreviewMode
}

export interface IntentnowSessionState {
  minimized?: boolean
  closed?: boolean
  lastShown?: number
  discount?: PromoWidgetContent
}

export interface IntetnowPreviewState {
  lastPreviewAt?: number
  previewWidgetId?: string
}
