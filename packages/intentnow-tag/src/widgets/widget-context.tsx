import React from 'react'
import { usePromoWidgetManager } from './manager'

type PromoWidgetProviderType = ReturnType<typeof usePromoWidgetManager>

export const PromoWidgetContext = React.createContext<
  PromoWidgetProviderType | undefined
>(undefined)

export const PromoWidgetProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const context = usePromoWidgetManager()

  return (
    <PromoWidgetContext.Provider value={context}>
      {children}
    </PromoWidgetContext.Provider>
  )
}

export const usePromoWidgetContext = () => {
  const context = React.useContext(PromoWidgetContext)
  if (!context) {
    throw new Error(
      'usePromoWidgetContext must be used within a PromoWidgetProvider'
    )
  }
  return context
}
