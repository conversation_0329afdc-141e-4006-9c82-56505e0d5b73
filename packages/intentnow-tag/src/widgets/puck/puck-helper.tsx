import { defaultFontMap } from '@packages/shared-entities'
import {
  Box1Props,
  ButtonColorsProps,
  ColorsProps,
  FontProps,
  SizeProps,
  Box3Props,
} from './puck-props'

export function genColorsCss(colors?: ColorsProps) {
  return `
    color: ${colors?.color};
    background-color: ${colors?.backgroundColor};
  `
}

export function genButtonColorsCss(colors?: ButtonColorsProps) {
  return `
    ${genColorsCss(colors)}
    &:hover {
      background-color: ${colors?.hoverBackgroundColor};
    }
  `
}

export function genFontCss(font?: FontProps) {
  const fontConfig = font?.name ? defaultFontMap.get(font.name) : undefined

  return `
    font-family: ${fontConfig?.fontFamily?.map((f) => `"${f}"`).join(',')};
    font-size: ${font?.size && `${font.size}px`};
    font-style: normal;
    font-variant: normal;
    font-weight: ${font?.weight ?? 'normal'};
    line-height: normal;
  `
}

export function genBox1Css(box?: Box1Props) {
  return `
    padding-left: ${box?.paddingLeft ?? 0}px;
    padding-right: ${box?.paddingRight ?? 0}px;
    padding-top: ${box?.paddingTop ?? 0}px;
    padding-bottom: ${box?.paddingBottom ?? 0}px;
  `
}

export function genBox2Css(box?: Box3Props) {
  return `
    margin-left: ${box?.marginLeft ?? 0}px;
    margin-right: ${box?.marginRight ?? 0}px;
    margin-top: ${box?.marginTop ?? 0}px;
    margin-bottom: ${box?.marginBottom ?? 0}px;
  `
}

export function genBox3Css(box?: Box3Props) {
  return `
    ${genBox1Css(box)}
    ${genBox2Css(box)}
  `
}

export function genSizeCss(size?: SizeProps) {
  return `
    ${Boolean(size?.width) && `min-width: ${size!.width}px;`}
    ${Boolean(size?.height) && `min-height: ${size!.height}px;`}
  `
}
