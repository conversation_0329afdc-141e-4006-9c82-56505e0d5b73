import { PromoWidgetAction } from '@packages/shared-entities'

export type ColorsProps = {
  color?: string
  backgroundColor?: string
}

export type ButtonColorsProps = ColorsProps & {
  hoverBackgroundColor?: string
}

export type FontProps = {
  name?: string
  size?: number
  weight?: string
}

export type SizeProps = {
  width?: number
  height?: number
}

export type Box1Props = {
  paddingTop?: number
  paddingBottom?: number
  paddingLeft?: number
  paddingRight?: number
}

export type Box2Props = {
  marginTop?: number
  marginBottom?: number
  marginLeft?: number
  marginRight?: number
}

export type Box3Props = Box1Props & Box2Props

export type ImageComponentProps = {
  imageUrl: string
  styles?: {
    size?: SizeProps
    box?: Box2Props
  }
}

export type TextComponentProps = {
  text: string
  styles: {
    align: 'left' | 'center' | 'right'
    colors?: ColorsProps
    font?: FontProps
    box?: Box2Props
  }
}

export enum ButtonVariant {
  default = 'default',
  link = 'link',
}

export type ButtonComponentProps = {
  text: string
  action?: {
    action?: PromoWidgetAction | undefined
    actionTarget?: string | undefined
  }
  styles: {
    variant: ButtonVariant
    colors?: ButtonColorsProps
    font?: FontProps
    size?: SizeProps
    box?: Box3Props
  }
}

export enum BorderStyle {
  none = 'none',
  solid = 'solid',
  dashed = 'dashed',
  dotted = 'dotted',
}

export type ContainerComponentProps = {
  styles?: {
    border: {
      style: BorderStyle
      width?: number
      color?: string
    }
    colors?: ColorsProps
    box?: Box3Props
  }
}

export type DiaglogContainerComponentProps = {
  image: {
    imageUrl: string
    desktopImagePostion: 'left' | 'right'
    showMobileImage: boolean
  }
}

export type Widget3DialogProps = {
  minimizeButton?: {
    color?: string
  }
  styles: {
    colors: ColorsProps
    font: FontProps
  }
}

export type Widget3DialogComponentsProps = {
  DialogContainer: DiaglogContainerComponentProps
  Container: ContainerComponentProps
  Text: TextComponentProps
  Image: ImageComponentProps
  Button: ButtonComponentProps
}

export type Widget3TeaserProps = {
  position?: {
    verticalGap?: number
    horizontalGap?: number
  }
  styles: {
    colors: ColorsProps
    font: FontProps
  }
}

export type Widget3TeaserComponentsProps = {
  Text: TextComponentProps
  Image: ImageComponentProps
}
