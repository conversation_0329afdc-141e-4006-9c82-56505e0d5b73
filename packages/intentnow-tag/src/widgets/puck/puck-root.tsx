import { Config, PuckContext } from '@measured/puck'
import {
  Widget3DialogProps,
  Widget3DialogComponentsProps,
  Widget3TeaserComponentsProps,
  Widget3TeaserProps,
} from './puck-props'
import {
  ImageComponent,
  TextComponent,
  ButtonComponent,
  DialogContainerComponent,
  ColorsField,
  FontField,
  ContainerComponent,
} from './puck-components'
import { PuckProvider, usePuckContext } from './puck-context'
import { css, Global } from '@emotion/react'
import { usePromoWidgetContext } from '../widget-context'
import { ReactNode, useEffect } from 'react'
import { genColorsCss } from './puck-helper'

export const widget3DialogPuckConfig: Config<
  Widget3DialogComponentsProps,
  Widget3DialogProps
> = {
  components: {
    DialogContainer: DialogContainerComponent,
    Container: ContainerComponent,
    Text: TextComponent,
    Image: ImageComponent,
    Button: ButtonComponent,
  },
  root: {
    fields: {
      minimizeButton: {
        label: 'Minimize Button',
        type: 'object',
        objectFields: {
          color: {
            type: 'text',
            label: 'Color',
          },
        },
      },
      styles: {
        label: 'Styles',
        type: 'object',
        objectFields: {
          colors: ColorsField,
          font: FontField,
        },
      },
    },
    defaultProps: {
      styles: {
        colors: {
          color: 'black',
          backgroundColor: 'white',
        },
        font: {
          name: 'Arial',
          size: 20,
        },
      },
    },
    render: ({ puck, children, minimizeButton, styles }) => {
      return (
        <PuckProvider puck={puck}>
          <Widget3DialogRootRenderInner
            puck={puck}
            minimizeButton={minimizeButton}
            styles={styles}
          >
            {children as ReactNode}
          </Widget3DialogRootRenderInner>
        </PuckProvider>
      )
    },
  },
}

function Widget3DialogRootRenderInner({
  puck,
  minimizeButton,
  styles,
  children,
}: {
  puck: PuckContext
  minimizeButton: Widget3DialogProps['minimizeButton']
  styles: Widget3DialogProps['styles']
  children: React.ReactNode
}) {
  const { isMobile, setDefaultFont, setDefaultColors } = usePuckContext()
  const { minimizeDialog } = usePromoWidgetContext()

  useEffect(() => {
    if (styles) {
      setDefaultColors(styles.colors)
      setDefaultFont(styles.font)
    }
  }, [styles])

  return (
    <div
      css={css`
        ${!puck.isEditing && `z-index: 90001;`}
        position: ${puck.isEditing ? 'absolute' : 'fixed'};

        max-width: 810px;
        width: 90%;
        max-height: auto;
        height: auto;

        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        ${genColorsCss(styles.colors)}
      `}
    >
      {children}
      <div
        id="minimize-button-wrapper"
        css={css`
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
          position: absolute;
          top: 11px;
          right: ${isMobile ? '11px' : '14px'};
        `}
      >
        <button
          id="minimize-button"
          css={css`
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            border: 0;
            cursor: pointer;
            background-color: transparent;

            stroke: ${minimizeButton?.color || styles.colors.color};

            ${isMobile &&
            ` width: 15px;
                height: 15px;
                padding: 2px;`}
          `}
          onClick={() => {
            minimizeDialog()
          }}
        >
          <svg
            width="15"
            height="5"
            viewBox="0 0 9 5"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <line
              y1="-0.5"
              x2="15"
              y2="-0.5"
              transform="matrix(-1 0 0.0867925 -0.996226 9 0)"
              strokeWidth="4"
            />
          </svg>
        </button>
      </div>
    </div>
  )
}

export const widget3TeaserPuckConfig: Config<
  Widget3TeaserComponentsProps,
  Widget3TeaserProps
> = {
  components: {
    Text: TextComponent,
    Image: ImageComponent,
  },
  root: {
    fields: {
      position: {
        label: 'Position',
        type: 'object',
        objectFields: {
          verticalGap: { type: 'number', label: 'Vertical Gap' },
          horizontalGap: { type: 'number', label: 'Horizontal Gap' },
        },
      },
      styles: {
        label: 'Styles',
        type: 'object',
        objectFields: {
          colors: ColorsField,
          font: FontField,
        },
      },
    },
    defaultProps: {
      styles: {
        colors: {
          color: 'white',
          backgroundColor: 'black',
        },
        font: {
          name: 'Arial',
          size: 13,
        },
      },
    },
    render: ({ puck, position, styles, children }) => {
      return (
        <>
          <PuckProvider puck={puck}>
            <Widget3TeaserRootRenderInner
              puck={puck}
              position={position}
              styles={styles}
            >
              {children as ReactNode}
            </Widget3TeaserRootRenderInner>
          </PuckProvider>
        </>
      )
    },
  },
}

function Widget3TeaserRootRenderInner({
  puck,
  position,
  styles,
  children,
}: {
  puck: PuckContext
  position: Widget3TeaserProps['position']
  styles: Widget3TeaserProps['styles']
  children: React.ReactNode
}) {
  const { isMobile, setDefaultFont, setDefaultColors } = usePuckContext()
  const { restoreDialog } = usePromoWidgetContext()

  useEffect(() => {
    if (styles) {
      setDefaultColors(styles.colors)
      setDefaultFont(styles.font)
    }
  }, [styles])

  return (
    <>
      <Global
        styles={css`
          @keyframes intentnow-teaser-slidein {
            0% {
              transform: translateX(-100%);
            }

            100% {
              transform: translateX(0);
            }
          }
        `}
      />

      <div
        id="intentnow-teaser"
        css={css`
          position: ${puck.isEditing ? 'absolute' : 'fixed'};
          display: flex;
          align-items: center;
          justify-content: center;
          left: ${position?.horizontalGap ?? 0}px;
          bottom: ${position?.verticalGap ?? 0}px;
          min-width: 180px;
          width: 180px;
          min-height: 120px;
          padding: 10px;

          ${puck.isEditing
            ? ''
            : `animation: intentnow-teaser-slidein 0.4s ease-in-out forwards;`}
          ${puck.isEditing ? '' : 'z-index: 90001;'}

          ${genColorsCss(styles.colors)}

          ${isMobile &&
          `
            padding: 10px;
            width: auto;
            min-width: 120px;
            min-height: 40px;
          `}
        `}
        onClick={() => {
          restoreDialog()
        }}
      >
        {children}
      </div>
    </>
  )
}
