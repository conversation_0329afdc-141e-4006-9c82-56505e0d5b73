import { PuckContext, usePuck } from '@measured/puck'
import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react'
import { ColorsProps, FontProps } from './puck-props'
import { usePromoWidgetContext } from '../widget-context'
import { PreviewMode } from '../../entities'
import { css } from '@emotion/react'

const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  })

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    window.addEventListener('resize', handleResize)

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return { screenSize }
}

function usePuckManager({ puck: pk }: { puck: PuckContext }) {
  const [puck, setPuck] = useState<PuckContext>(pk)
  const [widgetContext, setWidgetContext] =
    useState<ReturnType<typeof usePromoWidgetContext>>()
  const [screenSize, setScreenSize] = useState<{
    width: number
    height: number
  }>()
  const [isMobile, setIsMobile] = useState(false)
  const [defaultFont, setDefaultFont] = useState<FontProps>()
  const [defaultColors, setDefaultColors] = useState<ColorsProps>()

  return {
    puck,
    setPuck,
    widgetContext,
    setWidgetContext,
    screenSize,
    setScreenSize,
    isMobile,
    setIsMobile,
    defaultFont,
    setDefaultFont,
    defaultColors,
    setDefaultColors,
  }
}

type PuckProviderType = ReturnType<typeof usePuckManager>

export const PuckComponentContext = createContext<PuckProviderType | undefined>(
  undefined
)

export function PuckProviderInternal({
  children,
  puck,
}: {
  children: ReactNode
  puck: PuckContext
}) {
  const context = usePuckManager({ puck })
  const widgetContext = usePromoWidgetContext()

  useEffect(() => {
    context.setWidgetContext(widgetContext)
  }, [widgetContext])

  if (!widgetContext) {
    return <></>
  }

  return (
    <PuckComponentContext.Provider value={context}>
      {children}
    </PuckComponentContext.Provider>
  )
}

export function usePuckContext() {
  const context = useContext(PuckComponentContext)
  if (!context) {
    throw new Error('usePuckContext must be used within a PuckProvider')
  }
  return context
}

export function PuckEditWrapper({ children }: { children: ReactNode }) {
  const puckEdit = usePuck()
  const { widgetContext, setIsMobile, setScreenSize } = usePuckContext()

  useEffect(() => {
    setScreenSize({
      width: puckEdit.appState.ui.viewports.current.width,
      height:
        typeof puckEdit.appState.ui.viewports.current.height === 'number'
          ? puckEdit.appState.ui.viewports.current.height
          : 0,
    })
  }, [puckEdit.appState, setScreenSize])

  useEffect(() => {
    if (widgetContext?.settings?.previewMode) {
      if (
        [
          PreviewMode.previewDialogMobile,
          PreviewMode.previewTeaserMobile,
          PreviewMode.previewBothMobile,
        ].includes(widgetContext?.settings?.previewMode)
      ) {
        setIsMobile(true)
      } else {
        setIsMobile(false)
      }
    }
  }, [widgetContext?.settings?.previewMode])

  return (
    <>
      <div
        css={css`
          width: 100vw;
          height: 100vh;
        `}
        onClick={() => {
          //when user click on the blank preview area, clear the selected item
          if (puckEdit.appState.ui) {
            puckEdit.dispatch({
              type: 'setUi',
              ui: { itemSelector: null },
            })
          }
        }}
      >
        {children}
      </div>
    </>
  )
}

export function PuckRenderWrapper({ children }: { children: ReactNode }) {
  const { screenSize } = useScreenSize()
  const { setScreenSize, setIsMobile } = usePuckContext()

  useEffect(() => {
    setScreenSize(screenSize)
    const mobile = screenSize.width < 600
    setIsMobile(mobile)
  }, [screenSize])

  return <>{children}</>
}

export function PuckProvider({
  puck,
  children,
}: {
  puck: PuckContext
  children: ReactNode
}) {
  if (puck.isEditing) {
    return (
      <PuckProviderInternal puck={puck}>
        <PuckEditWrapper>{children}</PuckEditWrapper>
      </PuckProviderInternal>
    )
  }

  return (
    <PuckProviderInternal puck={puck}>
      <PuckRenderWrapper>{children}</PuckRenderWrapper>
    </PuckProviderInternal>
  )
}
