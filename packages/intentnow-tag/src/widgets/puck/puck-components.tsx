import { css } from '@emotion/react'
import { usePuckContext } from './puck-context'
import { ComponentConfig, DropZone, ObjectField } from '@measured/puck'
import { useEffect, useMemo } from 'react'
import { defaultFontList, PromoWidgetAction } from '@packages/shared-entities'
import {
  Box2Props,
  Box1Props,
  ButtonColorsProps,
  ButtonComponentProps,
  ColorsProps,
  DiaglogContainerComponentProps,
  FontProps,
  ImageComponentProps,
  SizeProps,
  TextComponentProps,
  Box3Props,
  ButtonVariant,
  ContainerComponentProps,
  BorderStyle,
} from './puck-props'
import {
  genBox2Css,
  genBox3Css,
  genButtonColorsCss,
  genColorsCss,
  genFontCss,
  genSizeCss,
} from './puck-helper'
import { usePromoWidgetContext } from '../widget-context'

export const ColorsField: ObjectField<ColorsProps> = {
  label: 'Colors',
  type: 'object',
  objectFields: {
    color: {
      //...ColorField,
      type: 'text',
      label: 'Color',
    },
    backgroundColor: {
      //...ColorField,
      type: 'text',
      label: 'Background Color',
    },
  },
}

export const ButtonColorsField: ObjectField<ButtonColorsProps> = {
  label: 'Colors',
  type: 'object',
  objectFields: {
    color: {
      type: 'text',
      label: 'Color',
    },
    backgroundColor: {
      type: 'text',
      label: 'Background Color',
    },
    hoverBackgroundColor: {
      type: 'text',
      label: 'Hover Background Color',
    },
  },
}

export const FontField: ObjectField<FontProps> = {
  label: 'Font',
  type: 'object',
  objectFields: {
    name: {
      type: 'select',
      label: 'Font Name',
      options: [
        {
          label: 'None',
          value: '',
        },
        ...defaultFontList.map((f) => ({
          label: f,
          value: f,
        })),
      ],
    },
    size: { type: 'number', label: 'Font Size' },
    weight: {
      type: 'select',
      label: 'Font Weight',
      options: [
        {
          label: 'None',
          value: '',
        },
        {
          label: 'Normal',
          value: 'normal',
        },
        {
          label: 'Bold',
          value: 'bold',
        },
        {
          label: 'Bolder',
          value: 'bolder',
        },
        {
          label: 'Lighter',
          value: 'lighter',
        },
        {
          label: '100',
          value: '100',
        },
        {
          label: '200',
          value: '200',
        },
        {
          label: '300',
          value: '300',
        },
        {
          label: '400',
          value: '400',
        },
        {
          label: '500',
          value: '500',
        },
        {
          label: '600',
          value: '600',
        },
        {
          label: '700',
          value: '700',
        },
        {
          label: '800',
          value: '800',
        },
        {
          label: '900',
          value: '900',
        },
      ],
    },
  },
}

export const SizeField: ObjectField<SizeProps> = {
  label: 'Size',
  type: 'object',
  objectFields: {
    width: { type: 'number', label: 'Width' },
    height: { type: 'number', label: 'Height' },
  },
}

export const Box1Field: ObjectField<Box1Props> = {
  label: 'Box',
  type: 'object',
  objectFields: {
    paddingTop: { type: 'number', label: 'Top Padding' },
    paddingBottom: { type: 'number', label: 'Bottom Padding' },
    paddingLeft: { type: 'number', label: 'Left Padding' },
    paddingRight: { type: 'number', label: 'Right Padding' },
  },
}

export const Box2Field: ObjectField<Box2Props> = {
  label: 'Box',
  type: 'object',
  objectFields: {
    marginTop: { type: 'number', label: 'Top Margin' },
    marginBottom: { type: 'number', label: 'Bottom Margin' },
    marginLeft: { type: 'number', label: 'Left Margin' },
    marginRight: { type: 'number', label: 'Right Margin' },
  },
}

export const Box3Field: ObjectField<Box3Props> = {
  label: 'Box',
  type: 'object',
  objectFields: {
    ...Box1Field['objectFields'],
    ...Box2Field['objectFields'],
  },
}

export const ImageComponent: Omit<
  ComponentConfig<ImageComponentProps>,
  'type'
> = {
  fields: {
    imageUrl: { type: 'text', label: 'Image URL' },
    styles: {
      label: 'Styles',
      type: 'object',
      objectFields: {
        size: SizeField,
        box: Box2Field,
      },
    },
  },
  defaultProps: {
    imageUrl: '',
    styles: {},
  },
  render: ({ imageUrl, styles }: ImageComponentProps) => {
    return (
      <div
        css={css`
          display: flex;
          flex: 0 0 50%;
          width: 100%;
          justify-content: center;
          align-items: center;

          ${genBox2Css(styles?.box)}
        `}
      >
        <img
          src={
            imageUrl ||
            'https://api.intentnow.com/cdn/system-images/placeholder-image.png'
          }
          alt=""
          css={css`
            max-width: 100%;
            object-fit: cover;
            object-position: top;

            ${styles?.size?.width && `width: ${styles.size.width}px;`}
            ${styles?.size?.height && `height: ${styles.size.height}px;`}
          `}
        />
      </div>
    )
  },
}

export const TextComponent: Omit<
  ComponentConfig<TextComponentProps>,
  'type'
> = {
  fields: {
    text: { type: 'text', label: 'Text' },
    styles: {
      label: 'Styles',
      type: 'object',
      objectFields: {
        align: {
          label: 'Align',
          type: 'radio',
          options: [
            {
              label: 'Left',
              value: 'left',
            },
            {
              label: 'Center',
              value: 'center',
            },
            {
              label: 'Right',
              value: 'right',
            },
          ],
        },
        colors: ColorsField,
        font: FontField,
        box: Box3Field,
      },
    },
  },
  defaultProps: {
    text: 'This is a text',
    styles: {
      align: 'center',
    },
  },
  render: ({ text, styles }) => {
    const { defaultFont, defaultColors, widgetContext } = usePuckContext()

    const font = {
      ...defaultFont,
      ...styles?.font,
    }

    const colors = {
      ...defaultColors,
      ...styles?.colors,
    }

    const renderText = useMemo(() => {
      if (!text || !widgetContext) {
        return ''
      }

      return widgetContext.fillVariableValues(text) ?? ''
    }, [text, widgetContext?.content])

    return (
      <div
        css={css`
          text-align: ${styles?.align ?? 'center'};

          ${genColorsCss(colors)}
          ${genFontCss(font)}
          ${genBox3Css(styles?.box)}
        `}
        //TODO: we may want to clean the text first to prevent malicious user content (when we start to release the widget editor to the merchants)
        //TODO: fillVariableValues
        dangerouslySetInnerHTML={{
          __html: renderText,
        }}
      />
    )
  },
}

export const ButtonComponent: Omit<
  ComponentConfig<ButtonComponentProps>,
  'type'
> = {
  fields: {
    text: { type: 'text', label: 'Button Text' },
    action: {
      label: 'Action',
      type: 'object',
      objectFields: {
        action: {
          label: 'Action',
          type: 'select',
          options: [
            { label: 'None', value: '' },
            { label: 'Copy Code', value: PromoWidgetAction.copyCode },
            //{ label: 'Restore Dialog', value: PromoWidgetAction.restoreDialog },
            {
              label: 'Minimize Dialog',
              value: PromoWidgetAction.minimizeDialog,
            },
            { label: 'Open URL', value: PromoWidgetAction.openUrl },
          ],
        },
        actionTarget: {
          label: 'Action Target',
          type: 'text',
        },
      },
    },
    styles: {
      label: 'Styles',
      type: 'object',
      objectFields: {
        variant: {
          label: 'Variant',
          type: 'radio',
          options: [
            { label: 'Default', value: ButtonVariant.default },
            { label: 'Link', value: ButtonVariant.link },
          ],
        },
        colors: ButtonColorsField,
        font: FontField,
        size: SizeField,
        box: Box3Field,
      },
    },
  },
  defaultProps: {
    text: 'Click Me',
    styles: {
      variant: ButtonVariant.default,
      colors: {
        color: 'white',
        backgroundColor: 'blue',
        hoverBackgroundColor: 'lightblue',
      },
      box: {
        paddingTop: 15,
        paddingBottom: 15,
        paddingLeft: 25,
        paddingRight: 25,
      },
    },
  },
  resolveFields: (data) => {
    //Show 'actionTarget' field conditionally
    const fields = { ...ButtonComponent.fields! }
    const actionField = { ...(fields.action as ObjectField) }

    if (data.props.action?.action !== PromoWidgetAction.openUrl) {
      actionField.objectFields = {
        action: actionField.objectFields.action,
      }
    }

    fields.action = actionField
    return fields
  },
  render: ({ puck, text, action, styles }) => {
    const { widgetContext, defaultColors, defaultFont } = usePuckContext()

    async function handleClick() {
      if (!puck.isEditing && action?.action && widgetContext?.actionCaller) {
        await widgetContext.actionCaller(
          action?.action,
          action?.action === PromoWidgetAction.openUrl
            ? {
                url: action.actionTarget,
              }
            : undefined
        )
      }
    }

    return (
      <div
        css={css`
          display: flex;
          justify-content: center;
        `}
      >
        <button
          css={css`
            cursor: pointer;

            ${styles.variant === ButtonVariant.link &&
            `
              background: none;
              border: none;
              text-decoration: underline;
              ${genColorsCss({
                ...defaultColors,
                ...styles?.colors,
              })}
              
            `}

            ${(styles.variant === ButtonVariant.default || !styles.variant) &&
            `
              border: 1px solid ${styles?.colors?.color || defaultColors?.color};
              border-radius: 4px;
              ${genButtonColorsCss({
                ...defaultColors,
                ...styles?.colors,
              })}
              
            `}
            
            ${genFontCss({
              ...defaultFont,
              ...styles?.font,
            })}

            ${genBox3Css(styles?.box)}

            ${genSizeCss(styles?.size)}
          `}
          onClick={handleClick}
        >
          {text}
        </button>
      </div>
    )
  },
}

export const ContainerComponent: Omit<
  ComponentConfig<ContainerComponentProps>,
  'type'
> = {
  fields: {
    styles: {
      label: 'Styles',
      type: 'object',
      objectFields: {
        border: {
          label: 'Border',
          type: 'object',
          objectFields: {
            style: {
              label: 'Style',
              type: 'select',
              options: [
                {
                  label: 'None',
                  value: BorderStyle.none,
                },
                {
                  label: 'Solid',
                  value: BorderStyle.solid,
                },
                {
                  label: 'Dashed',
                  value: BorderStyle.dashed,
                },
                {
                  label: 'Dotted',
                  value: BorderStyle.dotted,
                },
              ],
            },
            width: { type: 'number', label: 'Width' },
            color: {
              type: 'text',
              label: 'Color',
            },
          },
        },
        colors: ColorsField,
        box: Box3Field,
      },
    },
  },
  defaultProps: {
    styles: {
      border: {
        style: BorderStyle.solid,
        width: 1,
      },
    },
  },
  render: ({ styles }: ContainerComponentProps) => {
    const { defaultFont, defaultColors, widgetContext } = usePuckContext()

    return (
      <div
        css={css`
          ${styles?.border?.style !== BorderStyle.none &&
          `
            border-style: ${styles?.border?.style};
            border-width: ${styles?.border?.width ?? 0}px;
            border-color: ${styles?.border?.color ?? defaultColors?.color};
            border-radius: 2px;
          `}

          ${genColorsCss({
            ...defaultColors,
            ...styles?.colors,
          })}

          ${genFontCss({
            ...defaultFont,
          })}

          ${genBox3Css(styles?.box)}
        `}
      >
        <DropZone zone="content-zone" disallow={['DialogContainer']} />
      </div>
    )
  },
}

export const DialogContainerComponent: Omit<
  ComponentConfig<DiaglogContainerComponentProps>,
  'type'
> = {
  label: 'Dialog Layout',
  permissions: {
    insert: false,
    duplicate: false,
    delete: false,
  },
  fields: {
    image: {
      label: 'Dialog Image',
      type: 'object',
      objectFields: {
        imageUrl: { type: 'text', label: 'Image URL' },
        desktopImagePostion: {
          label: 'Desktop Image Position',
          type: 'radio',
          options: [
            {
              label: 'Left',
              value: 'left',
            },
            {
              label: 'Right',
              value: 'right',
            },
          ],
        },
        showMobileImage: {
          label: 'Show Image on Mobile',
          type: 'radio',
          options: [
            {
              label: 'Show',
              value: true,
            },
            {
              label: 'Hide',
              value: false,
            },
          ],
        },
      },
    },
  },
  defaultProps: {
    image: {
      imageUrl: '',
      desktopImagePostion: 'left',
      showMobileImage: true,
    },
  },
  render: ({
    image: { imageUrl, desktopImagePostion, showMobileImage },
  }: DiaglogContainerComponentProps) => {
    const { isMobile } = usePuckContext()

    return (
      <div id="intentnow-modal">
        {!isMobile && (
          <div
            css={css`
              display: flex;
            `}
          >
            {desktopImagePostion === 'left' && (
              <div
                id="image-panel"
                css={css`
                  display: flex;
                  flex: 0 0 50%;
                  width: 100%;
                `}
              >
                <img
                  src={imageUrl}
                  alt=""
                  css={css`
                    width: 100%;
                    object-fit: cover;
                    object-position: top;
                  `}
                />
              </div>
            )}
            <div
              id="content-panel"
              css={css`
                width: 100%;
                max-width: 100%;
                display: block;
                justify-content: center;

                padding: 20px;
              `}
            >
              <DropZone zone="content-panel" disallow={['DialogContainer']} />
            </div>
            {desktopImagePostion === 'right' && (
              <div
                id="image-panel"
                css={css`
                  display: flex;
                  flex: 0 0 50%;
                  width: 100%;
                `}
              >
                <img
                  src={
                    imageUrl ||
                    'https://api.intentnow.com/cdn/system-images/placeholder-dialog-image.png'
                  }
                  alt=""
                  css={css`
                    width: 100%;
                    object-fit: cover;
                    object-position: top;
                  `}
                />
              </div>
            )}
          </div>
        )}
        {isMobile && (
          <div
            css={css`
              display: flex;
              flex-direction: column;
            `}
          >
            {showMobileImage && (
              <div
                id="image-panel"
                css={css`
                  display: flex;
                  flex: 0 0 50%;
                  width: 100%;
                `}
              >
                <img
                  src={
                    imageUrl ||
                    'https://api.intentnow.com/cdn/system-images/placeholder-dialog-image.png'
                  }
                  alt=""
                  css={css`
                    width: 100%;
                    height: 230px;
                    object-fit: cover;
                    object-position: top;
                  `}
                  onClick={() => {}}
                />
              </div>
            )}
            <div
              id="content-panel"
              css={css`
                display: block;
                justify-content: center;
                padding: 5px;
              `}
            >
              <DropZone zone="content-panel" disallow={['DialogContainer']} />
            </div>
          </div>
        )}
      </div>
    )
  },
}
