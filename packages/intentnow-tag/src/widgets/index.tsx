export * from '../entities'
export * from './promo-widget'
export * from './puck/puck-root'
export * from './puck/puck-components'
export * from './puck/puck-props'
export * from './widget-context'
export { summarizeFontsFromWidget3Config } from './ui/widget3-helper'

import ReactDOM from 'react-dom/client'
import { PromoWidget } from './promo-widget'
import { IntentnowSettings } from '../entities'
import {
  customizeIntentnowSettings,
  generateMockWidget2Content,
  generateMockWidget3Content,
} from './settings'

const cachedDomRoot: Record<string, any> = {}

export function runWidget(
  elementId: string,
  intentnowSettings: IntentnowSettings
) {
  const renderRoot = document.getElementById(elementId)
  if (renderRoot) {
    intentnowSettings.logging && console.debug(`Element ${elementId} found`)

    customizeIntentnowSettings(intentnowSettings)

    // We will run into errors if we create the root more than once on the same element. So we cache it here.
    let domRoot = cachedDomRoot[elementId]
    if (!domRoot) {
      domRoot = ReactDOM.createRoot(renderRoot)
      cachedDomRoot[elementId] = domRoot
    }
    domRoot.render(<PromoWidget intentnowSettings={intentnowSettings} />)
  } else {
    intentnowSettings.logging && console.debug(`Element ${elementId} not found`)
  }
}

export function testWidget2(
  elementId: string,
  discountTitle: string | undefined
) {
  const shop = 'test-store.myshopify.com'
  const renderRoot = document.getElementById(elementId)
  if (renderRoot) {
    console.debug(`Element ${elementId} found`)

    let mockContent: ReturnType<typeof generateMockWidget2Content> | undefined

    if (discountTitle) {
      mockContent = generateMockWidget2Content()
      mockContent.widget2.dialog.texts[0].text = discountTitle
      mockContent.discount.title = discountTitle
    }

    const intentnowSettings = {
      baseApiUrl: 'mock-api',
      shop,
      logging: true,
      isEnabled: () => true,
      mockGetDiscount: mockContent ?? {},
      delayTimeMs: 0,
      hideWelcomeOffer: false,
    }
    //customizeIntentnowSettings(intentnowSettings)

    // We will run into errors if we create the root more than once on the same element. So we cache it here.
    let domRoot = cachedDomRoot[elementId]
    if (!domRoot) {
      domRoot = ReactDOM.createRoot(renderRoot)
      cachedDomRoot[elementId] = domRoot
    }

    domRoot.render(
      <PromoWidget settings={{}} intentnowSettings={intentnowSettings} />
    )
  } else {
    console.debug(`Element ${elementId} not found`)
  }
}

export function testWidget3(
  elementId: string,
  discountTitle: string | undefined
) {
  const shop = 'test-store.myshopify.com'
  const renderRoot = document.getElementById(elementId)
  if (renderRoot) {
    console.debug(`Element ${elementId} found`)

    let mockContent: ReturnType<typeof generateMockWidget3Content> | undefined

    if (discountTitle) {
      mockContent = generateMockWidget3Content()
      mockContent.discount.title = discountTitle
    }

    const intentnowSettings = {
      baseApiUrl: 'mock-api',
      shop,
      logging: true,
      isEnabled: () => true,
      mockGetDiscount: mockContent ?? {},
      delayTimeMs: 0,
      hideWelcomeOffer: false,
    }
    //customizeIntentnowSettings(intentnowSettings)

    // We will run into errors if we create the root more than once on the same element. So we cache it here.
    let domRoot = cachedDomRoot[elementId]
    if (!domRoot) {
      domRoot = ReactDOM.createRoot(renderRoot)
      cachedDomRoot[elementId] = domRoot
    }

    domRoot.render(
      <PromoWidget settings={{}} intentnowSettings={intentnowSettings} />
    )
  } else {
    console.debug(`Element ${elementId} not found`)
  }
}
