import React from 'react'
import {
  IntentnowSessionState,
  IntentnowSettings,
  PreviewMode,
  PromoWidgetContent,
  PromoWidgetSettings,
} from '../entities'
import { PromoWidgetAction, Widget2Config } from '@packages/shared-entities'
import { useSessionStorage } from 'usehooks-ts'
import { toast } from 'react-toastify'
import { getDiscount } from '../discount'
import { trackIntentnowEvent as originalTrackIntentnowEvent } from '../events'
import { hideWelcomeOfferInternal } from '../detectives'
import { mqMobile } from './ui/widget2-helper'

//This is brain of the Promo Widget: all states and business logics are implemented here. All the "template" components should just render the UI with very minimum logics to connect user actions to this hook.
export const usePromoWidgetManager = () => {
  const [intentnowSettings, setIntentnowSettings] =
    React.useState<IntentnowSettings>()
  const [settings, setSettings] = React.useState<PromoWidgetSettings>()
  const [content, setContent] = React.useState<PromoWidgetContent>()
  const [sessionState1, setSessionState1] =
    useSessionStorage<IntentnowSessionState>('intentnow-state', {
      minimized: false,
      closed: false,
      lastShown: 0,
    })
  const [cachedDiscountLoaded, setCachedDiscountLoaded] =
    React.useState<boolean>(false)
  const [promoShown, setPromoShown] = React.useState<boolean>(false)
  const [dialogVisible, setDialogVisible] = React.useState<boolean>(false)
  const [teaserVisible, setTeaserVisible] = React.useState<boolean>(false)

  //Use a mock session state under preview mode
  const { sessionState, setSessionState } = React.useMemo(() => {
    return settings?.previewMode
      ? {
          sessionState: {
            minimized: false,
            closed: false,
            lastShown: 0,
          },
          setSessionState: () => {},
        }
      : {
          sessionState: sessionState1,
          setSessionState: setSessionState1,
        }
  }, [settings?.previewMode, sessionState1, setSessionState1])

  const mobileMediaQuery = React.useMemo((): string => {
    if (settings?.previewMode) {
      if (
        [
          PreviewMode.previewDialogMobile,
          PreviewMode.previewTeaserMobile,
          PreviewMode.previewBothMobile,
        ].includes(settings.previewMode)
      ) {
        //Make it always mobile screen
        return '@media only screen and (max-width: 99999px)'
      } else {
        //Make it always desktop screen
        return '@media only screen and (max-width: 0px)'
      }
    } else {
      return mqMobile
    }
  }, [settings?.previewMode])

  async function trackIntentnowEvent(
    eventName: string,
    eventData: any,
    highPriority = false
  ) {
    if (intentnowSettings?.preview) {
      if (intentnowSettings?.logging) {
        console.debug(`live-preivew-trackIntentnowEvent: ${eventName}`)
      }
      return
    }

    if (settings?.previewMode) {
      if (intentnowSettings?.logging) {
        console.debug(`edit-preivew-trackIntentnowEvent: ${eventName}`)
      }
      return
    }

    return await originalTrackIntentnowEvent(eventName, eventData, highPriority)
  }

  React.useEffect(() => {
    if (settings?.previewMode) {
      setDialogVisible(
        [
          PreviewMode.previewDialog,
          PreviewMode.previewDialogMobile,
          PreviewMode.previewBoth,
          PreviewMode.previewBothMobile,
        ].includes(settings.previewMode)
      )
      setTeaserVisible(
        [
          PreviewMode.previewTeaser,
          PreviewMode.previewTeaserMobile,
          PreviewMode.previewBoth,
          PreviewMode.previewBothMobile,
        ].includes(settings.previewMode)
      )
    } else if ((content?.widget3 || content?.widget2) && content?.discount) {
      const showD = Boolean(!sessionState.minimized && !sessionState.closed)
      const showT = Boolean(sessionState.minimized && !sessionState.closed)
      setDialogVisible(showD)
      setTeaserVisible(showT)

      if (showD) {
        trackIntentnowEvent(
          'intentnow-promo-shown',
          {
            widgetId: content.widgetId,
          },
          !promoShown
        )
        setPromoShown(true)
      } else if (showT) {
        trackIntentnowEvent(
          'intentnow-promo-shown-minimized',
          {
            widgetId: content.widgetId,
          },
          !promoShown
        )
        setPromoShown(true)
      }

      hideWelcomeOfferInternal(intentnowSettings)
    } else if (
      (sessionState.discount?.widget3 || sessionState.discount?.widget2) &&
      sessionState.discount?.discount &&
      sessionState.minimized &&
      !cachedDiscountLoaded
    ) {
      setContent((prevState) => (prevState ? prevState : sessionState.discount))

      const showD = Boolean(!sessionState.minimized && !sessionState.closed)
      const showT = Boolean(sessionState.minimized && !sessionState.closed)
      setDialogVisible(showD)
      setTeaserVisible(showT)

      setCachedDiscountLoaded(() => true)

      hideWelcomeOfferInternal(intentnowSettings)
    } else {
      setCachedDiscountLoaded(() => true)
      setDialogVisible(false)
      setTeaserVisible(false)
    }
  }, [settings, content, sessionState, cachedDiscountLoaded])

  React.useEffect(() => {
    if (intentnowSettings) {
      trackIntentnowEvent('intentnow-promo-run', {})

      if (!intentnowSettings.isEnabled()) {
        setContent(undefined)
        updateSessionState({
          discount: undefined,
        })
        trackIntentnowEvent(
          'intentnow-promo-none',
          {
            reason: 'code-in-cart',
          },
          true
        )
        return
      }

      const timeoutId = setTimeout(async () => {
        try {
          const response = await getDiscount(intentnowSettings)
          const getDiscountResponse = {
            widgetId: response.widgetId,
            discount: response.discount,
            widget2: response.widget2 as Widget2Config,
            widget3: response.widget3,
          }

          if (
            (getDiscountResponse.widget3 || getDiscountResponse.widget2) &&
            getDiscountResponse.discount
          ) {
            setContent(getDiscountResponse as PromoWidgetContent)
            updateSessionState({
              discount: getDiscountResponse as PromoWidgetContent,
            })
          } else {
            setContent(undefined)
            updateSessionState({
              discount: undefined,
            })
            trackIntentnowEvent(
              'intentnow-promo-none',
              {
                reason: 'no-discount',
              },
              true
            )
          }
        } catch (_) {
          setContent(undefined)
          updateSessionState({
            discount: undefined,
          })
        }
      }, intentnowSettings.delayTimeMs ?? 0)

      return () => clearTimeout(timeoutId)
    }
  }, [intentnowSettings])

  function updateSessionState(update: Partial<IntentnowSessionState>) {
    setSessionState({
      ...sessionState,
      ...update,
    })
  }

  function restoreDialog() {
    if (settings?.previewMode) {
      return
    }

    updateSessionState({
      minimized: false,
      closed: false,
      lastShown: Date.now(),
    })

    trackIntentnowEvent('intentnow-promo-minimizedui-clicked', {})
  }

  function minimizeDialogInternal(event: string) {
    if (settings?.previewMode) {
      return
    }

    updateSessionState({
      minimized: true,
      closed: false,
      lastShown: Date.now(),
    })

    trackIntentnowEvent(event, {})
  }

  function minimizeDialog() {
    return minimizeDialogInternal('intentnow-promo-minimizebtn-clicked')
  }

  function cancelDialog() {
    return minimizeDialogInternal('intentnow-promo-cancelbtn-clicked')
  }

  function clickBackdrop() {
    return minimizeDialogInternal('intentnow-promo-backdrop-clicked')
  }

  async function copyCode() {
    if (settings?.previewMode) {
      return
    }

    if (content?.discount?.code) {
      try {
        await navigator.clipboard.writeText(content.discount.code)
        toast.success(`Discount code ${content.discount.code} copied`)
        setTimeout(() => {
          //For some reason the autoClose doesn't work on the Shopify site, so we force to close the toast here.
          toast.dismiss()
        }, 2000)

        trackIntentnowEvent('intentnow-promo-copybtn-clicked', {}, true)
      } catch (err) {
        toast.error(`Failed to copy`)
        intentnowSettings?.logging && console.error(`Failed to copy text:`, err)
      }
    }
  }

  function fillVariableValues(text: string | undefined): string | undefined {
    if (!text) {
      return undefined
    }

    if (!content) {
      return text
    }

    const variables = [
      {
        name: 'discountCode',
        value: content.discount?.code ?? '',
      },
      {
        name: 'discountTitle',
        value: content.discount?.title ?? '',
      },
    ]

    let newText = text

    for (const variable of variables) {
      newText = newText.replaceAll(`{{${variable.name}}}`, variable.value)
    }

    return newText
  }

  async function actionCaller(action: PromoWidgetAction, data?: any) {
    if (settings?.previewMode) {
      return
    }

    const actionMap: Record<PromoWidgetAction, (data?: any) => Promise<void>> =
      {
        [PromoWidgetAction.restoreDialog]: async () => restoreDialog(),
        [PromoWidgetAction.minimizeDialog]: async () => minimizeDialog(),
        [PromoWidgetAction.cancelDialog]: async () => cancelDialog(),
        [PromoWidgetAction.clickBackdrop]: async () => clickBackdrop(),
        [PromoWidgetAction.copyCode]: async () => {
          await copyCode()
        },
        [PromoWidgetAction.openUrl]: async (data?: any) => {
          if (data?.url) {
            window.open(data.url, '_blank')
          }
        },
      }

    const actionFunc = actionMap[action]
    if (actionFunc) {
      await actionFunc(data)
    }
  }

  return {
    settings,
    setSettings,
    intentnowSettings,
    setIntentnowSettings,
    content,
    setContent,
    dialogVisible,
    teaserVisible,
    sessionState,
    restoreDialog,
    minimizeDialog,
    cancelDialog,
    clickBackdrop,
    copyCode,
    mobileMediaQuery,
    fillVariableValues,
    actionCaller,
  }
}
