'use client'

import { Render } from '@measured/puck'
import '@measured/puck/puck.css'
import {
  widget3DialogPuckConfig,
  widget3TeaserPuckConfig,
} from '../puck/puck-root'
import { usePromoWidgetContext } from '../widget-context'

export function Widget3Dialog() {
  const { content, dialogVisible } = usePromoWidgetContext()

  if (!content?.widget3 || !dialogVisible) {
    return <></>
  }

  return (
    <Render config={widget3DialogPuckConfig} data={content?.widget3?.dialog} />
  )
}

//TODO: implement teaser based on Puck
export function Widget3Teaser() {
  const { content, teaserVisible } = usePromoWidgetContext()

  if (!content?.widget3 || !teaserVisible) {
    return <></>
  }

  return (
    <Render config={widget3TeaserPuckConfig} data={content?.widget3?.teaser} />
  )
}
