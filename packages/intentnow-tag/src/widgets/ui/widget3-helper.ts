import {
  defaultFontMap,
  Widget3Config,
  WidgetFontType,
} from '@packages/shared-entities'
import { FontProps } from '../puck/puck-props'

function getFontFromComponent(component: any): FontProps | undefined {
  if (
    component?.type === 'Text' ||
    component?.type === 'Button' ||
    component?.type === 'Container'
  ) {
    return component.props?.styles?.font
  }

  return undefined
}

export function summarizeFontsFromWidget3Config(widgetConfig: Widget3Config) {
  let fontSet = new Set<string | undefined>()

  function addFront(font?: FontProps) {
    if (font?.name) {
      fontSet.add(font.name)
    }
  }

  //Dialog
  if (widgetConfig.dialog) {
    addFront(widgetConfig.dialog?.root?.props?.styles?.font)

    widgetConfig.dialog?.content?.forEach((component: any) => {
      addFront(getFontFromComponent(component))
    })

    Object.values(widgetConfig.dialog?.zones ?? {}).forEach((zone: any) => {
      zone?.forEach((component: any) => {
        addFront(getFontFromComponent(component))
      })
    })
  }

  //Teaser
  if (widgetConfig.teaser) {
    addFront(widgetConfig.teaser?.root?.props?.styles?.font)

    widgetConfig.teaser?.content?.forEach((component: any) => {
      addFront(getFontFromComponent(component))
    })

    Object.values(widgetConfig.teaser?.zones ?? {}).forEach((zone: any) => {
      zone?.forEach((component: any) => {
        addFront(getFontFromComponent(component))
      })
    })
  }

  const fonts = Array.from(fontSet)
    .map((f) => {
      return f ? defaultFontMap.get(f) : undefined
    })
    .filter((f) => Boolean(f))
    .map((f) => f!)

  const googleFonts = fonts.filter((f) => f.type === WidgetFontType.google)
  const customFonts = fonts.filter((f) => f.type === WidgetFontType.custom)

  return {
    fonts,
    googleFonts,
    customFonts,
  }
}
