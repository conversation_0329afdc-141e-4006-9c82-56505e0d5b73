import { css, Global } from '@emotion/react'
import {
  defaultFontMap,
  Widget2Config,
  Widget2BoxConfig,
  Widget2ButtonConfig,
  Widget2FontConfig,
  WidgetFontType,
  Widget2SizeConfig,
  Widget2TextConfig,
} from '@packages/shared-entities'
import { CustomFontConfig } from '@packages/shared-entities'
import { PromoWidgetContent } from '../../entities'

export const mqMobile = '@media only screen and (max-width: 600px)'

export function genBoxStyles(box?: Widget2BoxConfig) {
  if (!box) {
    return ``
  }

  return `
    padding-top: ${box.paddingTop && `${box.paddingTop}px`};
    padding-right: ${box.paddingRight && `${box.paddingRight}px`};
    padding-bottom: ${box.paddingBottom && `${box.paddingBottom}px`};
    padding-left: ${box.paddingLeft && `${box.paddingLeft}px`};
  `
}

export function genSizeStyles(size?: Widget2SizeConfig) {
  if (!size) {
    return ``
  }

  return `
    width: ${size.width && `${size.width}px`};
    height: ${size.height && `${size.height}px`};
  `
}

export function genFontStyles(
  font: Widget2FontConfig | undefined,
  defaultFont: Widget2FontConfig
) {
  const fontName = font?.family ?? defaultFont.family
  const fontConfig = fontName ? defaultFontMap.get(fontName) : undefined
  return `
    font-family: ${fontConfig?.fontFamily?.map((f) => `"${f}"`).join(',')};
    font-size: ${(font?.size && `${font?.size}px`) || (defaultFont.size && `${defaultFont.size}px`)};
    font-style: normal;
    font-variant: normal;
    font-weight: ${font?.weight ?? defaultFont.weight ?? 'normal'};
    line-height: normal;
  `
}

export function genColorStyles(
  styles?: {
    color?: string
    backgroundColor?: string
  },
  defaultStyles?: {
    color?: string
    backgroundColor?: string
  }
) {
  return `
    color: ${styles?.color ?? defaultStyles?.color};
    background-color: ${styles?.backgroundColor ?? defaultStyles?.backgroundColor};
  `
}

export function genTextBlock(
  text: Widget2TextConfig,
  defaultFont: Widget2FontConfig,
  key?: any,
  content?: PromoWidgetContent
) {
  return (
    <div
      key={key}
      css={css`
        text-align: center;
        ${genFontStyles(text.styles?.font, defaultFont)}
        ${genBoxStyles(text.styles?.box)}
        ${genColorStyles(text.styles)}
      `}
    >
      <div
        //TODO: we may want to clean the text first to prevent malicious user content (when we start to release the widget editor to the merchants)
        dangerouslySetInnerHTML={{
          __html: fillVariableValues(text.text, content) ?? '',
        }}
      />
    </div>
  )
}

export function genButtonBlock(
  button: Widget2ButtonConfig,
  defaultStyles: {
    font: Widget2FontConfig
    color?: string
    backgroundColor?: string
  },
  onClick: () => void
) {
  return (
    <div
      css={css`
        display: flex;
        justify-content: center;
        width: 100%;
        ${genBoxStyles(button?.styles?.box)}
      `}
    >
      <button
        id="copyButton"
        css={css`
          border: 1px solid #fff;
          border-radius: 0px;
          cursor: pointer;
          transition: all 0.2s ease-in-out;
          max-width: 90%;
          ${genFontStyles(button?.styles?.font, defaultStyles?.font)}
          ${genColorStyles(button?.styles, defaultStyles)}
          ${genSizeStyles(button?.styles?.size)}
        `}
        onClick={onClick}
      >
        {button.text}
      </button>
    </div>
  )
}

export function fillVariableValues(
  text: string | undefined,
  content: PromoWidgetContent | undefined
): string | undefined {
  if (!text) {
    return undefined
  }

  if (!content) {
    return text
  }

  const variables = [
    {
      name: 'discountCode',
      value: content.discount?.code ?? '',
    },
    {
      name: 'discountTitle',
      value: content.discount?.title ?? '',
    },
  ]

  let newText = text

  for (const variable of variables) {
    newText = newText.replaceAll(`{{${variable.name}}}`, variable.value)
  }

  return newText
}

export function summarizeFontsFromWidget2Config(widgetConfig: Widget2Config) {
  let fontSet = new Set<string | undefined>()

  //Dialog
  fontSet.add(widgetConfig.dialog?.mainStyles?.font?.family)
  fontSet.add(
    widgetConfig.dialog?.copyCodeBlock?.cancelText?.styles?.font?.family
  )
  fontSet.add(widgetConfig.dialog?.copyCodeBlock?.codeTextStyles?.font?.family)
  fontSet.add(
    widgetConfig.dialog?.copyCodeBlock?.copyButton?.styles?.font?.family
  )
  widgetConfig.dialog?.texts?.forEach((text) => {
    fontSet.add(text.styles?.font?.family)
  })
  widgetConfig.dialog?.copyCodeBlock?.texts?.forEach((text) => {
    fontSet.add(text.styles?.font?.family)
  })

  //Teaser
  fontSet.add(widgetConfig.teaser?.mainStyles?.font?.family)
  widgetConfig.teaser?.texts?.forEach((text) => {
    fontSet.add(text.styles?.font?.family)
  })

  const fonts = Array.from(fontSet)
    .map((f) => {
      return f ? defaultFontMap.get(f) : undefined
    })
    .filter((f) => Boolean(f))
    .map((f) => f!)

  const googleFonts = fonts.filter((f) => f.type === WidgetFontType.google)
  const customFonts = fonts.filter((f) => f.type === WidgetFontType.custom)

  return {
    fonts,
    googleFonts,
    customFonts,
  }
}

export function generateStylesForCustomFonts(customFonts?: CustomFontConfig[]) {
  if (customFonts) {
    return (
      <Global
        styles={css`
          ${customFonts
            .map((font) => {
              return `
          @font-face {
            font-display: ${font.fontDisplay};
            font-family: ${font.fontFamily};
            ${font.fontStyle ? `font-style: ${font.fontStyle};` : ''}
            ${font.fontWeight ? `font-weight: ${font.fontWeight};` : ''}
            src:
              ${font.fontSources
                .map((source) => {
                  return `url(${source.url}) format("${source.format}")`
                })
                .join(', ')};
          }
      `
            })
            .join('\n\n')}
        `}
      />
    )
  } else {
    return <></>
  }
}
