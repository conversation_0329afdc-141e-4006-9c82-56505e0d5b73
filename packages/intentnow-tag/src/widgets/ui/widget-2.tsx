'use client'

import { css, Global } from '@emotion/react'
import { usePromoWidgetContext } from '../widget-context'
import {
  genBoxStyles,
  genButtonBlock,
  genColorStyles,
  genFontStyles,
  genSizeStyles,
  genTextBlock,
} from './widget2-helper'

export function Widget2Dialog() {
  const {
    settings,
    content,
    dialogVisible,
    minimizeDialog,
    cancelDialog,
    copyCode,
    mobileMediaQuery,
  } = usePromoWidgetContext()

  if (!content?.widget2 || !dialogVisible) {
    return <></>
  }

  const mainStyles = content.widget2.dialog.mainStyles
  const logoImage = content.widget2.dialog.logoImage
  const mainImage = content.widget2.dialog.mainImage
  const copyCodeBlockStyles = content.widget2.dialog.copyCodeBlock.mainStyles
  const codeTextStyles = content.widget2.dialog.copyCodeBlock.codeTextStyles
  const copyButton = content.widget2.dialog.copyCodeBlock.copyButton
  const cancelText = content.widget2.dialog.copyCodeBlock.cancelText

  return (
    <>
      <div
        id="intentnow-modal"
        css={css`
          position: ${settings?.previewMode ? 'absolute' : 'fixed'};
          height: auto;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          ${settings?.previewMode ? '' : 'z-index: 90001;'}
          max-height: auto;
          max-width: 810px;
          width: 90%;

          ${mobileMediaQuery} {
            max-width: 330px;
            max-height: none;
          }
        `}
      >
        <div
          id="action-button-wrapper"
          css={css`
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            position: absolute;
            top: 11px;
            right: 14px;
            z-index: 5;

            ${mobileMediaQuery} {
              right: 11px;
            }
          `}
        >
          <button
            id="minimize-button"
            css={css`
              width: 20px;
              height: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0;
              border: 0;
              cursor: pointer;
              background-color: transparent;
              stroke: #000;

              ${mobileMediaQuery} {
                width: 15px;
                height: 15px;
                padding: 2px;
              }
            `}
            onClick={() => {
              minimizeDialog()
            }}
          >
            <svg
              width="15"
              height="5"
              viewBox="0 0 9 5"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <line
                y1="-0.5"
                x2="15"
                y2="-0.5"
                transform="matrix(-1 0 0.0867925 -0.996226 9 0)"
                strokeWidth="4"
              />
            </svg>
          </button>
        </div>
        <div
          id="discount-modal-content"
          css={css`
            display: flex;
            align-items: flex-start;
            position: relative;
            width: 100%;
            margin: 0 auto;
            ${genColorStyles(mainStyles)}
            box-shadow: 0px 4px 24px 0px #00000026;

            ${mobileMediaQuery} {
              flex-direction: column;
              height: 100%;
            }
          `}
        >
          {mainImage.desktopPosition === 'left' && (
            <div
              id="discount-image-left"
              css={css`
                display: flex;
                height: 100%;
                max-width: 50%;
                flex: 0 0 50%;

                ${mobileMediaQuery} {
                  display: none;
                }
              `}
            >
              <img
                id="discountMainImage"
                src={`https://api.intentnow.com/cdn/shopify-images/${mainImage.path}`}
                width="100%"
                height="100%"
                alt=""
                css={css`
                  object-fit: cover;
                `}
              />
            </div>
          )}
          {mainImage.mobilePosition === 'top' && (
            <div
              id="discount-image-mobile-top"
              css={css`
                display: none;
                height: 100%;
                max-width: 100%;
                width: 100%;
                flex: 0 0 50%;

                ${mobileMediaQuery} {
                  display: flex;
                }
              `}
            >
              <img
                id="discountMainImage"
                src={`https://api.intentnow.com/cdn/shopify-images/${mainImage.path}`}
                alt=""
                css={css`
                  object-fit: cover;
                  height: 260px;
                  width: 100%;
                  object-position: top;
                `}
              />
            </div>
          )}
          <div
            id="discount-text"
            css={css`
              width: 100%;
              padding: 0;
              position: relative;
              max-width: 100%;
              flex: 0 0 50%;

              ${mobileMediaQuery} {
                width: 100%;
                padding: 0;
                position: relative;
                max-width: 100%;
                flex: 0 0 50%;
              }
            `}
          >
            <div
              id="modal-content"
              css={css`
                padding: 15px 25px 30px 30px;

                ${mobileMediaQuery} {
                  padding: 0px 20px 20px 20px;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                }
              `}
            >
              {logoImage?.path && (
                <div
                  id="logo-block"
                  css={css`
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    ${genBoxStyles(logoImage.styles?.box)}

                    ${mobileMediaQuery} {
                    }
                  `}
                >
                  <img
                    id="discountMainImage"
                    src={`https://api.intentnow.com/cdn/shopify-images/${logoImage.path}`}
                    alt=""
                    css={css`
                      max-width: 100%;
                      ${genSizeStyles(logoImage.styles?.size)}
                    `}
                  />
                </div>
              )}
              <div
                id="text-list"
                css={css`
                  padding: 0 0 20px;

                  ${mobileMediaQuery} {
                    padding: 0;
                  }
                `}
              >
                {content.widget2.dialog.texts?.map((text: any, index: number) =>
                  genTextBlock(text, mainStyles.font, index, content)
                )}
              </div>
              <div
                id="copy-code-block"
                css={css`
                  ${genColorStyles(codeTextStyles, copyCodeBlockStyles)}
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  margin-top: 0;
                  border: 1px solid;
                  border-radius: 2px;
                  padding: 10px;

                  ${mobileMediaQuery} {
                    margin-top: 16px;
                    justify-content: flex-start;
                    width: 100%;
                  }
                `}
              >
                {genTextBlock(
                  {
                    text: content.discount?.code ?? '',
                    styles: codeTextStyles,
                  },
                  mainStyles.font
                )}
                {genButtonBlock(copyButton, mainStyles, copyCode)}
                <div
                  id="discount-footer"
                  css={css`
                    text-align: center;
                  `}
                >
                  {content.widget2.dialog.copyCodeBlock.texts?.map(
                    (text: any, index: number) =>
                      genTextBlock(text, mainStyles.font, index, content)
                  )}
                </div>
              </div>
              <div
                id="discount-cancel"
                css={css`
                  width: 100%;
                  text-align: center;
                  padding-top: 10px;
                  ${genBoxStyles(cancelText.styles?.box)}
                `}
              >
                <a
                  href="#"
                  id="cancelButton"
                  //id="no-thanks-discount"
                  title="NO THANKS"
                  css={css`
                    ${genFontStyles(cancelText?.styles?.font, mainStyles.font)}
                    ${genColorStyles(cancelText?.styles, mainStyles)}
                    text-transform: uppercase;
                    line-height: 20px;
                    letter-spacing: 0.04em;
                    text-align: center;
                  `}
                  onClick={() => {
                    cancelDialog()
                  }}
                >
                  {cancelText.text}
                </a>
              </div>
            </div>
          </div>
          {mainImage.desktopPosition === 'right' && (
            <div
              id="discount-image-right"
              css={css`
                display: flex;
                height: 100%;
                max-width: 50%;
                flex: 0 0 50%;

                ${mobileMediaQuery} {
                  display: none;
                }
              `}
            >
              <img
                id="discountMainImage"
                src={`https://api.intentnow.com/cdn/shopify-images/${mainImage.path}`}
                width="100%"
                height="100%"
                alt=""
                css={css`
                  object-fit: cover;
                `}
              />
            </div>
          )}
          {mainImage.mobilePosition === 'bottom' && (
            <div
              id="discount-image-mobile-bottom"
              css={css`
                display: none;
                height: 100%;
                max-width: 100%;
                width: 100%;
                flex: 0 0 50%;

                ${mobileMediaQuery} {
                  display: flex;
                }
              `}
            >
              <img
                id="discountMainImage"
                src={`https://api.intentnow.com/cdn/shopify-images/${mainImage.path}`}
                alt=""
                css={css`
                  object-fit: cover;
                  height: 260px;
                  width: 100%;
                  object-position: top;
                `}
              />
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export function Widget2Teaser() {
  const { content, settings, teaserVisible, restoreDialog, mobileMediaQuery } =
    usePromoWidgetContext()

  if (!content?.widget2 || !teaserVisible) {
    return <></>
  }

  const mainStyles = content.widget2.teaser.mainStyles
  const position = content.widget2.teaser.position

  return (
    <>
      <Global
        styles={css`
          @keyframes intentnow-slideIn {
            0% {
              transform: translateX(-100%);
              /* Off-screen to the bottom */
            }

            100% {
              transform: translateX(0);
              /* Slide to the normal position */
            }
          }
        `}
      />
      <div
        id="minimize-ui"
        css={css`
          position: ${settings?.previewMode ? 'absolute' : 'fixed'};
          left: ${position?.horizontalGap ?? 0}px;
          bottom: ${position?.verticalGap ?? 0}px;
          min-width: 180px;
          padding: 10px;
          animation: intentnow-slideIn 0.4s ease-in-out forwards;
          ${genColorStyles(mainStyles)}
          min-height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          ${settings?.previewMode ? '' : 'z-index: 90001;'}

          ${mobileMediaQuery} {
            padding: 10px;
            width: auto;
            min-width: 120px;
            min-height: 40px;
          }
        `}
        onClick={() => {
          restoreDialog()
        }}
      >
        <div
          id="minimize-wrapper"
          css={css`
            display: flex;
            flex-direction: column;
            align-items: center;
            //gap: 24px;
            cursor: pointer;
          `}
        >
          {content.widget2.teaser.texts?.map((text: any, index: number) =>
            genTextBlock(text, mainStyles.font, index, content)
          )}
        </div>
      </div>
    </>
  )
}
