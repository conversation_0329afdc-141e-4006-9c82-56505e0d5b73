import {
  defaultWidget2Config,
  defaultWidget3Config,
} from '@packages/shared-entities'
import { IntentnowSettings } from '../entities'

//Store specific settings
export function customizeIntentnowSettings(
  intentnowSettings: IntentnowSettings
) {
  intentnowSettings.delayTimeMs = [
    'quickstart-1d36e8f9.myshopify.com',
    'ilia-beauty-v2.myshopify.com',
  ].includes(intentnowSettings.shop)
    ? 1000
    : 0
  intentnowSettings.hideWelcomeOffer = true
}

//For testing purpose
export function generateMockWidget2Content() {
  return {
    discount: {
      title: '15% Off',
      code: 'ABCDEFGH',
      startsAt: new Date(),
      endsAt: new Date(),
    },
    widget2: defaultWidget2Config,
  }
}

//For testing purpose
export function generateMockWidget3Content() {
  return {
    discount: {
      title: '15% Off',
      code: 'ABCDEFGH',
      startsAt: new Date(),
      endsAt: new Date(),
    },
    widget3: defaultWidget3Config,
  }
}
