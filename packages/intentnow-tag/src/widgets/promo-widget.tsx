'use client'

import React from 'react'
//import { generateStylesForCustomFonts } from './styles'
import {
  PromoWidgetSettings,
  IntentnowSettings,
  PromoWidgetContent,
} from '../entities'
import { GoogleFontConfig } from '@packages/shared-entities'
import { PromoWidgetProvider, usePromoWidgetContext } from './widget-context'
import { Widget2Dialog, Widget2Teaser } from './ui/widget-2'
import { Slide, ToastContainer } from 'react-toastify'
import { css } from '@emotion/react'
import { summarizeFontsFromWidget2Config } from './ui/widget2-helper'
import { summarizeFontsFromWidget3Config } from './ui/widget3-helper'
import { Widget3Dialog, Widget3Teaser } from './ui/widget-3'

export function PromoWidget({
  settings,
  intentnowSettings,
  content,
}: {
  settings?: PromoWidgetSettings
  intentnowSettings?: IntentnowSettings
  content?: PromoWidgetContent
}) {
  return (
    <PromoWidgetProvider>
      <PromoWidgetInner
        settings={settings}
        intentnowSettings={intentnowSettings}
        content={content}
      ></PromoWidgetInner>
    </PromoWidgetProvider>
  )
}

export function PromoWidgetInner({
  settings: initialSettings,
  intentnowSettings: initialIntentnowSettings,
  content: initialContent,
}: {
  settings?: PromoWidgetSettings
  intentnowSettings?: IntentnowSettings
  content?: PromoWidgetContent
}) {
  const {
    settings,
    setSettings,
    setIntentnowSettings,
    content,
    setContent,
    clickBackdrop,
    dialogVisible,
  } = usePromoWidgetContext()

  React.useEffect(() => {
    setSettings(initialSettings)
  }, [initialSettings])

  React.useEffect(() => {
    setIntentnowSettings(initialIntentnowSettings)
  }, [initialIntentnowSettings])

  React.useEffect(() => {
    if (initialContent) {
      setContent(initialContent)
    }
  }, [initialContent])

  const [goolgeFonts, setGoogleFonts] = React.useState<string[]>()
  React.useEffect(() => {
    if (content?.widget2) {
      const { googleFonts } = summarizeFontsFromWidget2Config(
        content.widget2 as any
      )
      setGoogleFonts(
        googleFonts.map((f) => (f.fontConfig as GoogleFontConfig).cssUrl)
      )
    } else if (content?.widget3) {
      const { googleFonts } = summarizeFontsFromWidget3Config(content.widget3)
      setGoogleFonts(
        googleFonts.map((f) => (f.fontConfig as GoogleFontConfig).cssUrl)
      )
    }
  }, [content])

  if (content?.widget2 && content?.widget3) {
    console.error('Widget2 and Widget3 cannot be used together')
    return <></>
  }

  return (
    <>
      {Boolean(goolgeFonts?.length) && (
        <head>
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link
            rel="preconnect"
            href="https://fonts.gstatic.com"
            crossOrigin="anonymous"
          />
          {goolgeFonts?.map((fontHref, index) => (
            <link href={fontHref} rel="stylesheet" key={index} />
          ))}
        </head>
      )}
      {!settings?.previewMode && (
        <ToastContainer
          position="top-center"
          stacked={false}
          autoClose={2000}
          hideProgressBar={true}
          newestOnTop={false}
          closeOnClick={true}
          rtl={false}
          pauseOnFocusLoss
          draggable={false}
          pauseOnHover={false}
          theme="dark"
          transition={Slide}
          icon={() => null}
          toastStyle={{
            fontSize: '15px',
            width: 'auto',
            height: 'auto',
          }}
          style={{
            zIndex: 90002,
            height: 'auto',
          }}
        />
      )}
      {dialogVisible && !settings?.previewMode && (
        <div
          id="intentnow-backdrop"
          css={css`
            display: block;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            ${!settings?.previewMode && 'z-index: 90000;'}
          `}
          onClick={() => {
            clickBackdrop()
          }}
        >
          {/* We need to have some children element so we don't run into the empty-div styles that will cause the backdrop <div> to be hidden. */}
          <div></div>
        </div>
      )}
      {content?.widget3 && (
        <>
          <Widget3Dialog />
          <Widget3Teaser />
        </>
      )}
      {content?.widget2 && (
        <>
          <Widget2Dialog />
          <Widget2Teaser />
        </>
      )}
    </>
  )
}
