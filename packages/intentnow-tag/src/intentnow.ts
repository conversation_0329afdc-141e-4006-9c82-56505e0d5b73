import { trackIntentnowEvent } from './events'
import { getCookies } from './utils'
import {
  IntentnowSessionState,
  IntentnowSettings,
  IntetnowPreviewState,
} from './entities'
import { runWidget } from './widgets'
import { getKlaviyoStateInternal, hideWelcomeOfferInternal } from './detectives'

export interface ShopifyIntentnowSettings {
  shop: string
  appProxySubPath: string
  logging?: boolean
  preview?: boolean
  previewWidetId?: string
  baseApiUrl?: string
}

let intentnowSettings: ShopifyIntentnowSettings | undefined = undefined

export function initializeIntentnow(settings: ShopifyIntentnowSettings) {
  settings.logging && console.debug('intentnowSettings', settings)

  intentnowSettings = settings

  // Test harness
  try {
    let preview: string | null | undefined
    let sessionPreview: IntetnowPreviewState | undefined
    let previewWidgetId: string | undefined
    let clientIdOverride: string | null | undefined

    const queryString = window?.location?.search
    if (queryString) {
      const urlParams = new URLSearchParams(queryString)

      // preview mode
      preview = urlParams.get('intentnow-preview')
      // intentnow-preview-widget-id is deprecated but we keep parsing it for backward compatibility for now
      previewWidgetId =
        urlParams.get('intentnow-preview-widget-id') ?? undefined

      // client id override
      clientIdOverride = urlParams.get('intentnow-client-id')
    }

    if (!preview) {
      //Check preview setting in session storage
      const sessionPreviewBlob = sessionStorage.getItem('intentnow-preview')
      sessionPreview = sessionPreviewBlob
        ? (JSON.parse(sessionPreviewBlob) as IntetnowPreviewState)
        : undefined

      if (sessionPreview) {
        //preview state will expire in 5 minutes
        const duration = Date.now() - (sessionPreview.lastPreviewAt ?? 0)
        if (duration > 5 * 60 * 1000) {
          sessionPreview = undefined
        }
      }
    }

    if (preview) {
      settings.logging && console.debug('intentnow preview on')

      if (
        !previewWidgetId &&
        preview.toLocaleLowerCase() !== 'true' &&
        preview.toLocaleLowerCase() !== 'false'
      ) {
        //The parameter is a widget id
        previewWidgetId = preview
      }

      if (previewWidgetId) {
        intentnowSettings.preview = true
        intentnowSettings.previewWidetId = previewWidgetId
      } else {
        intentnowSettings.preview = preview.toLocaleLowerCase() === 'true'
        intentnowSettings.previewWidetId = undefined
      }
    } else if (sessionPreview) {
      intentnowSettings.preview = true
      intentnowSettings.previewWidetId = sessionPreview.previewWidgetId
    }

    // Save or refresh the preview setting into session storage
    if (intentnowSettings.preview) {
      sessionStorage.setItem(
        'intentnow-preview',
        JSON.stringify({
          lastPreviewAt: Date.now(),
          previewWidgetId: intentnowSettings.previewWidetId,
        } satisfies IntetnowPreviewState)
      )
    } else {
      sessionStorage.removeItem('intentnow-preview')
    }

    // setting clientId from query parameter
    if (clientIdOverride) {
      const cookies = getCookies()
      const clientId = cookies['_shopify_y']
      if (clientId !== clientIdOverride) {
        let domain = window.location.hostname
        if (!domain.endsWith('.myshopify.com') && domain !== 'localhost') {
          const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
          if (!ipv4Regex.test(domain)) {
            const domainParts = domain.split('.')
            if (domainParts.length > 2) {
              domain = domainParts.slice(-2).join('.')
            }
            domain = `.${domain}`
          }
        }

        intentnowSettings.logging &&
          console.debug('overriding clientId', {
            domain,
            clientIdOverride,
          })

        const expiryDate = new Date()
        expiryDate.setTime(expiryDate.getTime() + 365 * 24 * 60 * 60 * 1000) //one year expiry
        const newCookie = `_shopify_y=${clientIdOverride}; path=/; samesite=Lax; domain=${domain}; expires=${expiryDate.toUTCString()}`

        intentnowSettings.logging && console.debug(`set cookie`, newCookie)
        document.cookie = newCookie
      }
    }
  } catch (e) {
    settings.logging && console.error('initializeIntentnow error', e)
  }
}

export async function getIntentnowDiscount() {
  if (!intentnowSettings) {
    console.error('Intentnow settings are not initialized')
    return {}
  }

  intentnowSettings.logging && console.debug('getDiscount called')
  intentnowSettings.logging &&
    console.debug('appProxySubPath', intentnowSettings.appProxySubPath)

  if (!intentnowSettings.appProxySubPath) {
    intentnowSettings.logging && console.error('appProxySubPath is not defined')
    return {}
  }

  const cookies = getCookies()
  const clientId = cookies['_shopify_y']

  if (!clientId) {
    intentnowSettings.logging && console.debug('missing clientId')
    return {}
  }

  const body = {
    clientId,
    preview: intentnowSettings.preview,
  }

  const response = await fetch(
    `/apps/${intentnowSettings.appProxySubPath}/get-discount`,
    {
      method: 'POST',
      body: JSON.stringify(body),
      redirect: 'manual',
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        //To support local testing through ngrok (a https proxy service)
        'ngrok-skip-browser-warning': 'true',
      },
    }
  )

  if (response.ok) {
    const resBody = await response.json()
    intentnowSettings.logging && console.debug('discount response', resBody)
    return resBody
  } else {
    return {}
  }
}

export function getIntentnowSessionState() {
  const sessionStateBlob = sessionStorage.getItem(`intentnow-state`)
  const sessionState = sessionStateBlob
    ? JSON.parse(sessionStateBlob)
    : {
        minimized: false,
        closed: false,
        lastShown: 0,
      }
  return sessionState as IntentnowSessionState
}

export function setIntentnowSessionState(
  sessionState: IntentnowSessionState,
  append = false
) {
  if (append) {
    const existingSessionState = getIntentnowSessionState()
    sessionState = {
      ...existingSessionState,
      ...sessionState,
    }
  }
  sessionStorage.setItem(`intentnow-state`, JSON.stringify(sessionState))
}

export function getKlaviyoState() {
  return getKlaviyoStateInternal(intentnowSettings)
}

export function hideWelcomeOffer() {
  return hideWelcomeOfferInternal(intentnowSettings)
}

export function runIntentnowWidget(elementId: string, isEnabled?: () => true) {
  if (!intentnowSettings) {
    console.error('Intentnow settings are not initialized')
    return
  }

  const settings: IntentnowSettings = {
    shop: intentnowSettings.shop,
    baseApiUrl:
      intentnowSettings.baseApiUrl ??
      `/apps/${intentnowSettings.appProxySubPath}`,
    logging: intentnowSettings.logging,
    preview: intentnowSettings.preview,
    previewWidgetId: intentnowSettings.previewWidetId,
    isEnabled: () => {
      if (isEnabled) {
        return isEnabled()
      } else {
        return true
      }
    },
  }

  runWidget(elementId, settings)
}
