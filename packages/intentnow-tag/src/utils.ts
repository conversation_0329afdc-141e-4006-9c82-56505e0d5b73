export function getCookies() {
  const pairs = document.cookie.split(';')
  const cookies: Record<string, string> = {}
  for (let i = 0; i < pairs.length; i++) {
    const pair = pairs[i].split('=')
    cookies[(pair[0] + '').trim()] = decodeURIComponent(pair.slice(1).join('='))
  }
  return cookies
}

export async function hash(message: string, algorithm = 'SHA-256') {
  // Convert message to Uint8Array if it's a string
  const msgBuffer = new TextEncoder().encode(message)

  // Hash the message
  const hashBuffer = await crypto.subtle.digest(algorithm, msgBuffer)

  // Convert buffer to hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')

  return hashHex
}

export async function sleep(ms: number) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(`slept for ${ms}ms`)
    }, ms)
  })
}
