# IntentNow Tag

This project contains the following elements:

- Promo widget implementation (based on React)
- Events tracking logics that is used by promo widget and also "duplciated" into the web pixel logics

It is used in two different ways:

- It is bundled (by Rollup) into a single script file, uploaded to CDN, and loaded into the Shopify App promo widget extension. The extension only contains minimum footprint and most of the logics are implemented within intentnow-tag
- It is built into a shared library that is used by intentnow-web to support previwing the promo widgets on the Admin Console and Shopify Admin console (merchant facing).

## Testing the promo widget UI

TBD

## Testing with the Shopify app extension

TBD
