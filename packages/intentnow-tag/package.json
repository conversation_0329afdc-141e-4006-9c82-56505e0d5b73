{"name": "@packages/intentnow-tag", "version": "1.0.0", "description": "", "main": "dist/src/index.js", "scripts": {"build": "rimraf dist && tsc", "bundle": "rimraf dist && tsc && rollup --config", "dev-serve": "rimraf dist && tsc && rollup --config --watch --environment ROLLUP_ACTION:serve", "dev-build": "rimraf dist && tsc && rollup --config --environment ROLLUP_ACTION:build", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "typecheck": "tsc --noEmit", "upload": "gcloud storage cp ./dist/scripts/*.js gs://intentnow-cdn/cdn/scripts", "bundle-upload": "pnpm bundle && pnpm upload"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@emotion/react": "^11.14.0", "@measured/puck": "^0.18.3", "@packages/shared-entities": "workspace:^", "react": "^18", "react-dom": "^18", "react-toastify": "^11.0.5", "usehooks-ts": "^3.1.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-react": "^7.26.3", "@emotion/babel-preset-css-prop": "^11.12.0", "@emotion/eslint-plugin": "^11.12.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-typescript": "^12.1.2", "@types/react": "^18", "@types/react-dom": "^18", "dotenv": "^16.4.5", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.37.4", "prettier": "^3.0.0", "prettier-eslint": "^16.3.0", "rimraf": "^6.0.1", "rollup": "^4.35.0", "rollup-plugin-livereload": "^2.0.5", "rollup-plugin-polyfill-node": "^0.13.0", "rollup-plugin-serve": "^3.0.0", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-uglify": "^6.0.4", "tslib": "^2.8.1", "typescript": "^5.8.2"}}