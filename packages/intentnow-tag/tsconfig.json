{"extends": "../../tsconfig.json", "compilerOptions": {"module": "esnext", "moduleResolution": "bundler", "lib": ["dom", "dom.iterable", "esnext"], "esModuleInterop": true, "resolveJsonModule": true, "allowJs": true, "isolatedModules": true, "plugins": [], "importHelpers": false, "jsx": "react-jsx", "jsxImportSource": "@emotion/react", "rootDir": "./", "outDir": "dist", "types": ["@emotion/react/types/css-prop"]}, "include": ["./src/**/*.ts", "src/widgets/index.tsx", "src/widgets/ui/styles.tsx", "src/widgets/widget-context.tsx", "src/widgets/puck/puck-context.tsx"], "exclude": ["node_modules", "dist", ".rollout.cache"]}