import * as dotenv from 'dotenv'
dotenv.config()

import typescript from '@rollup/plugin-typescript'
import { uglify } from 'rollup-plugin-uglify'
import nodeResolve from '@rollup/plugin-node-resolve'
import commonjs from '@rollup/plugin-commonjs'
import babel from '@rollup/plugin-babel'
import replace from '@rollup/plugin-replace'
import { DEFAULT_EXTENSIONS } from '@babel/core'
import serve from 'rollup-plugin-serve'
import styles from 'rollup-plugin-styles'
import nodePolyfills from 'rollup-plugin-polyfill-node'

const localServe = process.env.ROLLUP_ACTION?.toLowerCase() === 'serve'
const localBuild = process.env.ROLLUP_ACTION?.toLowerCase() === 'build'
let env = process.env.APP_ENV ?? 'development'

const isProd = env === 'production'

export default {
  input: './src/bundle-index.ts',
  output: {
    dir: 'dist',
    entryFileNames:
      localBuild || localServe
        ? 'scripts/intentnow-tag-local.js'
        : isProd
          ? `scripts/intentnow-tag-[hash].js`
          : `scripts/intentnow-tag-${env}-[hash].js`,
    format: 'iife',
    name: 'IntentnowTag',
  },
  plugins: [
    nodeResolve(), // requried by react; needs to be before babel
    commonjs(), // requried by react; needs to be before babel
    styles(), // required by @measured/puck
    typescript(),
    babel({
      exclude: 'node_modules/**',
      presets: [
        ['@babel/preset-react', { runtime: 'automatic' }], // requried by react
        '@emotion/babel-preset-css-prop', // requird by @emotion/react
      ],
      babelHelpers: 'bundled',
      extensions: [...DEFAULT_EXTENSIONS, '.tsx'],
      compact: true,
    }),
    nodePolyfills(), // required by @measured/puck to fix the crypto dependency
    replace({
      preventAssignment: false,
      'process.env.NODE_ENV': `"${env}"`,
    }), // requried by react
    ...(isProd ? [uglify()] : []),
    ...(localServe ? [serve()] : []),
  ],
}
