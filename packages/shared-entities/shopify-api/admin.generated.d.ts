/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
import type * as AdminTypes from './admin.types';

export type DiscountCodeBasicCreateMutationVariables = AdminTypes.Exact<{
  basicCodeDiscount: AdminTypes.DiscountCodeBasicInput;
}>;


export type DiscountCodeBasicCreateMutation = { discountCodeBasicCreate?: AdminTypes.Maybe<{ codeDiscountNode?: AdminTypes.Maybe<(
      Pick<AdminTypes.DiscountCodeNode, 'id'>
      & { codeDiscount: (
        Pick<AdminTypes.DiscountCodeBasic, 'title' | 'startsAt' | 'endsAt' | 'appliesOncePerCustomer'>
        & { codes: { nodes: Array<Pick<AdminTypes.DiscountRedeemCode, 'id' | 'code'>> }, shareableUrls: Array<Pick<AdminTypes.DiscountShareableUrl, 'url'>>, customerSelection: Pick<AdminTypes.DiscountCustomerAll, 'allCustomers'>, customerGets: { value: Pick<AdminTypes.DiscountPercentage, 'percentage'>, items: Pick<AdminTypes.AllDiscountItems, 'allItems'> } }
      ) }
    )>, userErrors: Array<Pick<AdminTypes.DiscountUserError, 'field' | 'code' | 'message'>> }> };

export type CheckCodeDiscountByCodeQueryVariables = AdminTypes.Exact<{
  code: AdminTypes.Scalars['String']['input'];
}>;


export type CheckCodeDiscountByCodeQuery = { codeDiscountNodeByCode?: AdminTypes.Maybe<{ codeDiscount: { __typename: 'DiscountCodeApp' | 'DiscountCodeBxgy' | 'DiscountCodeFreeShipping' } | (
      { __typename: 'DiscountCodeBasic' }
      & Pick<AdminTypes.DiscountCodeBasic, 'createdAt' | 'asyncUsageCount' | 'status'>
    ) }> };

export type GetShopInfoQueryVariables = AdminTypes.Exact<{ [key: string]: never; }>;


export type GetShopInfoQuery = { shop: (
    Pick<AdminTypes.Shop, 'id' | 'name' | 'description' | 'url' | 'email' | 'myshopifyDomain' | 'currencyCode'>
    & { plan: Pick<AdminTypes.ShopPlan, 'shopifyPlus'> }
  ) };

export type GetAppInstallationQueryVariables = AdminTypes.Exact<{ [key: string]: never; }>;


export type GetAppInstallationQuery = { currentAppInstallation: (
    Pick<AdminTypes.AppInstallation, 'id'>
    & { app: Pick<AdminTypes.App, 'id' | 'handle' | 'title'> }
  ) };

export type SetMetafildsMutationVariables = AdminTypes.Exact<{
  metafields: Array<AdminTypes.MetafieldsSetInput> | AdminTypes.MetafieldsSetInput;
}>;


export type SetMetafildsMutation = { metafieldsSet?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.MetafieldsSetUserError, 'field' | 'message'>>, metafields?: AdminTypes.Maybe<Array<Pick<AdminTypes.Metafield, 'id' | 'key' | 'namespace' | 'value'>>> }> };

export type GetWebPixelQueryVariables = AdminTypes.Exact<{ [key: string]: never; }>;


export type GetWebPixelQuery = { webPixel?: AdminTypes.Maybe<Pick<AdminTypes.WebPixel, 'id' | 'settings'>> };

export type DeleteWebPixelMutationVariables = AdminTypes.Exact<{
  id: AdminTypes.Scalars['ID']['input'];
}>;


export type DeleteWebPixelMutation = { webPixelDelete?: AdminTypes.Maybe<(
    Pick<AdminTypes.WebPixelDeletePayload, 'deletedWebPixelId'>
    & { userErrors: Array<Pick<AdminTypes.ErrorsWebPixelUserError, 'code' | 'field' | 'message'>> }
  )> };

export type EnableWebPixelMutationVariables = AdminTypes.Exact<{
  input: AdminTypes.WebPixelInput;
}>;


export type EnableWebPixelMutation = { webPixelCreate?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.ErrorsWebPixelUserError, 'code' | 'field' | 'message'>>, webPixel?: AdminTypes.Maybe<Pick<AdminTypes.WebPixel, 'settings' | 'id'>> }> };

export type UpdateWebPixelMutationVariables = AdminTypes.Exact<{
  id: AdminTypes.Scalars['ID']['input'];
  input: AdminTypes.WebPixelInput;
}>;


export type UpdateWebPixelMutation = { webPixelUpdate?: AdminTypes.Maybe<{ userErrors: Array<Pick<AdminTypes.ErrorsWebPixelUserError, 'code' | 'field' | 'message'>>, webPixel?: AdminTypes.Maybe<Pick<AdminTypes.WebPixel, 'id' | 'settings'>> }> };

export type GetIntentnowMetafieldsQueryVariables = AdminTypes.Exact<{
  appInstId: AdminTypes.Scalars['ID']['input'];
}>;


export type GetIntentnowMetafieldsQuery = { appInstallation?: AdminTypes.Maybe<{ metafields: { nodes: Array<Pick<AdminTypes.Metafield, 'id' | 'namespace' | 'key' | 'value'>> } }> };

interface GeneratedQueryTypes {
  "#graphql\n      query checkCodeDiscountByCode($code: String!) {\n        codeDiscountNodeByCode(code: $code) {\n          codeDiscount {\n            __typename\n            ... on DiscountCodeBasic {\n              createdAt\n              asyncUsageCount\n              status\n            }\n          }\n        }\n      }": {return: CheckCodeDiscountByCodeQuery, variables: CheckCodeDiscountByCodeQueryVariables},
  "#graphql\n      query getShopInfo {\n        shop {\n          id\n          name\n          description\n          url\n          email\n          myshopifyDomain\n          plan {\n            shopifyPlus\n          }\n          currencyCode\n        }\n      }": {return: GetShopInfoQuery, variables: GetShopInfoQueryVariables},
  "#graphql\n      query getAppInstallation {\n        currentAppInstallation {\n          id\n          app {\n            id\n            handle\n            title\n          }\n        }\n      }": {return: GetAppInstallationQuery, variables: GetAppInstallationQueryVariables},
  "#graphql\n          query getWebPixel {\n            webPixel {\n              id\n              settings\n            }\n          }": {return: GetWebPixelQuery, variables: GetWebPixelQueryVariables},
  "#graphql\n        query getIntentnowMetafields($appInstId:ID!) {\n          appInstallation(id:$appInstId) {\n            metafields(first: 250, namespace: \"intentnow\") {\n              nodes {\n                id\n                namespace\n                key\n                value\n              }\n            }\n          }\n        }": {return: GetIntentnowMetafieldsQuery, variables: GetIntentnowMetafieldsQueryVariables},
}

interface GeneratedMutationTypes {
  "#graphql\n      mutation discountCodeBasicCreate($basicCodeDiscount: DiscountCodeBasicInput!) {\n        discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {\n          codeDiscountNode {\n            id\n            codeDiscount {\n              ... on DiscountCodeBasic {\n                title\n                codes(first: 10) {\n                  nodes {\n                    id\n                    code\n                  }\n                }\n                shareableUrls {\n                  url\n                }\n                startsAt\n                endsAt\n                customerSelection {\n                  ... on DiscountCustomerAll {\n                    allCustomers\n                  }\n                }\n                customerGets {\n                  value {\n                    ... on DiscountPercentage {\n                      percentage\n                    }\n                  }\n                  items {\n                    ... on AllDiscountItems {\n                      allItems\n                    }\n                  }\n                }\n                appliesOncePerCustomer\n              }\n            }\n          }\n          userErrors {\n            field\n            code\n            message\n          }\n        }\n      }": {return: DiscountCodeBasicCreateMutation, variables: DiscountCodeBasicCreateMutationVariables},
  "#graphql\n      mutation setMetafilds($metafields: [MetafieldsSetInput!]!) {\n        metafieldsSet(metafields: $metafields) {\n          userErrors {\n            field\n            message\n          }\n          metafields {\n            id\n            key\n            namespace\n            value\n          }\n        }\n      }": {return: SetMetafildsMutation, variables: SetMetafildsMutationVariables},
  "#graphql\n        mutation deleteWebPixel($id: ID!) {\n          webPixelDelete(id: $id) {\n            deletedWebPixelId\n            userErrors {\n              code\n              field\n              message\n            }\n          }\n        }": {return: DeleteWebPixelMutation, variables: DeleteWebPixelMutationVariables},
  "#graphql\n        mutation enableWebPixel($input: WebPixelInput!) {\n          webPixelCreate(webPixel: $input) {\n            userErrors {\n              code\n              field\n              message\n            }\n            webPixel {\n              settings\n              id\n            }\n          }\n        }": {return: EnableWebPixelMutation, variables: EnableWebPixelMutationVariables},
  "#graphql\n        mutation updateWebPixel($id: ID!, $input: WebPixelInput!) {\n          webPixelUpdate(id: $id, webPixel: $input) {\n            userErrors {\n              code\n              field\n              message\n            }\n            webPixel {\n              id\n              settings\n            }\n          }\n        }": {return: UpdateWebPixelMutation, variables: UpdateWebPixelMutationVariables},
}
declare module '@shopify/admin-api-client' {
  type InputMaybe<T> = AdminTypes.InputMaybe<T>;
  interface AdminQueries extends GeneratedQueryTypes {}
  interface AdminMutations extends GeneratedMutationTypes {}
}
