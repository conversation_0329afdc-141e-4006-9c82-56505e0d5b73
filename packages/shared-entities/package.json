{"name": "@packages/shared-entities", "version": "1.0.0", "description": "", "private": true, "main": "dist/src/index.js", "scripts": {"build": "rimraf dist && tsc", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "typecheck": "tsc --noEmit"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "prettier": "^3.0.0", "prettier-eslint": "^16.3.0", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "dependencies": {"@nestjs/common": "^11.1.1", "json-schema-to-ts": "^3.1.1"}}