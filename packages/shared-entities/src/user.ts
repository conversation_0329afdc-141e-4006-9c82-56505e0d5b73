export enum AuthType {
  robot = 'robot',
  clerk = 'clerk',
  clerkWebhook = 'clerkWebhook',
  firebase = 'firebase',
  shopifyAdmin = 'shopifyAdmin',
  shopifyProxy = 'shopifyProxy',
}

export enum UserRole {
  admin = 'admin',
  merchant = 'merchant',
}

export interface ShopifyRequestContext {
  shop: string
  appHandle: string
}

export interface UserContext {
  authType: AuthType
  userId: string

  userInfo?: {
    email?: string
    displayName?: string
    imageUrl?: string
  }

  roles?: {
    admin?: boolean
    merchant?: boolean
  }

  shopifyContext?: ShopifyRequestContext

  storeAccesses?: {
    [storeId: string]: boolean
  }
}

export interface ClerkSessionPayload {
  //User ID
  sub: string

  metadata?: {
    //Global roles
    roles?: UserRole[]
    userInfo?: {
      email?: string
      fullName?: string
      imageUrl?: string
    }
  }
}
