export interface GoogleFontConfig {
  cssUrl: string
}

export interface CustomFontConfig {
  fontDisplay: string
  fontFamily: string
  fontStyle?: string
  fontWeight?: string
  fontSources: {
    url: string
    format: string
  }[]
}

export enum WidgetFontType {
  websafe = 'websafe',
  google = 'google',
  custom = 'custom',
}

export interface WidgetFont {
  name: string
  type: WidgetFontType
  fontFamily: string[]

  fontConfig?: GoogleFontConfig | CustomFontConfig
}

export const defaultFonts: WidgetFont[] = [
  // Websafe fonts are defined mainly based on these docs and ChatGPT answers: https://www.w3schools.com/css/css_font_fallbacks.asp, https://www.cssfontstack.com/
  {
    name: 'Arial',
    type: WidgetFontType.websafe,
    fontFamily: ['Arial', 'Helvetica Neue', 'Helvetica', 'sans-serif'],
  },
  {
    name: 'Arial Black',
    type: WidgetFontType.websafe,
    fontFamily: ['Arial Black', 'Arial Bold', 'Gadget', 'sans-serif'],
  },
  {
    name: 'Century Gothic',
    type: WidgetFontType.websafe,
    fontFamily: [
      'Century Gothic',
      'CenturyGothic',
      'AppleGothic',
      'sans-serif',
    ],
  },
  {
    name: 'Comic Sans MS',
    type: WidgetFontType.websafe,
    fontFamily: ['Comic Sans MS', 'cursive', 'sans-serif'],
  },
  {
    name: 'Courier',
    type: WidgetFontType.websafe,
    fontFamily: [
      'Courier',
      'Lucida Sans Typewriter',
      'Lucida Typewriter',
      'monospace',
    ],
  },
  {
    name: 'Courier New',
    type: WidgetFontType.websafe,
    fontFamily: [
      'Courier New',
      'Courier',
      'Lucida Sans Typewriter',
      'Lucida Typewriter',
      'monospace',
    ],
  },
  {
    name: 'Georgia',
    type: WidgetFontType.websafe,
    fontFamily: ['Georgia', 'serif'],
  },
  {
    name: 'Geneva',
    type: WidgetFontType.websafe,
    fontFamily: ['Geneva', 'Tahoma', 'Verdana', 'sans-serif'],
  },
  {
    name: 'Helvetica',
    type: WidgetFontType.websafe,
    fontFamily: ['Helvetica', 'Arial', 'sans-serif'],
  },
  {
    name: 'Lucida',
    type: WidgetFontType.websafe,
    fontFamily: [
      'Lucida Console',
      'Lucida Sans Typewriter',
      'monaco',
      'Bitstream Vera Sans Mono',
      'monospace',
    ],
  },
  {
    name: 'Lucida Grande',
    type: WidgetFontType.websafe,
    fontFamily: [
      'Lucida Grande',
      'Lucida Sans Unicode',
      'Lucida Sans',
      'Geneva',
      'Verdana',
      'sans-serif',
    ],
  },
  {
    name: 'Lucida Sans',
    type: WidgetFontType.websafe,
    fontFamily: [
      'Lucida Sans Typewriter',
      'Lucida Console',
      'monaco',
      'Bitstream Vera Sans Mono',
      'monospace',
    ],
  },
  {
    name: 'MS Serif',
    type: WidgetFontType.websafe,
    fontFamily: ['MS Serif', 'New York', 'Georgia', 'serif'], //based on ChatGPT answer, not necessarily accurate
  },
  {
    name: 'New York',
    type: WidgetFontType.websafe,
    fontFamily: ['New York', 'Georgia', 'Times New Roman', 'serif'], //based on ChatGPT answer, not necessarily accurate
  },
  {
    name: 'Palatino',
    type: WidgetFontType.websafe,
    fontFamily: [
      'Palatino',
      'Palatino Linotype',
      'Palatino LT STD',
      'Book Antiqua',
      'Georgia',
      'serif',
    ],
  },
  {
    name: 'Palatino Linotype',
    type: WidgetFontType.websafe,
    fontFamily: [
      'Palatino Linotype',
      'Book Antiqua',
      'Palatino',
      'Georgia',
      'serif',
    ], //based on ChatGPT answer, not necessarily accurate
  },
  {
    name: 'Tahoma',
    type: WidgetFontType.websafe,
    fontFamily: ['Tahoma', 'Verdana', 'Segoe', 'sans-serif'],
  },
  {
    name: 'Times New Roman',
    type: WidgetFontType.websafe,
    fontFamily: [
      'TimesNewRoman',
      'Times New Roman',
      'Times',
      'Baskerville',
      'Georgia',
      'serif',
    ],
  },
  {
    name: 'Trebuchet MS',
    type: WidgetFontType.websafe,
    fontFamily: [
      'Trebuchet MS',
      'Lucida Grande',
      'Lucida Sans Unicode',
      'Lucida Sans',
      'Tahoma',
      'sans-serif',
    ],
  },
  {
    name: 'Verdana',
    type: WidgetFontType.websafe,
    fontFamily: ['Verdana', 'Geneva', 'sans-serif'],
  },

  // Hand-picked Google Fonts
  {
    name: 'Nunito Sans',
    type: WidgetFontType.google,
    fontFamily: ['Nunito Sans', 'sans-serif'],
    fontConfig: {
      cssUrl:
        'https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000',
    },
  },
  {
    name: 'Poppins',
    type: WidgetFontType.google,
    fontFamily: ['Poppins', 'sans-serif'],
    fontConfig: {
      cssUrl:
        'https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900',
    },
  },
  // Custom fonts for Oofos
  {
    name: 'Helvetica Neue Pro',
    type: WidgetFontType.custom,
    fontFamily: ['Helvetica Neue Pro', 'Arial', 'sans-serif'],
  },
]

export const defaultFontList = defaultFonts.map((font) => font.name).sort()
export const defaultFontMap = new Map(
  defaultFonts.map((font) => [font.name, font])
)
