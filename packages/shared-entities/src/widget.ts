import type { FromSchema, JSONSchema } from 'json-schema-to-ts'
import { defaultFontList } from './font'

export interface DialogContent {
  brandName?: string
  preTitle?: string
  title: string
  mainImage: string
  description: string
  footer: string
  mTitle?: string
}

export interface DiscountContent {
  title: string
  code: string
  link?: string
  startsAt: Date
  endsAt: Date
}

export type Widget3DialogConfig = any //Puck Editor Data
export type Widget3TeaserConfig = any //Puck Editor Data
export interface Widget3Config {
  dialog: Widget3DialogConfig
  teaser: Widget3TeaserConfig
}

export interface PromoWidgetContent {
  discount: DiscountContent
  widget2?: Widget2Config
  widget3?: Widget3Config
}

export enum PromoWidgetAction {
  restoreDialog = 'restore-dialog',
  minimizeDialog = 'minimize-dialog',
  cancelDialog = 'cancel-dialog',
  clickBackdrop = 'click-backdrop',
  copyCode = 'copy-code',
  openUrl = 'open-url',
}

export const legacyWidgetStores = [
  'gourmandbeauty-com.myshopify.com',
  'ilia-beauty-v2.myshopify.com',
]

//TODO: generate json schemas using Zod?

export const widgetBoxJsonSchema = {
  type: 'object',
  properties: {
    paddingTop: {
      type: 'number',
    },
    paddingBottom: {
      type: 'number',
    },
    paddingLeft: {
      type: 'number',
    },
    paddingRight: {
      type: 'number',
    },
  },
} as const satisfies JSONSchema

export const widgetSizeJsonSchema = {
  type: 'object',
  properties: {
    width: {
      type: 'number',
    },
    height: {
      type: 'number',
    },
  },
} as const satisfies JSONSchema

export const widgetFontJsonSchema = {
  type: 'object',
  properties: {
    family: {
      type: 'string',
      format: 'font',
      enum: defaultFontList,
    },
    size: {
      type: 'number',
    },
    weight: {
      type: 'string',
    },
  },
  required: [],
} as const satisfies JSONSchema

export const widgetTextStylesJsonSchema = {
  type: 'object',
  properties: {
    font: widgetFontJsonSchema,
    color: {
      type: 'string',
      format: 'color',
    },
    backgroundColor: {
      type: 'string',
      format: 'color',
    },
    box: widgetBoxJsonSchema,
  },
  required: [],
} as const satisfies JSONSchema

export const widgetTextJsonSchema = {
  type: 'object',
  properties: {
    text: {
      type: 'string',
    },
    styles: widgetTextStylesJsonSchema,
  },
  required: ['text'],
} as const satisfies JSONSchema

export const widgetImageJsonSchema = {
  type: 'object',
  properties: {
    path: {
      type: 'string',
    },
    styles: {
      type: 'object',
      properties: {
        size: widgetSizeJsonSchema,
        box: widgetBoxJsonSchema,
      },
    },
  },
  required: ['path'],
} as const satisfies JSONSchema

export const widgetButtonStylesJsonSchema = {
  type: 'object',
  properties: {
    font: widgetFontJsonSchema,
    color: {
      type: 'string',
      format: 'color',
    },
    backgroundColor: {
      type: 'string',
      format: 'color',
    },
    box: widgetBoxJsonSchema,
    size: widgetSizeJsonSchema,
  },
  required: [],
} as const satisfies JSONSchema

export const widgetButtonJsonSchema = {
  type: 'object',
  properties: {
    text: {
      type: 'string',
    },
    styles: widgetButtonStylesJsonSchema,
  },
  required: ['text'],
} as const satisfies JSONSchema

export const widgetDialogJsonSchema = {
  type: 'object',
  properties: {
    mainStyles: {
      type: 'object',
      properties: {
        font: widgetFontJsonSchema,
        color: {
          type: 'string',
          format: 'color',
        },
        backgroundColor: {
          type: 'string',
          format: 'color',
        },
      },
      required: ['font', 'color', 'backgroundColor'],
    },
    logoImage: widgetImageJsonSchema,
    mainImage: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
        },
        desktopPosition: {
          type: 'string',
          enum: ['left', 'right'],
        },
        mobilePosition: {
          type: 'string',
          enum: ['top', 'bottom'],
        },
      },
      required: ['path', 'desktopPosition'],
    },
    texts: {
      type: 'array',
      items: widgetTextJsonSchema,
    },
    copyCodeBlock: {
      type: 'object',
      properties: {
        mainStyles: {
          type: 'object',
          properties: {
            color: {
              type: 'string',
              format: 'color',
            },
            backgroundColor: {
              type: 'string',
              format: 'color',
            },
          },
        },
        codeTextStyles: widgetTextStylesJsonSchema,
        copyButton: widgetButtonJsonSchema,
        texts: {
          type: 'array',
          items: widgetTextJsonSchema,
        },
        cancelText: widgetTextJsonSchema,
      },
      required: ['cancelText', 'copyButton'],
    },
  },
  required: ['mainStyles', 'mainImage', 'texts', 'copyCodeBlock'],
} as const satisfies JSONSchema

export const widgetTeaserJsonSchema = {
  type: 'object',
  properties: {
    position: {
      type: 'object',
      properties: {
        verticalGap: {
          type: 'number',
        },
        horizontalGap: {
          type: 'number',
        },
      },
    },
    mainStyles: {
      type: 'object',
      properties: {
        font: widgetFontJsonSchema,
        color: {
          type: 'string',
          format: 'color',
        },
        backgroundColor: {
          type: 'string',
          format: 'color',
        },
      },
      required: ['font', 'color', 'backgroundColor'],
    },
    texts: {
      type: 'array',
      items: widgetTextJsonSchema,
    },
  },
  required: ['mainStyles', 'texts'],
} as const satisfies JSONSchema

export const widget2ConfigJsonSchema = {
  type: 'object',
  properties: {
    dialog: widgetDialogJsonSchema,
    teaser: widgetTeaserJsonSchema,
  },
  required: ['dialog', 'teaser'],
} as const satisfies JSONSchema

export type Widget2BoxConfig = FromSchema<typeof widgetBoxJsonSchema>
export type Widget2SizeConfig = FromSchema<typeof widgetSizeJsonSchema>
export type Widget2TextConfig = FromSchema<typeof widgetTextJsonSchema>
export type Widget2FontConfig = FromSchema<typeof widgetFontJsonSchema>
export type Widget2ButtonConfig = FromSchema<typeof widgetButtonJsonSchema>
export type Widget2DialogConfig = FromSchema<typeof widgetDialogJsonSchema>
export type Widget2TeaserConfig = FromSchema<typeof widgetTeaserJsonSchema>
export type Widget2Config = FromSchema<typeof widget2ConfigJsonSchema>

export const widget2DialogUiSchema = {
  type: 'VerticalLayout',
  elements: [
    {
      type: 'Group',
      label: 'Main Styles',
      elements: [
        {
          type: 'Control',
          scope: '#/properties/mainStyles/properties/font',
        },
        {
          type: 'Control',
          scope: '#/properties/mainStyles/properties/color',
        },
        {
          type: 'Control',
          scope: '#/properties/mainStyles/properties/backgroundColor',
        },
      ],
    },
    {
      type: 'Group',
      label: 'Logo Image',
      elements: [
        {
          type: 'Control',
          scope: '#/properties/logoImage/properties/path',
        },
        {
          type: 'Group',
          label: 'Logo Image Styles',
          elements: [
            {
              type: 'Control',
              scope: '#/properties/logoImage/properties/styles/properties/size',
            },
            {
              type: 'Control',
              scope: '#/properties/logoImage/properties/styles/properties/box',
            },
          ],
        },
      ],
    },
    {
      type: 'Group',
      label: 'Main Image',
      elements: [
        {
          type: 'Control',
          scope: '#/properties/mainImage/properties/path',
        },
        {
          type: 'Control',
          scope: '#/properties/mainImage/properties/desktopPosition',
        },
        {
          type: 'Control',
          scope: '#/properties/mainImage/properties/mobilePosition',
        },
      ],
    },
    {
      type: 'Group',
      label: 'Texts',
      elements: [
        {
          type: 'Control',
          scope: '#/properties/texts',
          options: {
            showSortButtons: true, // <-- sortable array
          },
        },
      ],
    },
    {
      type: 'Group',
      label: 'Copy Code Block',
      elements: [
        {
          type: 'Group',
          label: 'Copy Code Block Main Styles',
          elements: [
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/mainStyles/properties/color',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/mainStyles/properties/backgroundColor',
            },
          ],
        },
        {
          type: 'Group',
          label: 'Code Text Styles',
          elements: [
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/codeTextStyles/properties/font',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/codeTextStyles/properties/color',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/codeTextStyles/properties/backgroundColor',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/codeTextStyles/properties/box',
            },
          ],
        },
        {
          type: 'Group',
          label: 'Copy Button',
          elements: [
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/copyButton/properties/text',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/copyButton/properties/styles/properties/font',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/copyButton/properties/styles/properties/color',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/copyButton/properties/styles/properties/backgroundColor',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/copyButton/properties/styles/properties/box',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/copyButton/properties/styles/properties/size',
            },
          ],
        },
        {
          type: 'Group',
          label: 'Copy Code Block Texts',
          elements: [
            {
              type: 'Control',
              scope: '#/properties/copyCodeBlock/properties/texts',
              options: {
                sortable: true, // <-- sortable array
              },
            },
          ],
        },
        {
          type: 'Group',
          label: 'Cancel Text',
          elements: [
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/cancelText/properties/text',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/cancelText/properties/styles/properties/font',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/cancelText/properties/styles/properties/color',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/cancelText/properties/styles/properties/backgroundColor',
            },
            {
              type: 'Control',
              scope:
                '#/properties/copyCodeBlock/properties/cancelText/properties/styles/properties/box',
            },
          ],
        },
      ],
    },
  ],
}

export const widget2TeaserUiSchema = {
  type: 'VerticalLayout',
  elements: [
    {
      label: 'Position',
      type: 'Control',
      scope: '#/properties/position',
    },
    ,
    {
      type: 'Group',
      label: 'Main Styles',
      elements: [
        {
          type: 'Control',
          scope: '#/properties/mainStyles/properties/font',
        },
        {
          type: 'Control',
          scope: '#/properties/mainStyles/properties/color',
        },
        {
          type: 'Control',
          scope: '#/properties/mainStyles/properties/backgroundColor',
        },
      ],
    },
    {
      type: 'Control',
      label: 'Texts',
      scope: '#/properties/texts',
      options: {
        showSortButtons: true,
      },
    },
  ],
}
