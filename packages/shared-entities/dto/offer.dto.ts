import { DocumentDto } from './common.dto';
export declare class OfferWidgetConfigDto {
    type: 'widget3';
    dialog: any;
    teaser: any;
}
export declare class CodeGenConfigDto {
    alphabet: string;
    length: number;
    prefix?: string;
}
export declare class OfferDiscountConfigDto {
    title: string;
    type: 'percent' | 'amount';
    percentOff?: number;
    amountOff?: number;
    minimumOrder: number;
}
export declare class OfferDiscountCodeConfigDto {
    codeGen: CodeGenConfigDto;
    discount: OfferDiscountConfigDto;
    discountDurationInMinutes: number;
    cacheValidDurationInMinutes: number;
}
export declare class StoreOfferDto extends DocumentDto {
    storeId: string;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    status: 'draft' | 'complete';
    widgetConfig?: OfferWidgetConfigDto;
    discountConfig?: OfferDiscountCodeConfigDto;
}
declare const PaginatedStoreOffersDto_base: {
    new (): {
        data: StoreOfferDto[];
        meta: import("./common.dto").PaginationMetaDto;
    };
};
export declare class PaginatedStoreOffersDto extends PaginatedStoreOffersDto_base {
}
export declare class StoreOfferCreateDto {
    name: string;
    widgetConfig?: OfferWidgetConfigDto;
    discountConfig?: OfferDiscountCodeConfigDto;
}
declare const StoreOfferUpdateDto_base: import("@nestjs/common").Type<Partial<StoreOfferCreateDto>>;
export declare class StoreOfferUpdateDto extends StoreOfferUpdateDto_base {
}
export {};
