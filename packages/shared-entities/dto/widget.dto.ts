export declare class DialogContentDto {
    brandName?: string;
    preTitle?: string;
    title: string;
    mainImage: string;
    description: string;
    footer: string;
    mTitle?: string;
}
export declare class DiscountContentDto {
    title: string;
    code: string;
    link?: string;
    startsAt: Date;
    endsAt: Date;
}
export declare class Widget3ConfigDto {
    dialog: any;
    teaser: any;
}
export declare class Widget2ConfigDto {
    dialog: any;
    teaser: any;
}
