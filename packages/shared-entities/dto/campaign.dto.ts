import { DocumentDto } from './common.dto';
export declare enum StoreCampaignStatus {
    active = "active",
    disabled = "disabled",
    notStarted = "notStarted",
    ended = "ended",
    unknown = "unknown"
}
export declare class StoreCampaignVariantDto {
    offerId: string;
    allocation: number;
}
export declare class StoreCampaignScheduleDto {
    start: Date;
    end?: Date;
}
export declare class StoreCampaignDto extends DocumentDto {
    storeId: string;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    enabled: boolean;
    schedule: StoreCampaignScheduleDto;
    variants: StoreCampaignVariantDto[];
    status?: StoreCampaignStatus;
}
declare const PaginatedStoreCampaignsDto_base: {
    new (): {
        data: StoreCampaignDto[];
        meta: import("./common.dto").PaginationMetaDto;
    };
};
export declare class PaginatedStoreCampaignsDto extends PaginatedStoreCampaignsDto_base {
}
export declare class StoreCampaignCreateDto {
    name: string;
    enabled: boolean;
    schedule: StoreCampaignScheduleDto;
    variants: StoreCampaignVariantDto[];
}
declare const StoreCampaignUpdateDto_base: import("@nestjs/common").Type<Partial<StoreCampaignCreateDto>>;
export declare class StoreCampaignUpdateDto extends StoreCampaignUpdateDto_base {
}
export {};
