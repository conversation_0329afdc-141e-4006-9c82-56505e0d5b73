import { DocumentDto } from './common.dto';
export declare class ShopifyAppDto extends DocumentDto {
    createdAt: Date;
    name: string;
    appHandle: string;
    themeAppId: string;
    clientId: string;
    clientSecret: string;
    promoEmbedId: string;
}
export declare class ShopifyAppCreateDto {
    name: string;
    appHandle: string;
    themeAppId: string;
    clientId: string;
    clientSecret: string;
    promoEmbedId: string;
}
declare const ShopifyAppUpdateDto_base: import("@nestjs/common").Type<Partial<ShopifyAppCreateDto>>;
export declare class ShopifyAppUpdateDto extends ShopifyAppUpdateDto_base {
}
declare const PaginatedShopifyAppsDto_base: {
    new (): {
        data: ShopifyAppDto[];
        meta: import("./common.dto").PaginationMetaDto;
    };
};
export declare class PaginatedShopifyAppsDto extends PaginatedShopifyAppsDto_base {
}
export {};
