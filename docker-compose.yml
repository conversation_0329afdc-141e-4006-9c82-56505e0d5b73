services:
  # MongoDB - Primary database
  mongodb:
    image: mongo:7.0
    container_name: intentnow-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: intentnow
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init.js:/docker-entrypoint-initdb.d/init.js:ro

  # Redis - Caching and session storage
  redis:
    image: redis:7.2-alpine
    container_name: intentnow-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local