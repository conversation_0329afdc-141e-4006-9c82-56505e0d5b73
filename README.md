# IntentNow Monorepo

This repo holds the following componnents:

- IntentNow web frontend (intentnow-web)
  - standalone web app
  - Shopify Admin web app
- IntentNow server (intentnow-server)
  - backend for the standalone web app
  - backend for the Shopify Admin web app
  - backend for the events tracking
  - backend for the promo widget
- IntentNow tag script
  - promo widget UI and logics (published to CDN and loaded by the Shopify app extensions)

## VSCode setup

- Open the project from the workspace file: intentnow-monorepo.code-workspace
- Enable "auto-formating" on save
- Strongly recommended VSCode extensions: ESL<PERSON>, Jest, Git<PERSON><PERSON>, "Prettier Code formatter"
- Other recommended VSCode extenstions: Docker, Github Actions

## Local Development

### Prerequisites

- Install Redis (on the default port 6379): https://redis.io/docs/latest/operate/oss_and_stack/install/archive/install-redis/
- Install gcloud CLI: https://cloud.google.com/sdk/docs/install
- Install Caddy (for reverse https proxy): https://caddyserver.com/

### Set up environment variables

- Create an ".env" file under "apps/intentnow-server", and fill in variable values following the examples in ".env.template"
- Create an ".env.local" file under "apps/intentnow-web", and fill in variable values following the examples in ".env.local.template"

Please use either of or both the following Shopify test apps for local development. The URLs settings in them have been set up to point to "locahost" instances:

- intentnow-dev-0
- intentnow-dev-1

Check the following template files for more detailed descriptions of the environment variables

- Server: [.env.template](apps/intentnow-server/.env.template)
- Web: [.env.local.template](apps/intentnow-web/.env.local.template)

### Authenticate with GCP (optional)

If you need to connect to the Firestore DB, you will need to login with GCP first:

```bash
# only need to run once
gcloud init

gcloud auth application-default login

gcloud config set project intentnow-backend
```

### Install dependencies

```bash
pnpm install
```

### Run the standalone web app locally

```bash
pnpm dev
```

- intentnow-web: http://localhost:3000
- intentnow-server: http://localhost:4000

### Run the Shopify Admin web app locally

To run the Shopify Admin web app, an https reverse proxy (e.g., caddy) is needed to make the local web server running on a HTTPS port.

```bash
pnpm shopify:dev
```

- intentnow-web: http://localhost:3000, https://localhost:3001 (https-proxied by caddy)
- intentnow-server: http://localhost:4000, https://locahost:4001 (https-proxied by caddy)

### Testing standalone website

http://localhost:3000

Internal Admin: http://localhost:3000/admin

### Testign standalone APIs

Base API URL: http://localhost:4000

Note-1: we use the Insomnia app (it is a cheaper version of "Postman") to maintain API requests for testing purpose. Ask to get access to the API request collection.

Note-2: only some of the APIs are standalone and can be tested directly (through "curl" or Insomnia). These APIs require either a Firebase Auth token or the configured robot token to access. The other APIs will required Shopify Admin auth token, and can only be tested within the Shopify Admin app.

### Testing Shopify Admin app

Note: it is required to deploy the shopify app configuration and extensions (see below) first before testing the app (either locally or on the cloud).

Test the Shopify App locally using the Shopify test store:

https://admin.shopify.com/store/quickstart-1d36e8f9/

If the test apps are not already installed, install them on the Shopify Partner dashboard:

https://partners.shopify.com/3823936/apps

### Debugging

Two debugger settings have been created in the VSCode projects:

- attach to server (to debug the local server instance)
- Web (to debug the standalone website using Chrome)
- Shopify-Web (to debug the Shopify Admin app (intentnow-dev-0) using Chrome)

### Shared packages

The following projects under the "packages" folder are shared by multiple apps. Please make sure to run "pnpm build" for the changes to be picked up, after making changes to them.

- shared-entities: shared data model definitions (for API or DB)
- intentnow-tag: promo widget rendering and logics; built into both a single script (to be loaded by the Shopify storefront extension) and a shared library to be used by intentnow-web for previewing widgets on the web app.

If you are making changes within either of these folders, you will need to rebuild them for the new changes to be picked up by the apps.

```bash
pnpm --filter @packages/shared-entities build

pnpm --filter @packages/intentnow-tag build
```

Or you can set up the following command in a separate terminal to listen to all the changes under "packages" folder and automatically rebuild it on every new change:

```bash
pnpm watch-packages
```

## GCP Deployment

Build and deploy it using Github Actions workflows:

https://github.com/intentnow/intentnow-monorepo/actions/workflows/build-deploy.yml

## Intentnow Tag script

A "intentnow-tag.js" script is built to be used by the Shopify App extension to run the promo widget logics on merchant's site. See details in the [intentnow-tag package](packages/intentnow-tag)

## Shopify App configurations and extensions

App configurations and extensions are developed in the "intentnow-shopify-app" repo:

https://github.com/intentnow/intentnow-shopify-app
