name: Type Check and Lint

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

concurrency:
  group: type-lint_${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  type-lint:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - uses: pnpm/action-setup@v4
        with:
          version: 10.2.0

      - name: Install Dependencies
        run: pnpm install

      - name: Build shared packages
        run: pnpm --filter @packages/shared-entities build && pnpm --filter @packages/intentnow-tag build

      - name: Run Type Check
        run: pnpm typecheck

      - name: Run ESLint
        run: pnpm lint
