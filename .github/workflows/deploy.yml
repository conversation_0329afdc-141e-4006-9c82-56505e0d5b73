name: Deploy to Cloud Run

on:
  workflow_dispatch:
    inputs:
      service_name: 
        type: choice
        description: 'Service'
        default: 'intentnow-server'
        options:
          - intentnow-server
          - intentnow-web
      deploy_instance:
        type: choice
        description: 'Instance'
        default: 'staging-1'
        options:
          - staging-1
          - staging-2
          - prod-1
      container_name:
        type: choice
        description: 'Container'
        default: 'app'
        options:
          - app
          - otel-collector
      deploy_args:
        type: string
        description: 'Optional deployment arguments'
        required: false
        default: ''
      deploy_args_container:
        type: string
        description: 'Optional deployment arguments for container'
        required: false
        default: ''
  workflow_call:
    inputs:
      service_name:
        type: string
        required: true
      deploy_instance:
        type: string
        required: true
      container_name:
        type: string
        required: false
        default: 'app'
      deploy_args:
        type: string
        required: false
        default: ''
      deploy_args_container:
        type: string
        required: false
        default: ''

concurrency:
  group: deploy_${{ inputs.service_name }}-${{ inputs.deploy_instance }}

env:
  IMAGE_NAME: ${{ inputs.container_name == 'otel-collector' && 'open-telemetry' || inputs.service_name }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    name: "Deploy [${{ inputs.deploy_instance }}, ${{ inputs.service_name }}, ${{ inputs.container_name }}, image=${{ inputs.container_name == 'otel-collector' && 'open-telemetry' || inputs.service_name }}]"

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        with:
            version: 10.2.0

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/intentnow-github-pool/providers/intentnow-github-provider"
          service_account: "<EMAIL>"

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"
        with:
          version: ">= 363.0.0"

      - name: Get the Latest Image Digest
        id: get_digest
        run: |
          DIGEST=$(gcloud artifacts docker tags list \
            us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-${{ env.IMAGE_NAME }}-${{ inputs.deploy_instance }} \
            --format="value(DIGEST)" | head -n 1)
          if [ -z "$DIGEST" ]; then
            echo "Failed to retrieve image digest."
            exit 1
          fi
          echo "Image digest: $DIGEST"
          echo "digest=$DIGEST" >> $GITHUB_OUTPUT

      - name: Deploy to Google Cloud Run
        run: |
          gcloud run deploy ${{ inputs.service_name }}-${{ inputs.deploy_instance }} \
            --region us-central1 \
            ${{ inputs.deploy_args }} \
            --update-labels service="cr-${{ inputs.service_name }}-${{ inputs.deploy_instance }}",env="${{ inputs.deploy_instance }}" \
            --container ${{ inputs.container_name }} \
            --image us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-${{ env.IMAGE_NAME }}-${{ inputs.deploy_instance }}@${{ steps.get_digest.outputs.digest }} \
            ${{ inputs.deploy_args_container }}
          gcloud run services update-traffic ${{ inputs.service_name }}-${{ inputs.deploy_instance }} \
            --region us-central1 --to-latest
