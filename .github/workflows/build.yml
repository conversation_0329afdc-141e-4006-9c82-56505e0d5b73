name: Build Docker Image

on:
  workflow_dispatch:
    inputs:
      service_name: 
        type: choice
        description: 'Service'
        default: 'intentnow-server'
        options:
          - intentnow-server
          - intentnow-web
      deploy_instance:
        type: choice
        description: 'Instance'
        default: 'staging-1'
        options:
          - staging-1
          - staging-2
          - prod-1
  workflow_call:
    inputs:
      service_name:
        type: string
        required: true
      deploy_instance:
        type: string
        required: true

concurrency:
  group: build_${{ inputs.service_name }}-${{ inputs.deploy_instance }}

jobs:
  build-image:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    environment: ${{ inputs.deploy_instance }}
    name: "Build [${{ inputs.deploy_instance }}, ${{ inputs.service_name }}]"

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        with:
          version: 10.2.0

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/intentnow-github-pool/providers/intentnow-github-provider"
          service_account: "<EMAIL>"

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"
        with:
          version: ">= 363.0.0"

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker us-central1-docker.pkg.dev
  
      - name: Build Docker Image
        run: |
          docker build . \
            --target ${{ inputs.service_name }} \
            -f Dockerfile \
            --platform linux/amd64 \
            -t us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-${{ inputs.service_name }}-${{ inputs.deploy_instance}} \
            --build-arg APP_NAME="${{ inputs.service_name }}-${{ inputs.deploy_instance}}" \
            --build-arg APP_ENV="${{ vars.APP_ENV }}" \
            --build-arg GIT_VERSION="${{ github.ref_type }}/${{ github.ref_name }}/${{ github.sha }}" \
            --build-arg CLIENT_API_URL="${{ vars.CLIENT_API_URL }}" \
            --build-arg WEB_URL="${{ vars.WEB_URL }}" \
            --build-arg SHOPIFY_APP_FE_CONFIGS='${{ vars.SHOPIFY_APP_FE_CONFIGS }}' \
            --build-arg STATSIG_CLIENT_KEY="${{ vars.STATSIG_CLIENT_KEY }}" \
            --build-arg FIREBASE_PROJECT_ID="${{ vars.FIREBASE_PROJECT_ID }}" \
            --build-arg FIREBASE_AUTH_DOMAIN="${{ vars.FIREBASE_AUTH_DOMAIN }}" \
            --build-arg FIREBASE_API_KEY="${{ vars.FIREBASE_API_KEY }}" \
            --build-arg FIREBASE_APP_ID="${{ vars.FIREBASE_APP_ID }}" \
            --build-arg INTENTNOW_TAG_VERSION="${{ vars.INTENTNOW_TAG_VERSION }}" \
            --build-arg CLERK_PUBLISHABLE_KEY="${{ vars.CLERK_PUBLISHABLE_KEY }}"

        env:
          DOCKER_BUILDKIT: 1

      - name: Push Service Docker Image
        run: docker push us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-${{ inputs.service_name }}-${{ inputs.deploy_instance}}
