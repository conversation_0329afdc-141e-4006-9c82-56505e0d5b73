name: Build Tag

on:
  workflow_dispatch:
    inputs:
      deploy_instance:
        type: choice
        description: 'Instance'
        default: 'staging-1'
        options:
          - staging-1
          - staging-2
          - prod-1
      update_tag:
        type: boolean
        default: false
        description: 'Update tag version in shopify-app repo?'
  workflow_call:
    inputs:
      deploy_instance:
        type: string
        required: true
      update_tag:
        type: boolean
        default: false
        required: false

run-name: Build Tag [${{ inputs.deploy_instance }}${{ inputs.update_tag && ', update_tag' || '' }}]

concurrency:
  group: build-tag_${{ inputs.deploy_instance }}

jobs:
  build-tag:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    environment: ${{ inputs.deploy_instance }}
    name: "Build Tag [${{ inputs.deploy_instance }}]"

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        with:
          version: 10.2.0

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/intentnow-github-pool/providers/intentnow-github-provider"
          service_account: "<EMAIL>"

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"
        with:
          version: ">= 363.0.0"

      - name: Install dependencies
        run: pnpm install
      
      - name: Build shared packages
        run: pnpm --filter @packages/shared-entities build

      - name: Build and bundle
        run: pnpm --filter @packages/intentnow-tag... bundle
        env:
          APP_ENV: ${{ vars.APP_ENV }}

      - name: Extract tag hash
        id: extract_tag_version
        run: |
          FILE=$(ls ./packages/intentnow-tag/dist/scripts/intentnow-tag-*.js | head -n 1)
          VERSION=$(basename "$FILE" | sed -E 's/intentnow-tag-(.*)\.js/\1/')
          echo "Extracted tag version: $VERSION"
          echo "tag_version=$VERSION" >> "$GITHUB_OUTPUT"
  
      - name: Upload to Cloud Storage [${{ steps.extract_tag_version.outputs.tag_version }}]
        run: |
          gcloud storage cp ./packages/intentnow-tag/dist/scripts/intentnow-tag-${{ steps.extract_tag_version.outputs.tag_version }}.js gs://intentnow-cdn/cdn/scripts

      - name: Update current repo tag version [${{ steps.extract_tag_version.outputs.tag_version }}]
        if: ${{ inputs.update_tag }}
        run: |
          gh variable set INTENTNOW_TAG_VERSION --body "${{ steps.extract_tag_version.outputs.tag_version }}" --repo intentnow/intentnow-monorepo
        env:
          GITHUB_TOKEN: ${{ secrets.INTENTNOW_MONOREPO_GITHUB_TOKEN }}

      - name: Update shopify-app repo tag version [${{ steps.extract_tag_version.outputs.tag_version }}]
        if: ${{ inputs.update_tag }}
        run: |
          gh variable list --repo intentnow/intentnow-shopify-app
          gh variable set INTENTNOW_TAG_VERSION --body "${{ steps.extract_tag_version.outputs.tag_version }}" --repo intentnow/intentnow-shopify-app
          gh variable list --repo intentnow/intentnow-shopify-app
        env:
          GITHUB_TOKEN: ${{ secrets.SHOPIFY_APP_GITHUB_TOKEN }}
