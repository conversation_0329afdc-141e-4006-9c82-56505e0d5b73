name: Build OpenTelemetry Collector

on:
  workflow_dispatch:
    inputs:
      deploy_instance:
        type: choice
        description: 'Instance'
        default: 'staging-1'
        options:
          - staging-1
          - staging-2
          - prod-1
  workflow_call:
    inputs:
      deploy_instance:
        type: string
        required: true

concurrency:
  group: build-otel

jobs:
  build-image:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    name: "Build Otel [${{ inputs.deploy_instance }}]"

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        with:
          version: 10.2.0

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/intentnow-github-pool/providers/intentnow-github-provider"
          service_account: "<EMAIL>"

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"
        with:
          version: ">= 363.0.0"

      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker us-central1-docker.pkg.dev
  
      - name: Build OpenTelemetry Docker Image
        run: |
          docker build . -f Dockerfile-otel \
            -t us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-open-telemetry-${{ inputs.deploy_instance }} 

      - name: Push OpenTelemetry Docker Image
        run: docker push us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-open-telemetry-${{ inputs.deploy_instance }}
