name: Build and Deploy

on:
  workflow_dispatch:
    inputs:
      deploy_instance:
        type: choice
        description: 'Instance'
        default: 'staging-1'
        options:
          - staging-1
          - staging-2
          - prod-1
      do_server:
        type: boolean
        description: 'Server?'
        required: true
        default: true
      do_web:
        type: boolean
        description: 'Web?'
        required: true
        default: true
      do_tag:
        type: boolean
        description: 'Tag?'
        required: true
        default: false
      do_otel:
        type: boolean
        description: 'OpenTelemetry?'
        required: true
        default: false

run-name: Build and Deploy [${{ inputs.deploy_instance }}${{ inputs.do_server && ', server' || '' }}${{ inputs.do_web && ', web' || '' }}${{ inputs.do_tag && ', tag' || '' }}${{ inputs.do_otel && ', otel' || '' }}]

concurrency:
  group: build-deploy_${{ github.workflow }}-${{ github.ref }}-${{ inputs.deploy_instance }}
  cancel-in-progress: true

jobs:
  build-server:
    if: ${{ inputs.do_server }}
    uses: ./.github/workflows/build.yml
    with:
      service_name: intentnow-server
      deploy_instance: ${{ inputs.deploy_instance }}
    secrets: inherit
    permissions:
      contents: "read"
      id-token: "write"
    name: Build Server [${{ inputs.deploy_instance}}]
    
  deploy-server:
    if: ${{ inputs.do_server }}
    needs:  [ build-server ]
    uses: ./.github/workflows/deploy-server-with-vars.yml
    with:
      deploy_instance: ${{ inputs.deploy_instance }}
    secrets: inherit
    permissions:
      contents: "read"
      id-token: "write"
    name: Deploy Server [${{ inputs.deploy_instance }}]

  build-web:
    if: ${{ inputs.do_web }}
    uses: ./.github/workflows/build.yml
    with:
      service_name: intentnow-web
      deploy_instance: ${{ inputs.deploy_instance }}
    secrets: inherit
    permissions:
      contents: "read"
      id-token: "write"
    name: Build Web [${{ inputs.deploy_instance}}]
    
  deploy-web:
    if: ${{ inputs.do_web }}
    needs: [ build-web ]
    uses: ./.github/workflows/deploy-web-with-vars.yml
    with:
      deploy_instance: ${{ inputs.deploy_instance }}
    secrets: inherit
    permissions:
      contents: "read"
      id-token: "write"
    name: Deploy Web [${{ inputs.deploy_instance }}]

  build-otel:
    if: ${{ inputs.do_otel }}
    uses: ./.github/workflows/build-otel.yml
    with:
      deploy_instance: ${{ inputs.deploy_instance }}
    secrets: inherit
    permissions:
      contents: "read"
      id-token: "write"
    name: Build Otel [${{ inputs.deploy_instance}}]

  deploy-otel:
    if: ${{ inputs.do_otel }}
    needs: [ build-otel ]
    uses: ./.github/workflows/deploy.yml
    with:
      service_name: intentnow-server
      deploy_instance: ${{ inputs.deploy_instance }}
      container_name: otel-collector
    secrets: inherit
    permissions:
      contents: "read"
      id-token: "write"
    name: Deploy Otel [${{ inputs.deploy_instance}}]

  build-tag:
    if: ${{ inputs.do_tag }}
    uses: ./.github/workflows/build-tag.yml
    with:
      deploy_instance: ${{ inputs.deploy_instance }}
      update_tag: true
    secrets: inherit
    permissions:
      contents: "read"
      id-token: "write"
    name: Build Tag [${{ inputs.deploy_instance }}]
