name: Deploy Server to Cloud Run

on:
  workflow_dispatch:
    inputs:
      deploy_instance:
        type: choice
        description: 'Instance'
        default: 'staging-1'
        options:
          - staging-1
          - staging-2
          - prod-1
      deploy_args:
        type: string
        description: 'Optional deployment arguments'
        required: false
        default: ''
  workflow_call:
    inputs:
      deploy_instance:
        type: string
        required: true
      deploy_args:
        type: string
        required: false
        default: ''

concurrency:
  group: deploy_intentnow-server-${{ inputs.deploy_instance }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    name: "Deploy Server [${{ inputs.deploy_instance }}]"
    environment: ${{ inputs.deploy_instance }}

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        with:
            version: 10.2.0

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/intentnow-github-pool/providers/intentnow-github-provider"
          service_account: "<EMAIL>"

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"
        with:
          version: ">= 363.0.0"

      - name: Get the Latest Server Image Digest
        id: get_digest
        run: |
          DIGEST=$(gcloud artifacts docker tags list \
            us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-intentnow-server-${{ inputs.deploy_instance }} \
            --format="value(DIGEST)" | head -n 1)
          if [ -z "$DIGEST" ]; then
            echo "Failed to retrieve image digest."
            exit 1
          fi
          echo "Image digest: $DIGEST"
          echo "digest=$DIGEST" >> $GITHUB_OUTPUT

      - name: Deploy to Google Cloud Run
        run: |
          gcloud run deploy intentnow-server-${{ inputs.deploy_instance }} \
            --region us-central1 \
            ${{ inputs.deploy_args }} \
            --update-labels service="cr-intentnow-server-${{ inputs.deploy_instance }}",env="${{ inputs.deploy_instance }}" \
            --container app \
            --depends-on otel-collector \
            --image us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-intentnow-server-${{ inputs.deploy_instance }}@${{ steps.get_digest.outputs.digest }} \
            --set-env-vars '^#^SHOPIFY_APP_CONFIGS=${{ vars.SHOPIFY_APP_CONFIGS }}' \
            --set-env-vars "^#^CLERK_PUBLISHABLE_KEY=${{ vars.CLERK_PUBLISHABLE_KEY }}#\
              CLERK_SECRET_KEY=${{ secrets.CLERK_SECRET_KEY }}#\
              CLERK_JWT_PUBLIC_KEY=${{ vars.CLERK_JWT_PUBLIC_KEY }}#\
              CLERK_WEBHOOK_SIGNING_SECRET=${{ secrets.CLERK_WEBHOOK_SIGNING_SECRET }}" \
            --set-env-vars "^#^\
              SHOPIFY_APP_ACCESS_SCOPES=${{ vars.SHOPIFY_APP_ACCESS_SCOPES }}#\
              SHOPIFY_APP_SERVER_HOST=${{ vars.SHOPIFY_APP_SERVER_HOST }}#\
              SHOPIFY_SESSION_STORAGE=${{ vars.SHOPIFY_SESSION_STORAGE }}#\
              APP_NAME=intentnow-server-${{ inputs.deploy_instance }}#\
              APP_ENV=${{ vars.APP_ENV }}#\
              GCP_PROJECT=${{ vars.GCP_PROJECT }}#\
              AUTH_ROBOT_TOKENS=${{ secrets.AUTH_ROBOT_TOKENS }}#\
              MONGODB_URI=${{ secrets.MONGODB_URI }}#\
              INTENTNOW_EVENT_API=${{ vars.INTENTNOW_EVENT_API }}#\
              INTENTNOW_EVENT_API_TOKEN=${{ vars.INTENTNOW_EVENT_API_TOKEN }}#\
              INTENTNOW_MODEL_API_BASE_URL=${{ vars.INTENTNOW_MODEL_API_BASE_URL }}#\
              INTENTNOW_MODEL_API_TOKEN=${{ secrets.INTENTNOW_MODEL_API_TOKEN }}#\
              INTENTNOW_ANALYTICS_EVENT_VERSION=${{ vars.INTENTNOW_ANALYTICS_EVENT_VERSION }}#\
              IP_API_BASE_URL=${{ vars.IP_API_BASE_URL }}#\
              IP_API_KEY=${{ vars.IP_API_KEY }}#\
              GA_MEASUREMENT_ID=${{ vars.GA_MEASUREMENT_ID }}#\
              GA_API_SECRET=${{ vars.GA_API_SECRET }}#\
              STATSIG_SECRET_KEY=${{ vars.STATSIG_SECRET_KEY }}#\
              STATSIG_CONSOLE_API_TOKEN=${{ vars.STATSIG_CONSOLE_API_TOKEN }}#\
              AMPLITUDE_API_KEY=${{ vars.AMPLITUDE_API_KEY }}#\
              AMPLITUDE_SECRET_KEY=${{ secrets.AMPLITUDE_SECRET_KEY }}#\
              AUTH_USER_EMAILS=${{ vars.AUTH_USER_EMAILS }}#\
              FIRESTORE_DATABASE=${{ vars.FIRESTORE_DATABASE }}#\
              REDIS_URL=${{ vars.REDIS_URL }}#\
              INTENTNOW_EVENT_SHADOW_API=${{ vars.INTENTNOW_EVENT_SHADOW_API }}#\
              STRIPE_SECRET_KEY=${{ secrets.STRIPE_SECRET_KEY }}#\
              LOGGER_LEVEL=info"
            
          gcloud run services update-traffic intentnow-server-${{ inputs.deploy_instance }} \
            --region us-central1 --to-latest