name: Deploy Web to Cloud Run

on:
  workflow_dispatch:
    inputs:
      deploy_instance:
        type: choice
        description: 'Instance'
        default: 'staging-1'
        options:
          - staging-1
          - staging-2
          - prod-1
      deploy_args:
        type: string
        description: 'Optional deployment arguments'
        required: false
        default: ''
  workflow_call:
    inputs:
      deploy_instance:
        type: string
        required: true
      deploy_args:
        type: string
        required: false
        default: ''

concurrency:
  group: deploy_intentnow-web-${{ inputs.deploy_instance }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    name: "Deploy Web [${{ inputs.deploy_instance }}]"
    environment: ${{ inputs.deploy_instance }}

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20

      - uses: pnpm/action-setup@v4
        with:
            version: 10.2.0

      - id: "auth"
        uses: "google-github-actions/auth@v2"
        with:
          workload_identity_provider: "projects/************/locations/global/workloadIdentityPools/intentnow-github-pool/providers/intentnow-github-provider"
          service_account: "<EMAIL>"

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"
        with:
          version: ">= 363.0.0"

      - name: Get the Latest Web Image Digest
        id: get_digest
        run: |
          DIGEST=$(gcloud artifacts docker tags list \
            us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-intentnow-web-${{ inputs.deploy_instance }} \
            --format="value(DIGEST)" | head -n 1)
          if [ -z "$DIGEST" ]; then
            echo "Failed to retrieve image digest."
            exit 1
          fi
          echo "Image digest: $DIGEST"
          echo "digest=$DIGEST" >> $GITHUB_OUTPUT

      - name: Deploy to Google Cloud Run
        run: |
          gcloud run deploy intentnow-web-${{ inputs.deploy_instance }} \
            --region us-central1 \
            ${{ inputs.deploy_args }} \
            --update-labels service="cr-intentnow-web-${{ inputs.deploy_instance }}",env="${{ inputs.deploy_instance }}" \
            --container app \
            --image us-central1-docker.pkg.dev/intentnow-backend/intentnow-builds-repo/dockerimage-intentnow-web-${{ inputs.deploy_instance }}@${{ steps.get_digest.outputs.digest }} \
            --set-env-vars "^#^CLERK_ENCRYPTION_KEY=${{ secrets.CLERK_ENCRYPTION_KEY }}#\
              CLERK_PUBLISHABLE_KEY=${{ vars.CLERK_PUBLISHABLE_KEY }}#\
              CLERK_SECRET_KEY=${{ secrets.CLERK_SECRET_KEY }}"
          gcloud run services update-traffic intentnow-web-${{ inputs.deploy_instance }} \
            --region us-central1 --to-latest