name: Notify Slack

on:
  workflow_dispatch:
    inputs:
      message:
        type: string
        description: 'Message'
        required: true
  workflow_call:
    inputs:
      message:
        type: string
        required: true

jobs:
  notify-slack:
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    name: "Notify Slack"

    steps:
      - name: Notify Slack
        uses: "slackapi/slack-github-action@v2.1.0"
        with:
          webhook: ${{ secrets.SLACK_WEBHOOK_URL }}
          webhook-type: incoming-webhook
          payload: |
            text: "*GitHub Action build result*"
            blocks:
              - type: "section"
                text:
                  type: "mrkdwn"
                  text: "GitHub Action build result block"
