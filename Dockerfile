FROM node:20-slim AS base
ARG APP_ENV=staging
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV NODE_OPTIONS="--max_old_space_size=4096"

ARG APP_NAME
ENV NEXT_PUBLIC_APP_NAME=$APP_NAME

ARG APP_ENV
ENV NEXT_PUBLIC_APP_ENV=$APP_ENV

ARG GIT_VERSION
ENV GIT_VERSION=$GIT_VERSION
ENV NEXT_PUBLIC_GIT_VERSION=$GIT_VERSION

ARG CLIENT_API_URL
ENV NEXT_PUBLIC_CLIENT_API_URL=$CLIENT_API_URL

ARG WEB_URL
ENV NEXT_PUBLIC_WEB_URL=$WEB_URL

ARG SHOPIFY_APP_FE_CONFIGS
ENV NEXT_PUBLIC_SHOPIFY_APP_FE_CONFIGS=$SHOPIFY_APP_FE_CONFIGS

ARG STATSIG_CLIENT_KEY
ENV NEXT_PUBLIC_STATSIG_CLIENT_KEY=$STATSIG_CLIENT_KEY

ARG FIREBASE_PROJECT_ID
ENV NEXT_PUBLIC_FIREBASE_PROJECT_ID=$FIREBASE_PROJECT_ID

ARG FIREBASE_AUTH_DOMAIN
ENV NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=$FIREBASE_AUTH_DOMAIN

ARG FIREBASE_API_KEY
ENV NEXT_PUBLIC_FIREBASE_API_KEY=$FIREBASE_API_KEY

ARG FIREBASE_APP_ID
ENV NEXT_PUBLIC_FIREBASE_APP_ID=$FIREBASE_APP_ID

ARG INTENTNOW_TAG_VERSION
ENV NEXT_PUBLIC_INTENTNOW_TAG_VERSION=$INTENTNOW_TAG_VERSION

ARG CLERK_PUBLISHABLE_KEY
ENV NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$CLERK_PUBLISHABLE_KEY

# RUN corepack enable
RUN npm install -g pnpm@10.2.0
COPY . /app
WORKDIR /app/

FROM base AS intentnow-server
RUN apt-get update
RUN apt-get -yq install ca-certificates
RUN pnpm --filter @apps/intentnow-server... install --frozen-lockfile
RUN pnpm --filter @apps/intentnow-server... build
RUN find . -name "node_modules" -type d | xargs rm -rf
RUN pnpm --filter @apps/intentnow-server... install --prod --frozen-lockfile
RUN echo "GIT_VERSION=$GIT_VERSION" >> /app/apps/intentnow-server/.env
WORKDIR /app/apps/intentnow-server
EXPOSE 4000
CMD [ "pnpm", "start:prod" ]

FROM base AS intentnow-web
RUN apt-get update
RUN apt-get -yq install ca-certificates
RUN pnpm --filter @apps/intentnow-web... install --frozen-lockfile
RUN pnpm --filter @apps/intentnow-web... build
RUN find . -name "node_modules" -type d | xargs rm -rf
RUN pnpm --filter @apps/intentnow-web... install --prod --frozen-lockfile
WORKDIR /app/apps/intentnow-web
EXPOSE 3000
CMD [ "pnpm", "start" ]
